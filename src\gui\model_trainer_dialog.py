# src/gui/model_trainer_dialog.py
import os
import logging
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                              QRadioButton, QButtonGroup, QMessageBox)
from PySide6.QtCore import Qt

# Import the Detectron2 model trainer dialog
from src.gui.detectron2_model_trainer_dialog import Detectron2ModelTrainerDialog
# Import the Model Evaluation dialog
from src.gui.model_evaluation_dialog import ModelEvaluationDialog

logger = logging.getLogger(__name__)

class ModelTrainerDialog(QDialog):
    """Dialog for training object detection and instance segmentation models."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Model Trainer")
        self.setMinimumWidth(500)
        self.setMinimumHeight(300)

        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)

        # Add a label explaining the options
        info_label = QLabel(
            "Choose a model type to train for object detection or instance segmentation. "
            "Detectron2 provides state-of-the-art models for both tasks."
        )
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # Add model type options
        model_group = QHBoxLayout()

        # Faster R-CNN button (object detection)
        self.faster_rcnn_btn = QPushButton("Faster R-CNN\n(Object Detection)")
        self.faster_rcnn_btn.setMinimumHeight(80)
        self.faster_rcnn_btn.clicked.connect(lambda: self.open_trainer("Faster R-CNN"))
        model_group.addWidget(self.faster_rcnn_btn)

        # Mask R-CNN button (instance segmentation)
        self.mask_rcnn_btn = QPushButton("Mask R-CNN\n(Instance Segmentation)")
        self.mask_rcnn_btn.setMinimumHeight(80)
        self.mask_rcnn_btn.clicked.connect(lambda: self.open_trainer("Mask R-CNN"))
        model_group.addWidget(self.mask_rcnn_btn)

        layout.addLayout(model_group)

        # Add Model Evaluation button
        evaluation_group = QHBoxLayout()
        self.model_evaluation_btn = QPushButton("Model Evaluation\n(Training Metrics Analysis)")
        self.model_evaluation_btn.setMinimumHeight(60)
        self.model_evaluation_btn.clicked.connect(self.open_model_evaluation)
        evaluation_group.addWidget(self.model_evaluation_btn)
        layout.addLayout(evaluation_group)

        # Add a description of the models
        description_label = QLabel(
            "<b>Faster R-CNN:</b> Detects objects and provides bounding boxes.<br><br>"
            "<b>Mask R-CNN:</b> Detects objects and provides pixel-level segmentation masks."
        )
        description_label.setWordWrap(True)
        layout.addWidget(description_label)

        # Add a close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.reject)
        layout.addWidget(close_button, alignment=Qt.AlignRight)

    def open_trainer(self, model_type):
        """Open the Detectron2 model trainer dialog with the selected model type."""
        try:
            # Try to import Detectron2 (either from pip or local installation)
            try:
                import detectron2
            except ImportError:
                # Try to import from local path
                import sys
                import os
                detectron2_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'detectron2')
                if os.path.exists(detectron2_path):
                    sys.path.append(os.path.dirname(detectron2_path))
                    import detectron2
                else:
                    raise ImportError("Detectron2 not found in local path")

            # Open the Detectron2 model trainer dialog
            dialog = Detectron2ModelTrainerDialog(self)

            # Set the model type
            if model_type == "Faster R-CNN":
                dialog.model_type_combo.setCurrentText("Faster R-CNN")
                dialog.inference_model_type_combo.setCurrentText("Faster R-CNN")
            else:
                dialog.model_type_combo.setCurrentText("Mask R-CNN")
                dialog.inference_model_type_combo.setCurrentText("Mask R-CNN")

            # Show the dialog
            dialog.exec()

        except ImportError as e:
            # Detectron2 is not installed or dependencies are missing
            QMessageBox.critical(
                self,
                "Detectron2 Not Found",
                f"Error importing Detectron2: {str(e)}\n\n"
                "You have two options:\n\n"
                "1. Install Detectron2 using pip:\n"
                "   pip install detectron2 -f https://dl.fbaipublicfiles.com/detectron2/wheels/cu102/torch1.10/index.html\n\n"
                "2. Make sure all dependencies are installed for the local Detectron2 installation:\n"
                "   - PyTorch (>=1.8)\n"
                "   - torchvision\n"
                "   - opencv-python\n"
                "   - pycocotools\n"
                "   - numpy\n\n"
                "Note: You may need to adjust the CUDA and PyTorch versions based on your system."
            )

    def open_model_evaluation(self):
        """Open the Model Evaluation dialog."""
        try:
            # Create and show the model evaluation dialog
            dialog = ModelEvaluationDialog(self)
            dialog.exec()
        except Exception as e:
            logger.error(f"Error opening model evaluation dialog: {str(e)}")
            QMessageBox.critical(
                self,
                "Error",
                f"Error opening model evaluation dialog: {str(e)}"
            )
