"""
Floating Class Selector Widget for quick class selection in point counting.
"""

from PySide6.QtWidgets import (QWidget, QHBoxLayout, QPushButton, QLabel, 
                              QFrame, QVBoxLayout)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QColor

class ClassButton(QPushButton):
    """A custom button for class selection."""
    
    def __init__(self, class_name, color, index, parent=None):
        super().__init__(parent)
        self.class_name = class_name
        self.color = color
        self.index = index
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the button UI."""
        # Set button text to class name
        self.setText(self.class_name)
        
        # Set button color
        r, g, b = self.color.red(), self.color.green(), self.color.blue()
        
        # Calculate text color based on background brightness
        brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255
        text_color = "white" if brightness < 0.5 else "black"
        
        # Set button style
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: rgb({r}, {g}, {b});
                color: {text_color};
                border: 2px solid #555555;
                border-radius: 5px;
                padding: 5px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: rgb({min(r+20, 255)}, {min(g+20, 255)}, {min(b+20, 255)});
                border: 2px solid #777777;
            }}
            QPushButton:pressed {{
                background-color: rgb({max(r-20, 0)}, {max(g-20, 0)}, {max(b-20, 0)});
            }}
        """)
        
        # Set fixed size
        self.setFixedSize(80, 30)
        
        # Set tooltip
        self.setToolTip(f"Select {self.class_name} (Shortcut: {self.index + 1})")

class FloatingClassSelector(QWidget):
    """A floating widget for quick class selection."""
    
    class_selected = Signal(int)  # Signal emitted when a class is selected
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.classes = []  # List of (class_name, color) tuples
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the UI components."""
        # Set window flags for floating behavior
        self.setWindowFlags(Qt.Tool | Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.main_layout.setSpacing(5)
        
        # Title bar
        title_layout = QHBoxLayout()
        title_label = QLabel("Class Selector")
        title_label.setStyleSheet("color: white; font-weight: bold;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # Close button
        self.close_button = QPushButton("×")
        self.close_button.setFixedSize(20, 20)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #ff5555;
                color: white;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ff7777;
            }
        """)
        self.close_button.clicked.connect(self.hide)
        title_layout.addWidget(self.close_button)
        
        self.main_layout.addLayout(title_layout)
        
        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #555555;")
        self.main_layout.addWidget(separator)
        
        # Container for class buttons
        self.buttons_container = QWidget()
        self.buttons_layout = QHBoxLayout(self.buttons_container)
        self.buttons_layout.setContentsMargins(0, 0, 0, 0)
        self.buttons_layout.setSpacing(5)
        
        self.main_layout.addWidget(self.buttons_container)
        
        # Set widget style
        self.setStyleSheet("""
            FloatingClassSelector {
                background-color: rgba(40, 40, 40, 200);
                border-radius: 10px;
                border: 1px solid #555555;
            }
        """)
    
    def update_classes(self, classes):
        """Update the class buttons.
        
        Args:
            classes: List of (class_name, color) tuples
        """
        self.classes = classes
        
        # Clear existing buttons
        while self.buttons_layout.count():
            item = self.buttons_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # Add new buttons
        for i, (class_name, color) in enumerate(classes):
            button = ClassButton(class_name, color, i)
            button.clicked.connect(lambda checked=False, idx=i: self.class_selected.emit(idx))
            self.buttons_layout.addWidget(button)
        
        # Adjust size based on number of buttons
        self.adjustSize()
    
    def sizeHint(self):
        """Suggest a size for the widget."""
        return QSize(len(self.classes) * 85 + 20, 80)
