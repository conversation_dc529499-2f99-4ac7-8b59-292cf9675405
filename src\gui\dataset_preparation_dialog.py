# src/gui/dataset_preparation_dialog.py
import os
import logging
import random
import shutil
import cv2
import numpy as np
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                              QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox,
                              QComboBox, QCheckBox, QFileDialog, QMessageBox,
                              QTabWidget, QWidget, QProgressBar, QLineEdit)
from PySide6.QtCore import Qt, Signal, Slot, QThread

logger = logging.getLogger(__name__)

class DatasetPreparationDialog(QDialog):
    """Dialog for preparing datasets for training."""

    def __init__(self, parent=None, annotations=None, class_names=None):
        super().__init__(parent)
        self.annotations = annotations or {}  # Dictionary of annotations per image
        self.class_names = class_names or {}  # Dictionary of class names
        self.export_dir = ""
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        self.setWindowTitle("Dataset Preparation")
        self.setMinimumWidth(600)
        self.setMinimumHeight(500)

        main_layout = QVBoxLayout(self)

        # Create tabs
        self.tab_widget = QTabWidget()

        # Splitting tab
        self.splitting_tab = QWidget()
        self.setup_splitting_tab()
        self.tab_widget.addTab(self.splitting_tab, "Dataset Splitting")

        # Augmentation tab
        self.augmentation_tab = QWidget()
        self.setup_augmentation_tab()
        self.tab_widget.addTab(self.augmentation_tab, "Data Augmentation")

        # Export tab
        self.export_tab = QWidget()
        self.setup_export_tab()
        self.tab_widget.addTab(self.export_tab, "Export Format")

        main_layout.addWidget(self.tab_widget)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # Buttons
        buttons_layout = QHBoxLayout()
        self.cancel_button = QPushButton("Cancel")
        self.prepare_button = QPushButton("Prepare Dataset")

        self.cancel_button.clicked.connect(self.reject)
        self.prepare_button.clicked.connect(self.prepare_dataset)

        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.prepare_button)

        main_layout.addLayout(buttons_layout)

    def setup_splitting_tab(self):
        """Set up the dataset splitting tab."""
        layout = QVBoxLayout(self.splitting_tab)

        # Splitting options group
        splitting_group = QGroupBox("Dataset Splitting")
        splitting_layout = QFormLayout(splitting_group)

        # Train/Val/Test split
        self.train_split = QDoubleSpinBox()
        self.train_split.setRange(0.1, 0.9)
        self.train_split.setValue(0.7)
        self.train_split.setSingleStep(0.05)
        splitting_layout.addRow("Training Set Ratio:", self.train_split)

        self.val_split = QDoubleSpinBox()
        self.val_split.setRange(0.0, 0.5)
        self.val_split.setValue(0.2)
        self.val_split.setSingleStep(0.05)
        splitting_layout.addRow("Validation Set Ratio:", self.val_split)

        self.test_split = QDoubleSpinBox()
        self.test_split.setRange(0.0, 0.5)
        self.test_split.setValue(0.1)
        self.test_split.setSingleStep(0.05)
        self.test_split.setEnabled(False)  # Calculated automatically
        splitting_layout.addRow("Test Set Ratio:", self.test_split)

        # Connect signals to update test split
        self.train_split.valueChanged.connect(self.update_test_split)
        self.val_split.valueChanged.connect(self.update_test_split)

        # Random seed
        self.random_seed = QSpinBox()
        self.random_seed.setRange(0, 9999)
        self.random_seed.setValue(42)
        splitting_layout.addRow("Random Seed:", self.random_seed)

        layout.addWidget(splitting_group)
        layout.addStretch()

    def setup_augmentation_tab(self):
        """Set up the data augmentation tab."""
        layout = QVBoxLayout(self.augmentation_tab)

        # Augmentation options group
        augmentation_group = QGroupBox("Augmentation Options")
        augmentation_layout = QFormLayout(augmentation_group)

        # Enable augmentation
        self.enable_augmentation = QCheckBox()
        self.enable_augmentation.setChecked(True)
        augmentation_layout.addRow("Enable Augmentation:", self.enable_augmentation)

        # Number of augmentations per image
        self.augmentations_per_image = QSpinBox()
        self.augmentations_per_image.setRange(1, 10)
        self.augmentations_per_image.setValue(3)
        augmentation_layout.addRow("Augmentations Per Image:", self.augmentations_per_image)

        # Augmentation types
        self.flip_horizontal = QCheckBox("Horizontal Flip")
        self.flip_horizontal.setChecked(True)
        augmentation_layout.addRow("", self.flip_horizontal)

        self.flip_vertical = QCheckBox("Vertical Flip")
        self.flip_vertical.setChecked(False)
        augmentation_layout.addRow("", self.flip_vertical)

        self.rotation = QCheckBox("Rotation")
        self.rotation.setChecked(True)
        augmentation_layout.addRow("", self.rotation)

        self.brightness_contrast = QCheckBox("Brightness/Contrast")
        self.brightness_contrast.setChecked(True)
        augmentation_layout.addRow("", self.brightness_contrast)

        self.noise = QCheckBox("Noise")
        self.noise.setChecked(False)
        augmentation_layout.addRow("", self.noise)

        layout.addWidget(augmentation_group)
        layout.addStretch()

    def setup_export_tab(self):
        """Set up the export format tab."""
        layout = QVBoxLayout(self.export_tab)

        # Export options group
        export_group = QGroupBox("Export Options")
        export_layout = QFormLayout(export_group)

        # Export format
        self.export_format = QComboBox()
        self.export_format.addItems(["COCO JSON", "YOLO Format", "Pascal VOC"])
        export_layout.addRow("Export Format:", self.export_format)

        # Export directory
        export_dir_layout = QHBoxLayout()
        self.export_dir_edit = QLineEdit()
        self.export_dir_edit.setReadOnly(True)
        self.export_dir_button = QPushButton("Browse...")
        self.export_dir_button.clicked.connect(self.browse_export_dir)
        export_dir_layout.addWidget(self.export_dir_edit)
        export_dir_layout.addWidget(self.export_dir_button)
        export_layout.addRow("Export Directory:", export_dir_layout)

        layout.addWidget(export_group)
        layout.addStretch()

    def update_test_split(self):
        """Update the test split based on train and validation splits."""
        train = self.train_split.value()
        val = self.val_split.value()

        # Ensure the total is not more than 1.0
        if train + val > 1.0:
            # Adjust validation split
            val = 1.0 - train
            self.val_split.setValue(val)

        # Calculate test split
        test = 1.0 - train - val
        self.test_split.setValue(test)

    def browse_export_dir(self):
        """Browse for export directory."""
        export_dir = QFileDialog.getExistingDirectory(
            self, "Select Export Directory", os.path.expanduser("~")
        )

        if export_dir:
            self.export_dir = export_dir
            self.export_dir_edit.setText(export_dir)

    def prepare_dataset(self):
        """Prepare the dataset based on the selected options."""
        # Validate inputs
        if not self.annotations:
            QMessageBox.warning(self, "Warning", "No annotations available")
            return

        if not self.export_dir:
            QMessageBox.warning(self, "Warning", "Please select an export directory")
            return

        # Verify export directory exists
        if not os.path.exists(self.export_dir):
            try:
                os.makedirs(self.export_dir, exist_ok=True)
                logger.info(f"Created export directory: {self.export_dir}")
            except Exception as e:
                logger.error(f"Failed to create export directory: {e}")
                QMessageBox.critical(self, "Error", f"Failed to create export directory: {e}")
                return

        # Verify all image paths exist
        valid_image_paths = []
        for image_path in self.annotations.keys():
            if os.path.exists(image_path):
                valid_image_paths.append(image_path)
            else:
                logger.warning(f"Image file not found: {image_path}")

        if not valid_image_paths:
            QMessageBox.warning(self, "Warning", "No valid image files found")
            return

        # Get options
        train_ratio = self.train_split.value()
        val_ratio = self.val_split.value()
        test_ratio = self.test_split.value()
        random_seed = self.random_seed.value()

        enable_augmentation = self.enable_augmentation.isChecked()
        augmentations_per_image = self.augmentations_per_image.value()

        export_format = self.export_format.currentText()

        # Show progress bar
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        try:
            # Split the dataset
            train_images, val_images, test_images = self.split_dataset(
                valid_image_paths, train_ratio, val_ratio, test_ratio, random_seed
            )

            # Create directories
            os.makedirs(os.path.join(self.export_dir, "train", "images"), exist_ok=True)
            os.makedirs(os.path.join(self.export_dir, "train", "labels"), exist_ok=True)
            os.makedirs(os.path.join(self.export_dir, "val", "images"), exist_ok=True)
            os.makedirs(os.path.join(self.export_dir, "val", "labels"), exist_ok=True)

            if test_images:
                os.makedirs(os.path.join(self.export_dir, "test", "images"), exist_ok=True)
                os.makedirs(os.path.join(self.export_dir, "test", "labels"), exist_ok=True)

            # Process training set
            self.progress_bar.setValue(10)
            self.process_image_set(train_images, "train", enable_augmentation, augmentations_per_image, export_format)

            # Process validation set
            self.progress_bar.setValue(60)
            self.process_image_set(val_images, "val", False, 0, export_format)

            # Process test set
            self.progress_bar.setValue(80)
            if test_images:
                self.process_image_set(test_images, "test", False, 0, export_format)

            # For COCO JSON format, write the annotation files
            if export_format == "COCO JSON" and hasattr(self, 'coco_annotations'):
                self.progress_bar.setValue(90)

                # Add info and licenses sections to each COCO JSON file
                for set_name, annotations in self.coco_annotations.items():
                    # Create complete COCO data structure
                    coco_data = {
                        "info": {
                            "description": "VisionLab Ai Annotations",
                            "version": "1.0",
                            "year": 2023,
                            "contributor": "VisionLab Ai",
                            "date_created": ""
                        },
                        "licenses": [
                            {
                                "id": 1,
                                "name": "Unknown",
                                "url": ""
                            }
                        ],
                        "images": annotations['images'],
                        "annotations": annotations['annotations'],
                        "categories": annotations['categories']
                    }

                    # Create annotations directory
                    annotations_dir = os.path.join(self.export_dir, set_name)
                    os.makedirs(annotations_dir, exist_ok=True)

                    # Write COCO JSON file
                    with open(os.path.join(annotations_dir, "annotations.json"), 'w') as f:
                        import json
                        json.dump(coco_data, f, indent=2)

                    logger.info(f"Wrote COCO JSON annotations for {set_name} set")

            self.progress_bar.setValue(100)

            QMessageBox.information(
                self, "Success", f"Dataset prepared successfully in {self.export_dir}"
            )

            self.accept()

        except Exception as e:
            logger.error(f"Error preparing dataset: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error preparing dataset: {str(e)}")
            self.progress_bar.setVisible(False)

    def split_dataset(self, image_paths, train_ratio, val_ratio, test_ratio, random_seed):
        """Split the dataset into training, validation, and test sets."""
        random.seed(random_seed)
        random.shuffle(image_paths)

        total_images = len(image_paths)
        train_count = int(total_images * train_ratio)
        val_count = int(total_images * val_ratio)

        train_images = image_paths[:train_count]
        val_images = image_paths[train_count:train_count + val_count]
        test_images = image_paths[train_count + val_count:]

        return train_images, val_images, test_images

    def process_image_set(self, image_paths, set_name, augment, augmentations_per_image, export_format):
        """Process a set of images (train, val, or test)."""
        for i, image_path in enumerate(image_paths):
            try:
                # Verify image exists
                if not os.path.exists(image_path):
                    logger.warning(f"Image file not found: {image_path}. Skipping.")
                    continue

                # Copy the image
                image_filename = os.path.basename(image_path)
                dest_image_path = os.path.join(self.export_dir, set_name, "images", image_filename)

                # Ensure destination directory exists
                os.makedirs(os.path.dirname(dest_image_path), exist_ok=True)

                # Copy the file
                shutil.copy2(image_path, dest_image_path)
                logger.info(f"Copied image to {dest_image_path}")

                # Export annotations
                annotations = self.annotations.get(image_path, [])
                self.export_annotations(image_path, annotations, set_name, export_format)

                # Augment if needed
                if augment and augmentations_per_image > 0:
                    self.augment_image(image_path, annotations, set_name, augmentations_per_image, export_format)

            except Exception as e:
                logger.error(f"Error processing image {image_path}: {e}")
                # Continue with next image instead of failing the entire process
                continue
            finally:
                # Update progress even if there was an error
                progress = 10 + (i / len(image_paths)) * 50 if set_name == "train" else \
                          60 + (i / len(image_paths)) * 20 if set_name == "val" else \
                          80 + (i / len(image_paths)) * 20
                self.progress_bar.setValue(int(progress))

    def export_annotations(self, image_path, annotations, set_name, export_format):
        """Export annotations in the specified format."""
        try:
            image_filename = os.path.basename(image_path)
            image_name, _ = os.path.splitext(image_filename)

            if export_format == "COCO JSON":
                # For COCO format, we'll collect all annotations and create a single JSON file later
                # Store the annotation data in a temporary attribute
                if not hasattr(self, 'coco_annotations'):
                    self.coco_annotations = {}

                if set_name not in self.coco_annotations:
                    self.coco_annotations[set_name] = {
                        'images': [],
                        'annotations': [],
                        'categories': []
                    }

                # Get image info
                try:
                    img = cv2.imread(image_path)
                    if img is None:
                        logger.error(f"Failed to load image: {image_path}")
                        return

                    img_height, img_width = img.shape[:2]
                except Exception as e:
                    logger.error(f"Error reading image {image_path}: {e}")
                    return

                # Generate a unique image ID
                image_id = len(self.coco_annotations[set_name]['images']) + 1

                # Add image to COCO data
                self.coco_annotations[set_name]['images'].append({
                    'id': image_id,
                    'width': img_width,
                    'height': img_height,
                    'file_name': image_filename,
                    'license': 1,
                    'flickr_url': '',
                    'coco_url': '',
                    'date_captured': ''
                })

                # Process annotations
                annotation_id = len(self.coco_annotations[set_name]['annotations']) + 1

                for annotation in annotations:
                    class_id = annotation.get('class_id', 1)

                    # Make sure the category exists
                    category_exists = False
                    for category in self.coco_annotations[set_name]['categories']:
                        if category['id'] == class_id:
                            category_exists = True
                            break

                    if not category_exists:
                        # Use the actual class name if available, otherwise use a generic name
                        class_name = self.class_names.get(class_id, f'Class_{class_id}')
                        self.coco_annotations[set_name]['categories'].append({
                            'id': class_id,
                            'name': class_name,
                            'supercategory': 'object'
                        })

                    # Create annotation data with default values to ensure required fields exist
                    annotation_data = {
                        'id': annotation_id,
                        'image_id': image_id,
                        'category_id': class_id,
                        'iscrowd': 0,
                        'bbox': [0, 0, 10, 10],  # Default bbox (will be overwritten if available)
                        'segmentation': [[0, 0, 10, 0, 10, 10, 0, 10]],  # Default segmentation
                        'area': 100.0  # Default area
                    }

                    if annotation['type'] == 'rectangle':
                        # Get rectangle points
                        x1, y1 = annotation['points'][0]
                        x2, y2 = annotation['points'][1]

                        # Ensure x1,y1 is top-left and x2,y2 is bottom-right
                        x_min, x_max = min(x1, x2), max(x1, x2)
                        y_min, y_max = min(y1, y2), max(y1, y2)
                        width, height = x_max - x_min, y_max - y_min

                        # Create segmentation (rectangle as polygon)
                        segmentation = [
                            x_min, y_min,
                            x_max, y_min,
                            x_max, y_max,
                            x_min, y_max
                        ]

                        annotation_data.update({
                            'segmentation': [segmentation],
                            'area': width * height,
                            'bbox': [x_min, y_min, width, height]
                        })

                    elif annotation['type'] == 'mask':
                        # Convert mask to polygon
                        mask = annotation['mask']
                        if isinstance(mask, np.ndarray):
                            # Ensure mask is binary and uint8
                            if mask.dtype != np.uint8:
                                mask = (mask > 0).astype(np.uint8)

                            contours, _ = cv2.findContours(
                                mask,
                                cv2.RETR_EXTERNAL,
                                cv2.CHAIN_APPROX_SIMPLE
                            )

                            if contours:
                                # Use the largest contour
                                largest_contour = max(contours, key=cv2.contourArea)

                                # Convert contour to segmentation
                                segmentation = []
                                for point in largest_contour.reshape(-1, 2):
                                    segmentation.extend([float(point[0]), float(point[1])])

                                # Calculate bounding box
                                x, y, w, h = cv2.boundingRect(largest_contour)

                                annotation_data.update({
                                    'segmentation': [segmentation],
                                    'area': cv2.contourArea(largest_contour),
                                    'bbox': [x, y, w, h]
                                })

                    # Ensure all required fields are present before adding to COCO data
                    required_fields = ['bbox', 'segmentation', 'area']
                    missing_fields = [field for field in required_fields if field not in annotation_data]

                    if missing_fields:
                        logger.warning(f"Annotation missing required fields: {missing_fields}. Using default values.")
                        # Add default values for any missing fields
                        if 'bbox' not in annotation_data:
                            annotation_data['bbox'] = [0, 0, 10, 10]
                        if 'segmentation' not in annotation_data:
                            annotation_data['segmentation'] = [[0, 0, 10, 0, 10, 10, 0, 10]]
                        if 'area' not in annotation_data:
                            annotation_data['area'] = 100.0

                    # Add annotation to COCO data
                    self.coco_annotations[set_name]['annotations'].append(annotation_data)
                    annotation_id += 1

            elif export_format == "YOLO Format":
                # YOLO format: class_id x_center y_center width height
                label_path = os.path.join(self.export_dir, set_name, "labels", f"{image_name}.txt")

                # Ensure the labels directory exists
                os.makedirs(os.path.dirname(label_path), exist_ok=True)

                try:
                    img = cv2.imread(image_path)
                    if img is None:
                        logger.error(f"Failed to load image: {image_path}")
                        return

                    img_height, img_width = img.shape[:2]
                except Exception as e:
                    logger.error(f"Error reading image {image_path}: {e}")
                    return

                try:
                    with open(label_path, "w") as f:
                        for annotation in annotations:
                            if annotation["type"] == "rectangle":
                                x1, y1 = annotation["points"][0]
                                x2, y2 = annotation["points"][1]

                                # Convert to YOLO format
                                x_center = (x1 + x2) / 2 / img_width
                                y_center = (y1 + y2) / 2 / img_height
                                width = abs(x2 - x1) / img_width
                                height = abs(y2 - y1) / img_height

                                class_id = annotation.get("class_id", 0)

                                f.write(f"{class_id} {x_center} {y_center} {width} {height}\n")

                            elif annotation["type"] == "mask":
                                # Convert mask to bounding box
                                mask = annotation["mask"]
                                y_indices, x_indices = np.where(mask > 0)

                                if len(y_indices) > 0 and len(x_indices) > 0:
                                    x1, y1 = np.min(x_indices), np.min(y_indices)
                                    x2, y2 = np.max(x_indices), np.max(y_indices)

                                    # Convert to YOLO format
                                    x_center = (x1 + x2) / 2 / img_width
                                    y_center = (y1 + y2) / 2 / img_height
                                    width = (x2 - x1) / img_width
                                    height = (y2 - y1) / img_height

                                    class_id = annotation.get("class_id", 0)

                                    f.write(f"{class_id} {x_center} {y_center} {width} {height}\n")
                except Exception as e:
                    logger.error(f"Error writing YOLO annotations to {label_path}: {e}")

            elif export_format == "Pascal VOC":
                # TODO: Implement Pascal VOC format export
                pass
        except Exception as e:
            logger.error(f"Error exporting annotations for {image_path}: {e}")

    def augment_image(self, image_path, annotations, set_name, count, export_format):
        """Augment an image and its annotations."""
        img = cv2.imread(image_path)
        if img is None:
            logger.error(f"Failed to load image for augmentation: {image_path}")
            return

        image_filename = os.path.basename(image_path)
        image_name, ext = os.path.splitext(image_filename)

        for i in range(count):
            # Apply augmentations
            augmented_img, augmented_annotations = self.apply_augmentations(
                img.copy(), annotations,
                self.flip_horizontal.isChecked(),
                self.flip_vertical.isChecked(),
                self.rotation.isChecked(),
                self.brightness_contrast.isChecked(),
                self.noise.isChecked()
            )

            # Save augmented image
            aug_image_filename = f"{image_name}_aug{i+1}{ext}"
            aug_image_path = os.path.join(self.export_dir, set_name, "images", aug_image_filename)
            cv2.imwrite(aug_image_path, augmented_img)

            # Export augmented annotations
            self.export_annotations(aug_image_path, augmented_annotations, set_name, export_format)

    def apply_augmentations(self, img, annotations, flip_h, flip_v, rotation, brightness, noise):
        """Apply augmentations to an image and its annotations."""
        height, width = img.shape[:2]
        augmented_annotations = []

        # Apply horizontal flip
        if flip_h and random.random() > 0.5:
            img = cv2.flip(img, 1)  # 1 for horizontal flip

            for annotation in annotations:
                if annotation["type"] == "rectangle":
                    x1, y1 = annotation["points"][0]
                    x2, y2 = annotation["points"][1]

                    # Flip x coordinates
                    x1_new = width - x1
                    x2_new = width - x2

                    # Ensure x1 < x2
                    x1, x2 = min(x1_new, x2_new), max(x1_new, x2_new)

                    # Create augmented annotation with all required fields
                    augmented_annotations.append({
                        "type": "rectangle",
                        "points": [(x1, y1), (x2, y2)],
                        "class_id": annotation.get("class_id", 0),
                        "bbox": [min(x1, x2), min(y1, y2), abs(x2-x1), abs(y2-y1)],  # Ensure bbox is present
                        "bbox_mode": annotation.get("bbox_mode", 0)  # Default to XYXY_ABS mode
                    })

                elif annotation["type"] == "mask":
                    mask = annotation["mask"]
                    # Convert boolean mask to uint8 before flipping
                    if mask.dtype == bool:
                        mask_uint8 = mask.astype(np.uint8) * 255
                        flipped_mask = cv2.flip(mask_uint8, 1)
                        # Convert back to boolean if needed
                        flipped_mask = flipped_mask > 0
                    else:
                        flipped_mask = cv2.flip(mask, 1)

                    # Get bounding box from mask
                    y_indices, x_indices = np.where(flipped_mask > 0)
                    if len(y_indices) > 0 and len(x_indices) > 0:
                        x1, y1 = np.min(x_indices), np.min(y_indices)
                        x2, y2 = np.max(x_indices), np.max(y_indices)
                        bbox = [x1, y1, x2-x1, y2-y1]
                    else:
                        # Default bbox if mask is empty
                        bbox = [0, 0, 10, 10]

                    augmented_annotations.append({
                        "type": "mask",
                        "mask": flipped_mask,
                        "class_id": annotation.get("class_id", 0),
                        "bbox": bbox,
                        "bbox_mode": annotation.get("bbox_mode", 0)  # Default to XYXY_ABS mode
                    })

        # Apply vertical flip
        if flip_v and random.random() > 0.5:
            img = cv2.flip(img, 0)  # 0 for vertical flip

            for annotation in annotations:
                if annotation["type"] == "rectangle":
                    x1, y1 = annotation["points"][0]
                    x2, y2 = annotation["points"][1]

                    # Flip y coordinates
                    y1_new = height - y1
                    y2_new = height - y2

                    # Ensure y1 < y2
                    y1, y2 = min(y1_new, y2_new), max(y1_new, y2_new)

                    # Create augmented annotation with all required fields
                    augmented_annotations.append({
                        "type": "rectangle",
                        "points": [(x1, y1), (x2, y2)],
                        "class_id": annotation.get("class_id", 0),
                        "bbox": [min(x1, x2), min(y1, y2), abs(x2-x1), abs(y2-y1)],  # Ensure bbox is present
                        "bbox_mode": annotation.get("bbox_mode", 0)  # Default to XYXY_ABS mode
                    })

                elif annotation["type"] == "mask":
                    mask = annotation["mask"]
                    # Convert boolean mask to uint8 before flipping
                    if mask.dtype == bool:
                        mask_uint8 = mask.astype(np.uint8) * 255
                        flipped_mask = cv2.flip(mask_uint8, 0)
                        # Convert back to boolean if needed
                        flipped_mask = flipped_mask > 0
                    else:
                        flipped_mask = cv2.flip(mask, 0)

                    # Get bounding box from mask
                    y_indices, x_indices = np.where(flipped_mask > 0)
                    if len(y_indices) > 0 and len(x_indices) > 0:
                        x1, y1 = np.min(x_indices), np.min(y_indices)
                        x2, y2 = np.max(x_indices), np.max(y_indices)
                        bbox = [x1, y1, x2-x1, y2-y1]
                    else:
                        # Default bbox if mask is empty
                        bbox = [0, 0, 10, 10]

                    augmented_annotations.append({
                        "type": "mask",
                        "mask": flipped_mask,
                        "class_id": annotation.get("class_id", 0),
                        "bbox": bbox,
                        "bbox_mode": annotation.get("bbox_mode", 0)  # Default to XYXY_ABS mode
                    })

        # Apply rotation (limited to small angles to preserve annotations)
        if rotation and random.random() > 0.5:
            angle = random.uniform(-15, 15)
            center = (width // 2, height // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            img = cv2.warpAffine(img, rotation_matrix, (width, height), flags=cv2.INTER_LINEAR)

            # TODO: Implement rotation for annotations
            # This is complex and requires transforming coordinates or masks
            augmented_annotations = annotations  # For now, just use original annotations

        # Apply brightness/contrast adjustment
        if brightness and random.random() > 0.5:
            alpha = random.uniform(0.8, 1.2)  # Contrast
            beta = random.uniform(-30, 30)    # Brightness
            img = cv2.convertScaleAbs(img, alpha=alpha, beta=beta)

            # Annotations don't change with brightness/contrast
            if not augmented_annotations:
                augmented_annotations = annotations

        # Apply noise
        if noise and random.random() > 0.5:
            noise_type = random.choice(["gaussian", "salt_pepper"])

            if noise_type == "gaussian":
                mean = 0
                stddev = random.uniform(5, 25)
                noise = np.random.normal(mean, stddev, img.shape).astype(np.uint8)
                img = cv2.add(img, noise)

            elif noise_type == "salt_pepper":
                prob = random.uniform(0.01, 0.05)
                noise_mask = np.random.random(img.shape[:2])
                img[noise_mask < prob/2] = 0    # Salt
                img[noise_mask > 1 - prob/2] = 255  # Pepper

            # Annotations don't change with noise
            if not augmented_annotations:
                augmented_annotations = annotations

        # If no augmentations were applied, use original annotations
        if not augmented_annotations:
            augmented_annotations = annotations

        return img, augmented_annotations
