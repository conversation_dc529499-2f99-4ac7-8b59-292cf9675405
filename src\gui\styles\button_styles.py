"""
Button styles for the application.
"""

# Normal button style (dark mode friendly)
NORMAL_BUTTON_STYLE = """
    QPushButton {
        background-color: #3d3d3d;
        color: #ffffff;
        border: 1px solid #555555;
        padding: 5px 10px;
        min-height: 16px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #4d4d4d;
    }
    QPushButton:pressed {
        background-color: #2d2d2d;
    }
    QPushButton:disabled {
        background-color: #2a2a2a;
        color: #707070;
    }
"""

# Active button style (when a tool is active)
ACTIVE_BUTTON_STYLE = """
    QPushButton {
        background-color: #2E7D32;
        color: white;
        border: 1px solid #1B5E20;
        padding: 5px 10px;
        min-height: 16px;
        border-radius: 3px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #388E3C;
    }
    QPushButton:pressed {
        background-color: #1B5E20;
    }
"""

# Toggleable button style
TOGGLEABLE_BUTTON_STYLE = """
    QPushButton {
        background-color: #3d3d3d;
        color: #ffffff;
        border: 1px solid #555555;
        padding: 5px 10px;
        min-height: 16px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #4d4d4d;
    }
    QPushButton:pressed {
        background-color: #2d2d2d;
    }
    QPushButton:checked {
        background-color: #2E7D32;
        color: white;
        border: 1px solid #1B5E20;
        font-weight: bold;
    }
    QPushButton:disabled {
        background-color: #2a2a2a;
        color: #707070;
    }
"""

# SAM tool button style (magic wand, point prompts)
SAM_TOOL_BUTTON_STYLE = """
    QPushButton {
        background-color: #1565C0;
        color: white;
        border: 1px solid #0D47A1;
        padding: 5px 10px;
        min-height: 16px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #1976D2;
    }
    QPushButton:pressed {
        background-color: #0D47A1;
    }
    QPushButton:checked {
        background-color: #0D47A1;
        color: white;
        border: 1px solid #082C6B;
        font-weight: bold;
    }
    QPushButton:disabled {
        background-color: #0A3A7D;
        color: #5C8DBC;
    }
"""

# Positive point prompt button style
POSITIVE_POINT_BUTTON_STYLE = """
    QPushButton {
        background-color: #2E7D32;
        color: white;
        border: 1px solid #1B5E20;
        padding: 5px 10px;
        min-height: 16px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #388E3C;
    }
    QPushButton:pressed {
        background-color: #1B5E20;
    }
    QPushButton:checked {
        background-color: #1B5E20;
        color: white;
        border: 1px solid #0A3F10;
        font-weight: bold;
    }
    QPushButton:disabled {
        background-color: #1A4A1E;
        color: #5BA05F;
    }
"""

# Negative point prompt button style
NEGATIVE_POINT_BUTTON_STYLE = """
    QPushButton {
        background-color: #C62828;
        color: white;
        border: 1px solid #B71C1C;
        padding: 5px 10px;
        min-height: 16px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #D32F2F;
    }
    QPushButton:pressed {
        background-color: #B71C1C;
    }
    QPushButton:checked {
        background-color: #B71C1C;
        color: white;
        border: 1px solid #7F0000;
        font-weight: bold;
    }
    QPushButton:disabled {
        background-color: #7A1919;
        color: #D67373;
    }
"""

# Accept button style
ACCEPT_BUTTON_STYLE = """
    QPushButton {
        background-color: #2E7D32;
        color: white;
        border: 1px solid #1B5E20;
        padding: 5px 10px;
        min-height: 16px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #388E3C;
    }
    QPushButton:pressed {
        background-color: #1B5E20;
    }
    QPushButton:disabled {
        background-color: #1A4A1E;
        color: #5BA05F;
    }
"""

# Reject button style
REJECT_BUTTON_STYLE = """
    QPushButton {
        background-color: #C62828;
        color: white;
        border: 1px solid #B71C1C;
        padding: 5px 10px;
        min-height: 16px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #D32F2F;
    }
    QPushButton:pressed {
        background-color: #B71C1C;
    }
    QPushButton:disabled {
        background-color: #7A1919;
        color: #D67373;
    }
"""

# Clear button style
CLEAR_BUTTON_STYLE = """
    QPushButton {
        background-color: #E65100;
        color: white;
        border: 1px solid #BF360C;
        padding: 5px 10px;
        min-height: 16px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #EF6C00;
    }
    QPushButton:pressed {
        background-color: #BF360C;
    }
    QPushButton:disabled {
        background-color: #8A3004;
        color: #D98B59;
    }
"""
