"""
Interactive histogram widget for the Image Lab page.

This module provides an interactive histogram widget that displays image histograms
and automatically resizes to fit the available space.
"""

import numpy as np
from PySide6.QtWidgets import QWidget, QVBoxLayout, QSizePolicy
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QPainter, QPen, QColor, QBrush, QPainterPath, QLinearGradient

class InteractiveHistogram(QWidget):
    """Interactive histogram widget that displays image histograms."""
    
    def __init__(self, parent=None):
        """Initialize the interactive histogram widget.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Set up widget properties
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(200, 100)
        
        # Initialize histogram data
        self.histogram_data = None
        self.channel_colors = [QColor(255, 0, 0, 180), QColor(0, 255, 0, 180), QColor(0, 0, 255, 180)]
        self.gray_color = QColor(100, 100, 100, 180)
        self.is_color = False
        self.max_value = 0
        
        # Set background color
        self.setAutoFillBackground(True)
        palette = self.palette()
        palette.setColor(self.backgroundRole(), QColor(240, 240, 240))
        self.setPalette(palette)
    
    def sizeHint(self):
        """Return the recommended size for the widget."""
        return QSize(300, 200)
    
    def set_histogram_data(self, image):
        """Set the histogram data from an image.
        
        Args:
            image: OpenCV image (numpy array)
        """
        # Check if the image is grayscale or color
        if len(image.shape) == 2 or image.shape[2] == 1:
            # Grayscale image
            self.is_color = False
            hist = cv2.calcHist([image], [0], None, [256], [0, 256])
            self.histogram_data = [hist.flatten()]
        else:
            # Color image - calculate histogram for each channel
            self.is_color = True
            self.histogram_data = []
            for i in range(3):
                hist = cv2.calcHist([image], [i], None, [256], [0, 256])
                self.histogram_data.append(hist.flatten())
        
        # Find the maximum value for scaling
        self.max_value = max([np.max(hist) for hist in self.histogram_data])
        
        # Trigger a repaint
        self.update()
    
    def paintEvent(self, event):
        """Paint the histogram."""
        if self.histogram_data is None:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Get widget dimensions
        width = self.width()
        height = self.height()
        
        # Draw background
        painter.fillRect(0, 0, width, height, QColor(240, 240, 240))
        
        # Draw border
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        painter.drawRect(0, 0, width - 1, height - 1)
        
        # Draw axes
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.drawLine(10, height - 20, width - 10, height - 20)  # X-axis
        painter.drawLine(10, 10, 10, height - 20)  # Y-axis
        
        # Draw X-axis labels
        painter.setPen(QPen(QColor(50, 50, 50), 1))
        painter.drawText(5, height - 5, "0")
        painter.drawText(width - 25, height - 5, "255")
        
        # Draw title
        if self.is_color:
            title = "RGB Histogram"
        else:
            title = "Grayscale Histogram"
        
        painter.drawText(width // 2 - 40, 15, title)
        
        # Calculate scaling factors
        x_scale = (width - 20) / 256
        
        # Avoid division by zero
        if self.max_value > 0:
            y_scale = (height - 30) / self.max_value
        else:
            y_scale = 1
        
        # Draw histograms
        for i, hist in enumerate(self.histogram_data):
            # Select color
            if self.is_color:
                color = self.channel_colors[i]
            else:
                color = self.gray_color
            
            # Create path for the histogram
            path = QPainterPath()
            path.moveTo(10, height - 20)
            
            # Add points to the path
            for x, value in enumerate(hist):
                x_pos = 10 + x * x_scale
                y_pos = height - 20 - value * y_scale
                path.lineTo(x_pos, y_pos)
            
            # Close the path
            path.lineTo(10 + 255 * x_scale, height - 20)
            path.closeSubpath()
            
            # Draw the filled path
            painter.fillPath(path, QBrush(color))
            
            # Draw the outline
            painter.setPen(QPen(color.darker(120), 1))
            painter.drawPath(path)
        
        # Draw legend if color
        if self.is_color:
            legend_x = width - 80
            legend_y = 30
            
            for i, color in enumerate(self.channel_colors):
                # Draw color box
                painter.fillRect(legend_x, legend_y + i * 20, 15, 15, color)
                painter.setPen(QPen(QColor(50, 50, 50), 1))
                painter.drawRect(legend_x, legend_y + i * 20, 15, 15)
                
                # Draw label
                channel_names = ["R", "G", "B"]
                painter.drawText(legend_x + 20, legend_y + i * 20 + 12, channel_names[i])
        
        painter.end()

# Import OpenCV here to avoid circular imports
import cv2
