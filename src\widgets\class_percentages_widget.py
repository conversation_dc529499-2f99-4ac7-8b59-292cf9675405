"""
Class Percentages Widget for displaying segmentation class distributions.
"""

import numpy as np

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                              QProgressBar, QFrame, QScrollArea, QApplication)
from PySide6.QtGui import QColor, QFont, QPalette
from PySide6.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve, QTimer
import logging

logger = logging.getLogger(__name__)

def get_palette_color(role, group=None, fallback=None):
    """Get a color from the application palette.

    Args:
        role: The QPalette color role to get
        group: The QPalette color group (Active, Disabled, Inactive) or None
        fallback: Fallback color if palette can't be accessed

    Returns:
        str: The color as a hex string
    """
    try:
        app = QApplication.instance()
        if app:
            # Handle the different call signatures correctly
            if group is not None and isinstance(group, QPalette.ColorGroup):
                # If group is a valid ColorGroup, use both parameters
                color = app.palette().color(group, role)
            elif group is not None and not isinstance(group, QPalette.ColorGroup):
                # If group is provided but not a ColorGroup, it's likely a fallback
                # Use the single parameter signature and store the group as fallback
                color = app.palette().color(role)
                fallback = group
            else:
                # If no group is provided, use the single parameter signature
                color = app.palette().color(role)
            return color.name()
    except Exception as e:
        logger.error(f"Error getting palette color: {e}")

    return fallback

class ClassPercentageBar(QWidget):
    """A custom widget that displays a class percentage as a horizontal bar."""

    def __init__(self, label_name, percentage, color, parent=None):
        super().__init__(parent)
        self.label_name = label_name
        self.percentage = percentage
        self.color = color
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI components."""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 2, 0, 2)
        layout.setSpacing(8)

        # Color indicator
        color_indicator = QFrame()
        color_indicator.setFixedSize(16, 16)

        # Handle different color types (QColor or RGB tuple/list)
        if isinstance(self.color, QColor):
            # If it's a QColor object, use it directly
            r, g, b = self.color.red(), self.color.green(), self.color.blue()
            color_str = self.color.name()
        else:
            # If it's a tuple/list of RGB values
            try:
                r, g, b = self.color
                color_str = f"rgb({r}, {g}, {b})"
            except (ValueError, TypeError):
                # Fallback to a default color if there's an issue
                logger.warning(f"Invalid color format: {self.color}, using default")
                r, g, b = 255, 0, 0
                color_str = "rgb(255, 0, 0)"

        # Get border color from palette
        border_color = get_palette_color(QPalette.Mid, fallback="#555555")
        color_indicator.setStyleSheet(f"background-color: {color_str}; border-radius: 8px; border: 1px solid {border_color};")
        layout.addWidget(color_indicator)

        # Label name with fixed width
        self.name_label = QLabel(self.label_name)
        self.name_label.setMinimumWidth(100)
        self.name_label.setMaximumWidth(150)
        self.name_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        font = QFont()
        font.setBold(True)
        self.name_label.setFont(font)

        # Use palette color for text
        text_color = get_palette_color(QPalette.Text, fallback="#FFFFFF")
        self.name_label.setStyleSheet(f"color: {text_color}; background-color: transparent;")

        layout.addWidget(self.name_label)

        # Progress bar for percentage visualization
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)  # Start at 0 for animation
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat(f"{self.percentage:.2f}%")
        self.progress_bar.setMinimumHeight(20)  # Make the bar a bit taller

        # Set text color to white or black depending on background brightness
        brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255
        text_color = "#FFFFFF" if brightness < 0.5 else "#000000"

        # Get colors from palette
        border_color = get_palette_color(QPalette.Mid, fallback="#555555")
        bg_color = get_palette_color(QPalette.Base, fallback="#2D2D2D")

        # Create a complete stylesheet with the text color and palette colors
        style = (
            f"QProgressBar {{ "
            f"border: 1px solid {border_color}; "
            f"border-radius: 5px; "
            f"background-color: {bg_color}; "
            f"text-align: center; "
            f"color: {text_color}; "
            f"}} "
            f"QProgressBar::chunk {{ "
            f"background-color: {color_str}; "
            f"border-radius: 5px; "
            f"}}"
        )
        self.progress_bar.setStyleSheet(style)

        layout.addWidget(self.progress_bar, 1)  # Give the progress bar more space

        # Animate the progress bar
        self.animation = QPropertyAnimation(self.progress_bar, b"value")
        self.animation.setDuration(800)  # Animation duration in ms
        self.animation.setStartValue(0)
        self.animation.setEndValue(int(self.percentage))
        self.animation.setEasingCurve(QEasingCurve.OutCubic)

        # Start animation after a short delay
        QTimer.singleShot(100, self.animation.start)

class ClassPercentagesWidget(QWidget):
    """A widget that displays class percentages with animated bars."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.percentages = {}
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Make the widget transparent to inherit parent background
        # Just add a border radius for styling
        self.setStyleSheet(
            "QWidget { background-color: transparent; border-radius: 8px; }"
        )

        # Title with icon
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(8)

        # Title
        title_label = QLabel("Class Distribution")
        title_label.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setBold(True)
        font.setPointSize(12)
        title_label.setFont(font)

        # Use palette color for text
        text_color = get_palette_color(QPalette.Text, fallback="#FFFFFF")
        title_label.setStyleSheet(f"color: {text_color}; background-color: transparent;")

        title_layout.addWidget(title_label)

        layout.addLayout(title_layout)

        # Separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)

        # Use palette color for separator
        separator_color = get_palette_color(QPalette.Mid, fallback="#555555")
        separator.setStyleSheet(f"background-color: {separator_color};")
        separator.setMaximumHeight(1)
        layout.addWidget(separator)

        # Scroll area for percentage bars
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # Get colors from palette for scrollbar
        scrollbar_bg = get_palette_color(QPalette.Base, fallback="#2D2D2D")
        scrollbar_handle = get_palette_color(QPalette.Mid, fallback="#555555")

        scroll_area.setStyleSheet(
            "QScrollArea { background-color: transparent; border: none; }"
            f"QScrollBar:vertical {{ background-color: {scrollbar_bg}; width: 10px; border-radius: 5px; }}"
            f"QScrollBar::handle:vertical {{ background-color: {scrollbar_handle}; border-radius: 5px; }}"
            "QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical { height: 0px; }"
        )

        # Container for percentage bars
        self.container = QWidget()
        self.container.setStyleSheet("background-color: transparent;")
        self.container_layout = QVBoxLayout(self.container)
        self.container_layout.setContentsMargins(5, 5, 5, 5)
        self.container_layout.setSpacing(8)
        self.container_layout.setAlignment(Qt.AlignTop)

        scroll_area.setWidget(self.container)
        layout.addWidget(scroll_area)

        # Add a placeholder message
        self.placeholder = QLabel("No segmentation result available")
        self.placeholder.setAlignment(Qt.AlignCenter)
        self.placeholder.setWordWrap(True)

        # Use palette color for placeholder text
        placeholder_color = get_palette_color(QPalette.Text, QPalette.Disabled, "#AAAAAA")
        self.placeholder.setStyleSheet(f"color: {placeholder_color}; padding: 20px; background-color: transparent;")

        self.container_layout.addWidget(self.placeholder)

    def update_percentages(self, percentages, colors):
        """Update the displayed percentages.

        Args:
            percentages: Dictionary mapping label names to percentage values
            colors: Dictionary or list mapping label names or indices to RGB color values
        """
        # Clear existing percentage bars
        while self.container_layout.count():
            item = self.container_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        if not percentages:
            # Show placeholder if no percentages
            self.placeholder = QLabel("No segmentation result available")
            self.placeholder.setAlignment(Qt.AlignCenter)
            self.placeholder.setWordWrap(True)

            # Use palette color for placeholder text
            placeholder_color = get_palette_color(QPalette.Text, QPalette.Disabled, "#AAAAAA")
            self.placeholder.setStyleSheet(f"color: {placeholder_color}; padding: 20px; background-color: transparent;")

            self.container_layout.addWidget(self.placeholder)
            return

        # Add summary information
        summary_label = QLabel(f"Total Classes: {len(percentages)}")

        # Use palette color for summary text
        text_color = get_palette_color(QPalette.Text, fallback="#FFFFFF")
        summary_label.setStyleSheet(f"color: {text_color}; font-weight: bold; padding-bottom: 5px; background-color: transparent;")
        self.container_layout.addWidget(summary_label)

        # Add a mini separator
        mini_separator = QFrame()
        mini_separator.setFrameShape(QFrame.HLine)
        mini_separator.setFrameShadow(QFrame.Sunken)

        # Use palette color for separator
        separator_color = get_palette_color(QPalette.Mid, fallback="#333333")
        mini_separator.setStyleSheet(f"background-color: {separator_color};")
        mini_separator.setMaximumHeight(1)
        self.container_layout.addWidget(mini_separator)

        # Create a mapping from label names to colors
        label_to_color = {}

        # If colors is already a dictionary mapping label names to colors, use it directly
        if isinstance(colors, dict) and all(isinstance(k, str) for k in colors.keys()):
            # If colors is already a dictionary with string keys (label names), use it directly
            label_to_color = colors
            logger.debug(f"Using provided label_to_color dictionary directly with {len(colors)} entries")
        # If colors is a list or numpy array, map each label to its corresponding color
        elif isinstance(colors, list) or isinstance(colors, np.ndarray):
            # Get the label names from the percentages dictionary
            label_names = list(percentages.keys())

            # Map each label name to its corresponding color by position
            # This ensures the colors match the order in the dropdown
            for i, label_name in enumerate(sorted(label_names)):
                if i < len(colors):
                    label_to_color[label_name] = colors[i]
                    logger.debug(f"Mapped label '{label_name}' to color {colors[i]} by position")
                else:
                    # Use a default color if we run out of colors
                    label_to_color[label_name] = [255, 0, 0]
                    logger.debug(f"Using default color for label '{label_name}' (out of colors)")
        elif isinstance(colors, dict):
            # If colors is a dictionary with numeric keys, we need to map to label names
            # This is a fallback for backward compatibility
            for label_name in percentages.keys():
                found = False
                for key, color in colors.items():
                    # Try to match the label name to a key in the colors dictionary
                    if str(key) == label_name or (isinstance(key, int) and f"Segment {key}" == label_name):
                        label_to_color[label_name] = color
                        found = True
                        logger.debug(f"Mapped label '{label_name}' to color {color} by key matching")
                        break

                if not found:
                    # Use a default color if no match is found
                    label_to_color[label_name] = [255, 0, 0]
                    logger.debug(f"Using default color for label '{label_name}' (no match found)")

        logger.debug(f"Final label_to_color mapping has {len(label_to_color)} entries")

        # Sort percentages by value (descending)
        sorted_percentages = sorted(
            percentages.items(),
            key=lambda x: x[1],
            reverse=True
        )

        # Add percentage bars for each class
        for label_name, percentage in sorted_percentages:
            # Get color for this label from our mapping
            if label_name in label_to_color:
                color = label_to_color[label_name]
            else:
                # Fallback to default color
                color = [255, 0, 0]

            # Create and add percentage bar
            percentage_bar = ClassPercentageBar(label_name, percentage, color)
            self.container_layout.addWidget(percentage_bar)

        # Add a spacer at the bottom
        self.container_layout.addStretch()

    def sizeHint(self):
        """Suggest a size for the widget."""
        return QSize(300, 300)
