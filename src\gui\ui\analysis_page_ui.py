# src/gui/ui/analysis_page_ui.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QScrollArea, QFrame, QTabWidget, QSlider, QSpinBox, QDoubleSpinBox,
    QFormLayout, QComboBox)
from PySide6.QtCore import Qt

from src.widgets.scrollable_frame import Scrollable<PERSON>rame
from src.widgets.pixmap_view import QPixmapView
from src.widgets.collapsible_section import CollapsibleSection

class AnalysisPageUI:
    """Class for creating and managing the image analysis page UI."""

    def setup_analysis_page(self):
        """Sets up the Analysis page with image processing tools."""
        self.analysis_page = QWidget()
        analysis_layout = QHBoxLayout(self.analysis_page)
        self.stacked_widget.addTab(self.analysis_page, "Image Lab")

        # Left panel for controls
        control_panel = ScrollableFrame()
        control_panel.setFixedWidth(300)
        control_layout = QVBoxLayout(control_panel.get_content_frame())

        # Image Gallery Group
        gallery_group = QGroupBox("Image Gallery")
        gallery_layout = QVBoxLayout(gallery_group)
        gallery_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        gallery_layout.setSpacing(2)  # Reduce spacing between elements

        # Set fixed height for the gallery group
        gallery_group.setFixedHeight(200)

        # Create the analysis gallery
        from src.widgets.analysis_page_gallery import AnalysisPageGallery
        self.analysis_gallery = AnalysisPageGallery()
        self.analysis_gallery.image_clicked.connect(self.on_analysis_gallery_image_clicked)
        gallery_layout.addWidget(self.analysis_gallery)

        control_layout.addWidget(gallery_group)

        # Basic Enhancement Section (Collapsible, collapsed by default)
        enhance_section = CollapsibleSection("Basic Enhancement")
        enhance_section.set_expanded(False)  # Collapsed by default
        enhance_widget = QWidget()
        enhance_layout = QVBoxLayout(enhance_widget)

        self.hist_equal_btn = QPushButton("Histogram Equalization")
        enhance_layout.addWidget(self.hist_equal_btn)

        #Interactive Crop tool
        self.crop_button = QPushButton("Crop Image")
        enhance_layout.addWidget(self.crop_button)

        self.auto_contrast_btn = QPushButton("Auto Contrast")
        enhance_layout.addWidget(self.auto_contrast_btn)

        # Add basic enhancement widgets to enhance section
        enhance_section.add_widget(enhance_widget)
        control_layout.addWidget(enhance_section)

        # Adjustments Section (Collapsible, collapsed by default)
        adjust_section = CollapsibleSection("Adjustments")
        adjust_section.set_expanded(False)  # Collapsed by default
        adjust_widget = QWidget()
        adjust_layout = QFormLayout(adjust_widget)

        # Brightness control
        self.brightness_slider = QSlider(Qt.Horizontal)
        self.brightness_slider.setRange(-100, 100)
        self.brightness_slider.setValue(0)
        self.brightness_value = QLabel("0")
        brightness_layout = QHBoxLayout()
        brightness_layout.addWidget(self.brightness_slider)
        brightness_layout.addWidget(self.brightness_value)
        adjust_layout.addRow("Brightness:", brightness_layout)

        # Contrast control
        self.contrast_slider = QSlider(Qt.Horizontal)
        self.contrast_slider.setRange(-100, 100)
        self.contrast_slider.setValue(0)
        self.contrast_value = QLabel("0")
        contrast_layout = QHBoxLayout()
        contrast_layout.addWidget(self.contrast_slider)
        contrast_layout.addWidget(self.contrast_value)
        adjust_layout.addRow("Contrast:", contrast_layout)

        # Saturation control
        self.saturation_slider = QSlider(Qt.Horizontal)
        self.saturation_slider.setRange(-100, 100)
        self.saturation_slider.setValue(0)
        self.saturation_value = QLabel("0")
        saturation_layout = QHBoxLayout()
        saturation_layout.addWidget(self.saturation_slider)
        saturation_layout.addWidget(self.saturation_value)
        adjust_layout.addRow("Saturation:", saturation_layout)

        # Hue control
        self.hue_slider = QSlider(Qt.Horizontal)
        self.hue_slider.setRange(-180, 180)
        self.hue_slider.setValue(0)
        self.hue_value = QLabel("0")
        hue_layout = QHBoxLayout()
        hue_layout.addWidget(self.hue_slider)
        hue_layout.addWidget(self.hue_value)
        adjust_layout.addRow("Hue:", hue_layout)

        # Sharpness control
        self.sharpness_slider = QSlider(Qt.Horizontal)
        self.sharpness_slider.setRange(0, 100)
        self.sharpness_slider.setValue(0)
        self.sharpness_value = QLabel("0")
        sharpness_layout = QHBoxLayout()
        sharpness_layout.addWidget(self.sharpness_slider)
        sharpness_layout.addWidget(self.sharpness_value)
        adjust_layout.addRow("Sharpness:", sharpness_layout)

        adjust_section.add_widget(adjust_widget)
        control_layout.addWidget(adjust_section)

        # Noise Reduction Section (Collapsible, collapsed by default)
        noise_section = CollapsibleSection("Noise Reduction")
        noise_section.set_expanded(False)  # Collapsed by default
        noise_widget = QWidget()
        noise_layout = QVBoxLayout(noise_widget)

        # Gaussian Blur
        gaussian_layout = QHBoxLayout()
        gaussian_layout.addWidget(QLabel("Gaussian Blur:"))
        self.gaussian_slider = QSlider(Qt.Horizontal)
        self.gaussian_slider.setRange(0, 20)
        self.gaussian_slider.setValue(0)
        self.gaussian_slider.setSingleStep(1)
        self.gaussian_value = QLabel("0")
        gaussian_layout.addWidget(self.gaussian_slider)
        gaussian_layout.addWidget(self.gaussian_value)
        noise_layout.addLayout(gaussian_layout)

        # Median Filter
        median_layout = QHBoxLayout()
        median_layout.addWidget(QLabel("Median Filter:"))
        self.median_slider = QSlider(Qt.Horizontal)
        self.median_slider.setRange(0, 10)
        self.median_slider.setValue(0)
        self.median_slider.setSingleStep(1)
        self.median_value = QLabel("0")
        median_layout.addWidget(self.median_slider)
        median_layout.addWidget(self.median_value)
        noise_layout.addLayout(median_layout)

        # Apply Noise Reduction Button
        self.apply_noise_btn = QPushButton("Apply Noise Reduction")
        noise_layout.addWidget(self.apply_noise_btn)

        noise_section.add_widget(noise_widget)
        control_layout.addWidget(noise_section)

        # Thresholding Section (Collapsible, collapsed by default)
        threshold_section = CollapsibleSection("Thresholding")
        threshold_section.set_expanded(False)  # Collapsed by default
        threshold_widget = QWidget()
        threshold_layout = QVBoxLayout(threshold_widget)

        # Threshold Method
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("Method:"))
        self.threshold_method_combo = QComboBox()
        self.threshold_method_combo.addItems(["Binary", "Binary Inverted", "Truncate", "To Zero",
                                           "To Zero Inverted", "Otsu", "Adaptive Mean", "Adaptive Gaussian"])
        method_layout.addWidget(self.threshold_method_combo)
        threshold_layout.addLayout(method_layout)

        # Threshold Value
        threshold_value_layout = QHBoxLayout()
        threshold_value_layout.addWidget(QLabel("Threshold:"))
        self.threshold_slider = QSlider(Qt.Horizontal)
        self.threshold_slider.setRange(0, 255)
        self.threshold_slider.setValue(127)
        self.threshold_value_label = QLabel("127")
        threshold_value_layout.addWidget(self.threshold_slider)
        threshold_value_layout.addWidget(self.threshold_value_label)
        threshold_layout.addLayout(threshold_value_layout)

        # Adaptive Parameters (for adaptive thresholding)
        adaptive_layout = QVBoxLayout()

        # Block Size
        block_size_layout = QHBoxLayout()
        block_size_layout.addWidget(QLabel("Block Size:"))
        self.adaptive_block_size_slider = QSlider(Qt.Horizontal)
        self.adaptive_block_size_slider.setRange(1, 20)  # Will be multiplied by 2 and add 3
        self.adaptive_block_size_slider.setValue(5)
        self.adaptive_block_size_label = QLabel("5")
        block_size_layout.addWidget(self.adaptive_block_size_slider)
        block_size_layout.addWidget(self.adaptive_block_size_label)
        adaptive_layout.addLayout(block_size_layout)

        # C Value
        c_value_layout = QHBoxLayout()
        c_value_layout.addWidget(QLabel("C Value:"))
        self.adaptive_c_slider = QSlider(Qt.Horizontal)
        self.adaptive_c_slider.setRange(-10, 30)
        self.adaptive_c_slider.setValue(2)
        self.adaptive_c_label = QLabel("2")
        c_value_layout.addWidget(self.adaptive_c_slider)
        c_value_layout.addWidget(self.adaptive_c_label)
        adaptive_layout.addLayout(c_value_layout)

        threshold_layout.addLayout(adaptive_layout)

        # Apply Threshold Button
        self.apply_threshold_btn = QPushButton("Apply Threshold")
        threshold_layout.addWidget(self.apply_threshold_btn)

        threshold_section.add_widget(threshold_widget)
        control_layout.addWidget(threshold_section)

        # Edge Detection Section (Collapsible, collapsed by default)
        edge_section = CollapsibleSection("Edge Detection")
        edge_section.set_expanded(False)  # Collapsed by default
        edge_widget = QWidget()
        edge_layout = QVBoxLayout(edge_widget)

        # Edge Detection Method
        edge_method_layout = QHBoxLayout()
        edge_method_layout.addWidget(QLabel("Method:"))
        self.edge_method_combo = QComboBox()
        self.edge_method_combo.addItems(["Sobel", "Canny", "Laplacian"])
        edge_method_layout.addWidget(self.edge_method_combo)
        edge_layout.addLayout(edge_method_layout)

        # Canny Parameters
        canny_layout = QVBoxLayout()

        # Low Threshold
        low_threshold_layout = QHBoxLayout()
        low_threshold_layout.addWidget(QLabel("Low Threshold:"))
        self.canny_low_threshold_slider = QSlider(Qt.Horizontal)
        self.canny_low_threshold_slider.setRange(0, 255)
        self.canny_low_threshold_slider.setValue(50)
        self.canny_low_threshold_label = QLabel("50")
        low_threshold_layout.addWidget(self.canny_low_threshold_slider)
        low_threshold_layout.addWidget(self.canny_low_threshold_label)
        canny_layout.addLayout(low_threshold_layout)

        # High Threshold
        high_threshold_layout = QHBoxLayout()
        high_threshold_layout.addWidget(QLabel("High Threshold:"))
        self.canny_high_threshold_slider = QSlider(Qt.Horizontal)
        self.canny_high_threshold_slider.setRange(0, 255)
        self.canny_high_threshold_slider.setValue(150)
        self.canny_high_threshold_label = QLabel("150")
        high_threshold_layout.addWidget(self.canny_high_threshold_slider)
        high_threshold_layout.addWidget(self.canny_high_threshold_label)
        canny_layout.addLayout(high_threshold_layout)

        edge_layout.addLayout(canny_layout)

        # Apply Edge Detection Button
        self.apply_edge_btn = QPushButton("Apply Edge Detection")
        edge_layout.addWidget(self.apply_edge_btn)

        edge_section.add_widget(edge_widget)
        control_layout.addWidget(edge_section)

        # Morphological Operations Section (Collapsible, collapsed by default)
        morph_section = CollapsibleSection("Morphological Operations")
        morph_section.set_expanded(False)  # Collapsed by default
        morph_widget = QWidget()
        morph_layout = QVBoxLayout(morph_widget)

        # Morphological Operation Method
        morph_method_layout = QHBoxLayout()
        morph_method_layout.addWidget(QLabel("Operation:"))
        self.morph_op_combo = QComboBox()
        self.morph_op_combo.addItems(["Erosion", "Dilation", "Opening", "Closing", "Gradient", "Top Hat", "Black Hat"])
        morph_method_layout.addWidget(self.morph_op_combo)
        morph_layout.addLayout(morph_method_layout)

        # Kernel Size
        kernel_size_layout = QHBoxLayout()
        kernel_size_layout.addWidget(QLabel("Kernel Size:"))
        self.morph_kernel_size_slider = QSlider(Qt.Horizontal)
        self.morph_kernel_size_slider.setRange(1, 10)  # Will be multiplied by 2 and add 1
        self.morph_kernel_size_slider.setValue(1)
        self.morph_kernel_size_label = QLabel("1")
        kernel_size_layout.addWidget(self.morph_kernel_size_slider)
        kernel_size_layout.addWidget(self.morph_kernel_size_label)
        morph_layout.addLayout(kernel_size_layout)

        # Iterations
        iterations_layout = QHBoxLayout()
        iterations_layout.addWidget(QLabel("Iterations:"))
        self.morph_iterations_slider = QSlider(Qt.Horizontal)
        self.morph_iterations_slider.setRange(1, 10)
        self.morph_iterations_slider.setValue(1)
        self.morph_iterations_label = QLabel("1")
        iterations_layout.addWidget(self.morph_iterations_slider)
        iterations_layout.addWidget(self.morph_iterations_label)
        morph_layout.addLayout(iterations_layout)

        # Apply Morphological Operation Button
        self.apply_morph_btn = QPushButton("Apply Morphological Operation")
        morph_layout.addWidget(self.apply_morph_btn)

        morph_section.add_widget(morph_widget)
        control_layout.addWidget(morph_section)

        # Final Controls Section (Collapsible, collapsed by default)
        final_section = CollapsibleSection("Final Controls")
        final_section.set_expanded(False)  # Collapsed by default
        final_widget = QWidget()
        final_layout = QVBoxLayout(final_widget)

        # Gamma correction
        gamma_layout = QHBoxLayout()
        gamma_layout.addWidget(QLabel("Gamma:"))
        self.gamma_spinbox = QDoubleSpinBox()
        self.gamma_spinbox.setRange(0.1, 5.0)
        self.gamma_spinbox.setValue(1.0)
        self.gamma_spinbox.setSingleStep(0.1)
        gamma_layout.addWidget(self.gamma_spinbox)
        self.apply_gamma_btn = QPushButton("Apply")
        gamma_layout.addWidget(self.apply_gamma_btn)
        final_layout.addLayout(gamma_layout)

        # Undo and Reset buttons
        undo_reset_layout = QHBoxLayout()

        self.undo_btn = QPushButton("Undo Last")
        self.undo_btn.setToolTip("Undo the last image modification")
        undo_reset_layout.addWidget(self.undo_btn)

        self.reset_image_btn = QPushButton("Reset Image")
        self.reset_image_btn.setToolTip("Reset image to original state")
        undo_reset_layout.addWidget(self.reset_image_btn)

        final_layout.addLayout(undo_reset_layout)

        # Apply Edits Button
        self.apply_edits_button = QPushButton("Apply Edits to Main Image")
        final_layout.addWidget(self.apply_edits_button)

        # Add final section to control layout
        final_section.add_widget(final_widget)
        control_layout.addWidget(final_section)



        # Add control panel to main layout
        analysis_layout.addWidget(control_panel)

        # Center panel for image display
        image_display_container = QWidget()
        image_display_layout = QVBoxLayout(image_display_container)
        image_display_layout.setContentsMargins(0, 0, 0, 0)

        # Use QTabWidget to switch between views
        self.analysis_tab_widget = QTabWidget()
        image_display_layout.addWidget(self.analysis_tab_widget)

        # Side-by-side view tab
        side_by_side_widget = QWidget()
        side_by_side_layout = QHBoxLayout(side_by_side_widget)
        side_by_side_layout.setContentsMargins(0, 0, 0, 0)

        # Original image view
        self.analysis_original_view = QPixmapView()
        side_by_side_layout.addWidget(self.analysis_original_view)

        # Processed image view
        self.process_image_view = QPixmapView()
        side_by_side_layout.addWidget(self.process_image_view)

        self.analysis_tab_widget.addTab(side_by_side_widget, "Side by Side View")

        # Full processed image tab
        full_processed_widget = QWidget()
        full_processed_layout = QVBoxLayout(full_processed_widget)
        full_processed_layout.setContentsMargins(0, 0, 0, 0)

        self.full_processed_view = QPixmapView()
        full_processed_layout.addWidget(self.full_processed_view)

        self.analysis_tab_widget.addTab(full_processed_widget, "Processed Image")

        # Add image display container to main layout with stretch
        analysis_layout.addWidget(image_display_container, stretch=1)

        # Right panel for image information
        info_panel = QWidget()
        info_panel.setFixedWidth(250)
        info_layout = QVBoxLayout(info_panel)

        # Image information group
        image_info_group = QGroupBox("Image Information")
        image_info_layout = QVBoxLayout(image_info_group)

        self.image_info_label = QLabel("No image loaded")
        image_info_layout.addWidget(self.image_info_label)

        # Histogram placeholder
        self.histogram_label = QLabel("Histogram will appear here")
        self.histogram_label.setAlignment(Qt.AlignCenter)
        self.histogram_label.setMinimumHeight(200)
        self.histogram_label.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;")
        image_info_layout.addWidget(self.histogram_label)

        info_layout.addWidget(image_info_group)
        info_layout.addStretch()

        # Add info panel to main layout
        analysis_layout.addWidget(info_panel)