#!/usr/bin/env python
# build_visionlab_ai_exe.py - Simple script to build VisionLab Ai executable for testing

import os
import sys
import subprocess
import logging
import argparse
import shutil
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_environment():
    """Check if PyInstaller is installed."""
    try:
        import PyInstaller
        logger.info(f"PyInstaller version: {PyInstaller.__version__}")
        return True
    except ImportError:
        logger.error("PyInstaller is not installed. Installing it now...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            logger.info("PyInstaller installed successfully.")
            return True
        except Exception as e:
            logger.error(f"Failed to install PyInstaller: {e}")
            return False

def install_dependencies():
    """Install required dependencies."""
    logger.info("Installing dependencies...")

    # Basic dependencies needed for the application to run
    dependencies = [
        'PySide6==6.6.1',
        'numpy',
        'Pillow',
        'opencv-python',
        'torch',
        'torchvision',
        'scikit-image',
        'pyyaml',
        'ultralytics',
        'xgboost',  # Add XGBoost to dependencies
        # TensorFlow removed and replaced with PyTorch
    ]

    try:
        for dep in dependencies:
            logger.info(f"Installing {dep}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], check=True)

        logger.info("Dependencies installed successfully.")
        return True
    except Exception as e:
        logger.error(f"Failed to install dependencies: {e}")
        return False

def check_model_files():
    """Check if required model files are present and create directories if needed."""
    logger.info("Checking model files...")

    # Create weights directory if it doesn't exist
    weights_dir = os.path.join(os.getcwd(), 'weights')
    os.makedirs(weights_dir, exist_ok=True)

    # Create models directory if it doesn't exist
    models_dir = os.path.join(os.getcwd(), 'src', 'grainsight_components', 'models')
    os.makedirs(models_dir, exist_ok=True)

    # Check for MobileSAM weights
    mobile_sam_path = os.path.join(weights_dir, 'mobile_sam.pt')
    if not os.path.exists(mobile_sam_path):
        logger.warning(f"MobileSAM weights not found at: {mobile_sam_path}")
        logger.warning("Download from: https://github.com/ChaoningZhang/MobileSAM/tree/master/weights")

    # Check for FastSAM model
    fastsam_path = os.path.join(models_dir, 'FastSAM-x.pt')
    if not os.path.exists(fastsam_path):
        logger.warning(f"FastSAM model not found at: {fastsam_path}")
        logger.warning("Download from: https://github.com/CASIA-IVA-Lab/FastSAM/releases")

    logger.info("Model directories checked and created if needed.")
    return True

def create_spec_file():
    """Create a PyInstaller spec file."""
    logger.info("Creating PyInstaller spec file...")

    spec_content = """# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

block_cipher = None

# Define the base path
base_path = os.path.abspath('.')

# Collect all necessary data files
datas = []

# Add model weights and other resource files
weights_dir = os.path.join(base_path, 'weights')
if os.path.exists(weights_dir):
    datas.append((weights_dir, 'weights'))

# Add any other resource directories
resource_dirs = [
    ('src/gui/styles', 'src/gui/styles'),
    ('src/gui/icons', 'src/gui/icons'),
    ('src/grainsight_components/models', 'src/grainsight_components/models'),
    ('src/grainsight_components/weights', 'src/grainsight_components/weights'),
]

for src, dst in resource_dirs:
    src_path = os.path.join(base_path, src)
    if os.path.exists(src_path):
        datas.append((src_path, dst))

# Add detectron2 directory
detectron2_dir = os.path.join(base_path, 'src', 'detectron2')
if os.path.exists(detectron2_dir):
    datas.append((detectron2_dir, 'detectron2'))

# Add mobile_sam module if it exists
try:
    import mobile_sam
    datas += collect_data_files('mobile_sam')
except ImportError:
    pass

# Collect all necessary hidden imports
hiddenimports = [
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'numpy',
    'PIL',
    'cv2',
    'torch',
    'torchvision',
    'yaml',
    'ultralytics',
    'xgboost',
    'torch._C',
    'torch.utils',
    'torch.utils.data',
    'torch.nn',
    'torch.optim',
    'torch._numpy',
    'torch._numpy._ufuncs',
    'torch._dynamo',
    'torch._dynamo.utils',
    'torch._dynamo.convert_frame',
    'detectron2',
    'fvcore',
]

# Add mobile_sam submodules if available
try:
    import mobile_sam
    hiddenimports += collect_submodules('mobile_sam')
except ImportError:
    pass

a = Analysis(
    ['main.py'],
    pathex=[base_path, os.path.join(base_path, 'src')],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='VisionLab Ai',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # Set to True for testing to see any error messages
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='VisionLab Ai',
)
"""

    with open('visionlab_ai_test.spec', 'w') as f:
        f.write(spec_content)

    logger.info("PyInstaller spec file created: visionlab_ai_test.spec")
    return True

def find_xgboost_dll():
    """Find the XGBoost DLL file in the Python environment."""
    logger.info("Looking for XGBoost DLL...")

    try:
        import xgboost
        import os

        # Get the XGBoost package directory
        xgboost_dir = os.path.dirname(xgboost.__file__)
        logger.info(f"XGBoost package directory: {xgboost_dir}")

        # Check common locations for the DLL
        possible_dll_locations = [
            os.path.join(xgboost_dir, 'lib', 'xgboost.dll'),
            os.path.join(xgboost_dir, '..', 'lib', 'xgboost.dll'),
            os.path.join(xgboost_dir, '..', '..', 'Library', 'bin', 'xgboost.dll'),
            os.path.join(os.path.dirname(sys.executable), 'Library', 'bin', 'xgboost.dll'),
        ]

        for dll_path in possible_dll_locations:
            if os.path.exists(dll_path):
                logger.info(f"Found XGBoost DLL at: {dll_path}")
                return dll_path

        logger.warning("XGBoost DLL not found in common locations.")
        return None
    except ImportError:
        logger.warning("XGBoost not installed. Skipping DLL search.")
        return None

def build_executable(clean=True):
    """Build the executable using PyInstaller."""
    logger.info("Building executable...")

    # Clean build and dist directories if requested
    if clean:
        for dir_name in ['build', 'dist']:
            if os.path.exists(dir_name):
                logger.info(f"Cleaning {dir_name} directory...")
                shutil.rmtree(dir_name)

    # Build command - use direct PyInstaller command instead of spec file
    # Use correct path separator for Windows
    path_sep = ';' if sys.platform == 'win32' else ':'

    # Add src to PYTHONPATH for PyInstaller to find local modules
    env = os.environ.copy()
    python_path = env.get('PYTHONPATH', '')
    src_path_abs = os.path.abspath('src') # Ensure src path is absolute
    if python_path:
        env['PYTHONPATH'] = f"{src_path_abs}{path_sep}{python_path}"
    else:
        env['PYTHONPATH'] = src_path_abs

    cmd = [
        sys.executable,
        '-m',
        'PyInstaller',
        '--name=VisionLab Ai',
        f'--add-data=weights{path_sep}weights',
        f'--add-data=src/gui/styles{path_sep}src/gui/styles',
        f'--add-data=src/gui/icons{path_sep}src/gui/icons',
        f'--add-data=src/grainsight_components/models{path_sep}src/grainsight_components/models',
        f'--add-data=src/grainsight_components/weights{path_sep}src/grainsight_components/weights',
        f'--add-data=src/detectron2{path_sep}detectron2',
        '--hidden-import=PySide6.QtCore',
        '--hidden-import=PySide6.QtGui',
        '--hidden-import=PySide6.QtWidgets',
        '--hidden-import=numpy',
        '--hidden-import=PIL',
        '--hidden-import=cv2',
        '--hidden-import=torch',
        '--hidden-import=torchvision',
        '--hidden-import=yaml',
        '--hidden-import=ultralytics',
        '--hidden-import=mobile_sam',
        '--hidden-import=xgboost',
        '--hidden-import=torch._C',
        '--hidden-import=torch.utils',
        '--hidden-import=torch.utils.data',
        '--hidden-import=torch.nn',
        '--hidden-import=torch.optim',
        '--hidden-import=torch._numpy',
        '--hidden-import=torch._numpy._ufuncs',
        '--hidden-import=torch._dynamo',
        '--hidden-import=torch._dynamo.utils',
        '--hidden-import=torch._dynamo.convert_frame',
        '--hidden-import=detectron2',
        '--hidden-import=fvcore',
        # TensorFlow hidden import removed
        '--collect-all=xgboost',  # Collect all XGBoost files including DLLs
        '--console',  # Keep console for testing to see error messages
        'main.py'
    ]

    try:
        logger.info(f"Running PyInstaller...")
        logger.info(f"Executing command: {' '.join(cmd)}") # Log the command
        logger.info(f"PYTHONPATH for subprocess: {env.get('PYTHONPATH')}") # Log PYTHONPATH
        subprocess.run(cmd, check=True, env=env)
        logger.info("Build completed successfully.")

        # Get the output path
        output_path = os.path.join('dist', 'VisionLab Ai')

        # Handle XGBoost DLL manually
        xgboost_dll_path = find_xgboost_dll()
        if xgboost_dll_path:
            # Create directories if they don't exist
            for dll_dir in ['lib', 'bin', 'Library/bin']:
                target_dir = os.path.join(output_path, dll_dir)
                os.makedirs(target_dir, exist_ok=True)

                # Copy the DLL to each possible location
                target_path = os.path.join(target_dir, 'xgboost.dll')
                logger.info(f"Copying XGBoost DLL to: {target_path}")
                shutil.copy2(xgboost_dll_path, target_path)

        logger.info(f"Executable created at: {os.path.abspath(output_path)}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Build failed: {e}")
        return False

def main():
    """Main function to build the executable."""
    parser = argparse.ArgumentParser(description='Build VisionLab Ai executable for testing')
    parser.add_argument('--skip-deps', action='store_true', help='Skip installing dependencies')
    parser.add_argument('--no-clean', action='store_true', help='Do not clean build and dist directories')
    args = parser.parse_args()

    logger.info("Starting VisionLab Ai executable build process...")

    # Check environment
    if not check_environment():
        logger.error("Environment check failed. Please install PyInstaller manually.")
        return 1

    # Install dependencies if not skipped
    if not args.skip_deps:
        if not install_dependencies():
            logger.error("Failed to install dependencies. Build process will continue but may fail.")

    # Check model files
    check_model_files()

    # Build executable
    if not build_executable(clean=not args.no_clean):
        logger.error("Failed to build executable. Please check the logs for details.")
        return 1

    logger.info("Build process completed successfully.")
    logger.info("The executable is located at: dist/VisionLab Ai/VisionLab Ai.exe")
    logger.info("To run the executable, navigate to the dist/VisionLab Ai directory and run VisionLab Ai.exe")

    return 0

if __name__ == "__main__":
    sys.exit(main())
