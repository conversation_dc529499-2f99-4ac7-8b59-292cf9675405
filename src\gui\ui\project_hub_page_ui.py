# src/gui/ui/project_hub_page_ui.py
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                             QListWidget, QGroupBox, QLineEdit, QTextEdit, QSplitter,
                             QFormLayout, QSpacerItem, QSizePolicy, QAbstractItemView,
                             QFrame, QGridLayout)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon
from src.widgets.pixmap_view import QPixmapView

class ProjectHubPageUI:
    """UI definition for the Project Hub page."""

    def setup_ui(self, parent_widget: QWidget):
        """Set up the UI elements for the Project Hub page."""
        # Main layout
        self.main_layout = QVBoxLayout(parent_widget)
        self.main_layout.setContentsMargins(10, 10, 10, 10)
        self.main_layout.setSpacing(10)

        # Project header section
        self.header_layout = QHBoxLayout()
        self.project_name_label = QLabel("No Project Loaded")
        self.project_name_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.header_layout.addWidget(self.project_name_label)
        self.header_layout.addStretch()

        # VLP project buttons
        self.new_vlp_project_button = QPushButton("New VLP Project")
        self.open_vlp_project_button = QPushButton("Open VLP Project")

        # Add VLP project buttons
        self.header_layout.addWidget(self.new_vlp_project_button)
        self.header_layout.addWidget(self.open_vlp_project_button)

        self.main_layout.addLayout(self.header_layout)

        # Main content splitter (gallery on left, details on right)
        self.content_splitter = QSplitter(Qt.Horizontal)

        # Left side - Gallery
        self.gallery_widget = QWidget()
        self.gallery_layout = QVBoxLayout(self.gallery_widget)
        self.gallery_layout.setContentsMargins(0, 0, 0, 0)

        # Gallery header with title and import button
        self.gallery_header = QHBoxLayout()
        self.gallery_title = QLabel("Project Images")
        self.gallery_title.setStyleSheet("font-weight: bold;")
        self.gallery_header.addWidget(self.gallery_title)
        self.gallery_header.addStretch()

        # Import images button - will be styled by the theme system
        self.import_images_button = QPushButton("Import Images")
        self.import_images_button.setEnabled(False)  # Disabled until project is loaded
        self.import_images_button.setMinimumHeight(30)
        self.gallery_header.addWidget(self.import_images_button)

        self.gallery_layout.addLayout(self.gallery_header)

        # Gallery list
        self.project_gallery_list = QListWidget()
        self.project_gallery_list.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.project_gallery_list.setMinimumWidth(300)
        self.gallery_layout.addWidget(self.project_gallery_list)

        # Right side - Details panel
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        self.details_layout.setContentsMargins(0, 0, 0, 0)

        # Image preview section - will be styled by the theme system
        self.image_preview_groupbox = QGroupBox("Image Preview")
        self.image_preview_groupbox.setEnabled(False)  # Disabled until image is selected
        self.image_preview_layout = QVBoxLayout(self.image_preview_groupbox)
        self.image_preview_layout.setContentsMargins(10, 20, 10, 10)  # Add more padding

        # Image preview view
        self.image_preview_view = QPixmapView()
        self.image_preview_view.setMinimumHeight(250)  # Set minimum height for preview
        self.image_preview_layout.addWidget(self.image_preview_view)

        # Image info label
        self.image_info_label = QLabel("No image selected")
        self.image_info_label.setAlignment(Qt.AlignCenter)
        self.image_preview_layout.addWidget(self.image_info_label)

        self.details_layout.addWidget(self.image_preview_groupbox)

        # Metadata group box - will be styled by the theme system
        self.metadata_groupbox = QGroupBox("Image Metadata")
        self.metadata_groupbox.setEnabled(False)  # Disabled until image is selected
        self.metadata_layout = QFormLayout(self.metadata_groupbox)
        self.metadata_layout.setContentsMargins(10, 20, 10, 10)  # Add more padding

        self.metadata_filename_label = QLabel("-")
        self.metadata_sampleid_edit = QLineEdit()
        self.metadata_magnification_edit = QLineEdit()
        self.metadata_notes_edit = QTextEdit()
        self.metadata_notes_edit.setMaximumHeight(100)

        self.metadata_layout.addRow("Filename:", self.metadata_filename_label)
        self.metadata_layout.addRow("Sample ID:", self.metadata_sampleid_edit)
        self.metadata_layout.addRow("Magnification:", self.metadata_magnification_edit)
        self.metadata_layout.addRow("Notes:", self.metadata_notes_edit)

        # Save metadata button - will be styled by the theme system
        self.save_metadata_button = QPushButton("Save Metadata")
        self.save_metadata_button.setMinimumHeight(30)
        self.metadata_layout.addRow("", self.save_metadata_button)

        self.details_layout.addWidget(self.metadata_groupbox)

        # Actions group box - will be styled by the theme system
        self.actions_groupbox = QGroupBox("Analysis Actions")
        self.actions_groupbox.setEnabled(False)  # Disabled until image is selected
        self.actions_layout = QVBoxLayout(self.actions_groupbox)
        self.actions_layout.setSpacing(5)  # Reduce spacing between elements
        self.actions_layout.setContentsMargins(5, 15, 5, 5)  # Reduce padding

        # Create a grid layout for analysis buttons (2 columns)
        self.analysis_grid = QGridLayout()
        self.analysis_grid.setSpacing(5)  # Reduce space between grid items even more
        self.analysis_grid.setContentsMargins(0, 0, 0, 0)
        self.analysis_grid.setColumnStretch(0, 1)  # Make columns stretch equally
        self.analysis_grid.setColumnStretch(1, 1)
        # Set row heights to be equal
        self.analysis_grid.setRowStretch(0, 1)
        self.analysis_grid.setRowStretch(1, 1)
        self.analysis_grid.setRowStretch(2, 1)

        # Analysis buttons with icons and improved styling

        # Use theme-aware button style for header buttons

        # Analysis buttons will be styled by the theme system

        # Unsupervised Segmentation
        self.analyze_unsupervised_button = QPushButton("Unsupervised Segmentation")
        self.analyze_unsupervised_button.setToolTip("Segment image using unsupervised methods")
        self.analyze_unsupervised_button.setMinimumHeight(30)  # Even shorter buttons
        self.analysis_grid.addWidget(self.analyze_unsupervised_button, 0, 0)

        # Trainable Segmentation
        self.analyze_trainable_button = QPushButton("Trainable Segmentation")
        self.analyze_trainable_button.setToolTip("Segment image using trainable methods")
        self.analyze_trainable_button.setMinimumHeight(30)
        self.analysis_grid.addWidget(self.analyze_trainable_button, 0, 1)

        # Grain Size Analysis
        self.analyze_grain_size_button = QPushButton("Grain Size Analysis")
        self.analyze_grain_size_button.setToolTip("Analyze grain size in the image")
        self.analyze_grain_size_button.setMinimumHeight(30)
        self.analysis_grid.addWidget(self.analyze_grain_size_button, 1, 0)

        # Image Lab
        self.analyze_porosity_button = QPushButton("Image Lab")
        self.analyze_porosity_button.setToolTip("Edit and enhance the image")
        self.analyze_porosity_button.setMinimumHeight(30)
        self.analysis_grid.addWidget(self.analyze_porosity_button, 1, 1)

        # AI Assistant Analysis
        self.analyze_ai_assistant_button = QPushButton("AI Assistant Analysis")
        self.analyze_ai_assistant_button.setToolTip("Analyze image using AI assistant")
        self.analyze_ai_assistant_button.setMinimumHeight(30)
        self.analysis_grid.addWidget(self.analyze_ai_assistant_button, 2, 0)

        # Point Counting
        self.analyze_point_counting_button = QPushButton("Point Counting")
        self.analyze_point_counting_button.setToolTip("Perform point counting analysis")
        self.analyze_point_counting_button.setMinimumHeight(30)
        self.analysis_grid.addWidget(self.analyze_point_counting_button, 2, 1)

        # Advanced Segmentation
        self.analyze_advanced_segmentation_button = QPushButton("Advanced Segmentation")
        self.analyze_advanced_segmentation_button.setToolTip("Annotate and train Faster RCNN models")
        self.analyze_advanced_segmentation_button.setMinimumHeight(30)
        self.analysis_grid.addWidget(self.analyze_advanced_segmentation_button, 3, 0)

        # Add the grid to the main layout
        self.actions_layout.addLayout(self.analysis_grid)

        # Add a thin separator line before batch processing
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("margin-top: 2px; margin-bottom: 2px; max-height: 1px;")
        self.actions_layout.addWidget(separator)

        # Batch processing section - will be styled by the theme system
        batch_container = QWidget()
        batch_layout = QVBoxLayout(batch_container)
        batch_layout.setContentsMargins(2, 2, 2, 2)  # Minimal margins

        self.batch_label = QLabel("Batch Processing (Future Feature)")
        batch_layout.addWidget(self.batch_label)

        self.batch_process_button = QPushButton("Start Batch Processing")
        self.batch_process_button.setEnabled(False)  # Disable for now if not implemented
        self.batch_process_button.setMinimumHeight(30)
        self.batch_process_button.setToolTip("Process multiple images in batch mode (coming soon)")
        batch_layout.addWidget(self.batch_process_button)

        self.actions_layout.addWidget(batch_container)

        self.details_layout.addWidget(self.actions_groupbox)

        # Add spacer at the bottom
        self.details_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # Add widgets to splitter
        self.content_splitter.addWidget(self.gallery_widget)
        self.content_splitter.addWidget(self.details_widget)
        self.content_splitter.setStretchFactor(0, 1)  # Gallery gets more space
        self.content_splitter.setStretchFactor(1, 1)

        # Add splitter to main layout
        self.main_layout.addWidget(self.content_splitter)