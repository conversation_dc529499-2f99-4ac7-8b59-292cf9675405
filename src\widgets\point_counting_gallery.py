# src/widgets/point_counting_gallery.py

from PySide6.QtCore import Signal

from src.widgets.page_image_gallery import PageImageGallery

class PointCountingGallery(PageImageGallery):
    """Image gallery specifically for the Point Counting Page."""

    # Additional signals specific to point counting page
    point_counting_requested = Signal(int)  # Signal to request point counting of an image

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_point_counting_controls()

    def setup_point_counting_controls(self):
        """Set up controls specific to the point counting gallery."""
        # We're removing the Count Points button as it's redundant
        # The user can simply click on an image to select it for point counting
        pass

    def update_ui_state(self):
        """Update the UI state based on the current selection."""
        # No UI elements to update since we removed the count button
        pass

    def clear_images(self):
        """Removes all images and thumbnails from the gallery."""
        # Call the parent class's clear method to handle the actual clearing
        self.clear()
        print("PointCountingGallery: All images cleared.")

    def get_image_data(self, file_path):
        """Retrieves the image data for a given file path.

        Args:
            file_path: The file path to look up

        Returns:
            The image data if found, None otherwise
        """
        try:
            # Find the index of the file path in our list
            if file_path in self.file_paths:
                index = self.file_paths.index(file_path)
                # Return the corresponding image data
                return self.images[index]
            else:
                print(f"Warning: File path not found in gallery: {file_path}")
                return None
        except Exception as e:
            print(f"Error retrieving image data: {e}")
            return None
