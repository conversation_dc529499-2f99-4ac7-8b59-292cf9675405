<?php
// Enable error reporting for development (disable in production)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Database configuration
$db_config = [
    'host' => 'localhost',
    'dbname' => 'visionlab_waitlist',
    'username' => 'your_db_username',
    'password' => 'your_db_password'
];

// Email configuration
$email_config = [
    'smtp_host' => 'smtp.hostinger.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'your_email_password',
    'from_email' => '<EMAIL>',
    'from_name' => 'VisionLab AI Website',
    'admin_email' => '<EMAIL>'
];

try {
    // Validate and sanitize input
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    // Validation
    $errors = [];
    
    if (empty($name) || strlen($name) < 2) {
        $errors[] = 'Name must be at least 2 characters long';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address';
    }
    
    if (empty($subject) || strlen($subject) < 5) {
        $errors[] = 'Subject must be at least 5 characters long';
    }
    
    if (empty($message) || strlen($message) < 10) {
        $errors[] = 'Message must be at least 10 characters long';
    }
    
    // Basic spam protection
    $spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'million dollars'];
    $messageContent = strtolower($subject . ' ' . $message);
    
    foreach ($spamKeywords as $keyword) {
        if (strpos($messageContent, $keyword) !== false) {
            $errors[] = 'Message contains prohibited content';
            break;
        }
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
        exit;
    }
    
    // Rate limiting check (optional - requires database)
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    try {
        // Connect to database
        $pdo = new PDO(
            "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset=utf8mb4",
            $db_config['username'],
            $db_config['password'],
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );
        
        // Check for recent submissions from same IP (rate limiting)
        $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM contact_messages WHERE ip_address = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)');
        $stmt->execute([$clientIp]);
        $recentCount = $stmt->fetch()['count'];
        
        if ($recentCount >= 5) {
            echo json_encode(['success' => false, 'message' => 'Too many messages sent. Please wait before sending another message.']);
            exit;
        }
        
        // Insert contact message into database
        $stmt = $pdo->prepare('
            INSERT INTO contact_messages (name, email, subject, message, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ');
        
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $stmt->execute([$name, $email, $subject, $message, $clientIp, $userAgent]);
        $messageId = $pdo->lastInsertId();
        
    } catch (PDOException $e) {
        // If database fails, continue without storing (but log the error)
        error_log('Database error in contact form: ' . $e->getMessage());
        $messageId = 'db_error_' . time();
    }
    
    // Send email to admin
    $adminEmailSent = sendAdminNotification($name, $email, $subject, $message, $messageId, $email_config);
    
    // Send confirmation email to user
    $userEmailSent = sendUserConfirmation($name, $email, $subject, $email_config);
    
    if ($adminEmailSent) {
        echo json_encode([
            'success' => true, 
            'message' => 'Thank you for your message! We\'ll get back to you within 24 hours.',
            'id' => $messageId
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'There was an issue sending your message. Please try again or contact us directly.'
        ]);
    }
    
} catch (Exception $e) {
    error_log('General error in contact form: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred. Please try again.']);
}

function sendAdminNotification($name, $email, $subject, $message, $messageId, $config) {
    try {
        // Use PHPMailer for better email handling
        require_once 'vendor/autoload.php';
        
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = $config['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $config['smtp_username'];
        $mail->Password = $config['smtp_password'];
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $config['smtp_port'];
        
        // Recipients
        $mail->setFrom($config['from_email'], $config['from_name']);
        $mail->addAddress($config['admin_email'], 'VisionLab AI Admin');
        $mail->addReplyTo($email, $name);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = '[VisionLab AI Contact] ' . $subject;
        
        $mail->Body = getAdminEmailTemplate($name, $email, $subject, $message, $messageId);
        $mail->AltBody = getAdminEmailTextVersion($name, $email, $subject, $message, $messageId);
        
        $mail->send();
        return true;
        
    } catch (Exception $e) {
        error_log('Admin email error: ' . $e->getMessage());
        
        // Fallback to PHP mail() function
        return sendSimpleAdminEmail($name, $email, $subject, $message, $messageId, $config);
    }
}

function sendUserConfirmation($name, $email, $subject, $config) {
    try {
        // Use PHPMailer for better email handling
        require_once 'vendor/autoload.php';
        
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = $config['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $config['smtp_username'];
        $mail->Password = $config['smtp_password'];
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $config['smtp_port'];
        
        // Recipients
        $mail->setFrom($config['from_email'], $config['from_name']);
        $mail->addAddress($email, $name);
        $mail->addReplyTo($config['from_email'], $config['from_name']);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Thank you for contacting VisionLab AI';
        
        $mail->Body = getUserConfirmationTemplate($name, $subject);
        $mail->AltBody = getUserConfirmationTextVersion($name, $subject);
        
        $mail->send();
        return true;
        
    } catch (Exception $e) {
        error_log('User confirmation email error: ' . $e->getMessage());
        return false; // Don't fail the whole process if user confirmation fails
    }
}

function sendSimpleAdminEmail($name, $email, $subject, $message, $messageId, $config) {
    $emailSubject = '[VisionLab AI Contact] ' . $subject;
    $emailMessage = getAdminEmailTextVersion($name, $email, $subject, $message, $messageId);
    
    $headers = [
        'From: ' . $config['from_name'] . ' <' . $config['from_email'] . '>',
        'Reply-To: ' . $name . ' <' . $email . '>',
        'Content-Type: text/html; charset=UTF-8',
        'MIME-Version: 1.0'
    ];
    
    return mail($config['admin_email'], $emailSubject, getAdminEmailTemplate($name, $email, $subject, $message, $messageId), implode("\r\n", $headers));
}

function getAdminEmailTemplate($name, $email, $subject, $message, $messageId) {
    $timestamp = date('Y-m-d H:i:s');
    $escapedMessage = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');
    
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>New Contact Form Submission</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
            .content { background: white; padding: 20px; border: 1px solid #ddd; }
            .field { margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
            .label { font-weight: bold; color: #2563eb; }
            .message-content { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 4px; margin-top: 10px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>New Contact Form Submission</h2>
                <p>VisionLab AI Website</p>
            </div>
            
            <div class='content'>
                <div class='field'>
                    <div class='label'>Message ID:</div>
                    {$messageId}
                </div>
                
                <div class='field'>
                    <div class='label'>Timestamp:</div>
                    {$timestamp}
                </div>
                
                <div class='field'>
                    <div class='label'>Name:</div>
                    {$name}
                </div>
                
                <div class='field'>
                    <div class='label'>Email:</div>
                    <a href='mailto:{$email}'>{$email}</a>
                </div>
                
                <div class='field'>
                    <div class='label'>Subject:</div>
                    {$subject}
                </div>
                
                <div class='field'>
                    <div class='label'>Message:</div>
                    <div class='message-content'>{$escapedMessage}</div>
                </div>
            </div>
        </div>
    </body>
    </html>
    ";
}

function getAdminEmailTextVersion($name, $email, $subject, $message, $messageId) {
    $timestamp = date('Y-m-d H:i:s');
    
    return "
New Contact Form Submission - VisionLab AI

Message ID: {$messageId}
Timestamp: {$timestamp}
Name: {$name}
Email: {$email}
Subject: {$subject}

Message:
{$message}

---
This message was sent through the VisionLab AI contact form.
    ";
}

function getUserConfirmationTemplate($name, $subject) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Thank you for contacting VisionLab AI</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #2563eb, #3b82f6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: white; padding: 30px; border: 1px solid #e2e8f0; }
            .footer { background: #f8fafc; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; font-size: 14px; color: #64748b; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Thank You!</h1>
                <p>We've received your message</p>
            </div>
            
            <div class='content'>
                <h2>Hi {$name},</h2>
                
                <p>Thank you for reaching out to VisionLab AI! We've successfully received your message regarding: <strong>{$subject}</strong></p>
                
                <p>Our team will review your inquiry and get back to you within 24 hours. We appreciate your interest in VisionLab AI and look forward to assisting you.</p>
                
                <p>In the meantime, feel free to:</p>
                <ul>
                    <li>Join our waitlist for early access to VisionLab AI</li>
                    <li>Follow our development progress on our website</li>
                    <li>Check out our feature roadmap and timeline</li>
                </ul>
                
                <p>If you have any urgent questions, please don't hesitate to reply to this email.</p>
                
                <p>Best regards,<br>The VisionLab AI Team</p>
            </div>
            
            <div class='footer'>
                <p>VisionLab AI - Revolutionary AI-Powered Image Segmentation</p>
                <p>This is an automated response to confirm we received your message.</p>
            </div>
        </div>
    </body>
    </html>
    ";
}

function getUserConfirmationTextVersion($name, $subject) {
    return "
Hi {$name},

Thank you for reaching out to VisionLab AI! We've successfully received your message regarding: {$subject}

Our team will review your inquiry and get back to you within 24 hours. We appreciate your interest in VisionLab AI and look forward to assisting you.

In the meantime, feel free to:
- Join our waitlist for early access to VisionLab AI
- Follow our development progress on our website
- Check out our feature roadmap and timeline

If you have any urgent questions, please don't hesitate to reply to this email.

Best regards,
The VisionLab AI Team

---
VisionLab AI - Revolutionary AI-Powered Image Segmentation
This is an automated response to confirm we received your message.
    ";
}
?>