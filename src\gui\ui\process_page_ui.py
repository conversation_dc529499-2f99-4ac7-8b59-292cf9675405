# src/gui/ui/process_page_ui.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QScrollArea, QFrame, QTabWidget, QSlider, QSpinBox, QDoubleSpinBox,
    QComboBox, QProgressBar, QFormLayout, QMessageBox, QSplitter)
from PySide6.QtCore import Qt

from src.widgets.scrollable_frame import ScrollableFrame
from src.widgets.pixmap_view import QPixmapView
from src.widgets.segment_grid_widget import SegmentGridWidget
from src.gui.color_picker_dialog import ColorPickerDialog
from src.gui.export_annotations_dialog import ExportAnnotationsDialog

# Import ProcessPageGallery
from src.widgets.process_page_gallery import ProcessPageGallery

# Import PaletteComboBox
from src.widgets.palette_combo_box import PaletteComboBox

# Import SynchronizedImageView
from src.gui.widgets.synchronized_image_view import SynchronizedImageView

class ProcessPageUI:
    """Class for creating and managing the image processing page UI."""

    def show_export_annotations_dialog(self):
        """Shows the export annotations dialog."""
        print("DEBUG: show_export_annotations_dialog called")

        # Check if we have the necessary attributes
        if not hasattr(self, 'segmented_image') or self.segmented_image is None:
            QMessageBox.warning(self, "Warning", "No segmented image available. Please segment an image first.")
            return

        # Create and show the dialog
        dialog = ExportAnnotationsDialog(
            segmented_image=self.segmented_image,
            original_image=self.image if hasattr(self, 'image') else None,
            segment_names=self.segment_names if hasattr(self, 'segment_names') else {},
            image_path=self.image_full_path if hasattr(self, 'image_full_path') else None,
            parent=self
        )
        dialog.exec()

    def setup_process_page(self):
        """Sets up the Image Processing page."""
        self.process_page = QWidget()
        process_layout = QHBoxLayout(self.process_page)
        self.stacked_widget.addTab(self.process_page, "Unsupervised Segmentation")

        # Sidebar
        self.sidebar = ScrollableFrame()
        self.sidebar.setFixedWidth(350)
        sidebar_layout = QVBoxLayout(self.sidebar.get_content_frame())
        process_layout.addWidget(self.sidebar)
        self.setup_sidebar(sidebar_layout) # Add Controls to Sidebar

        # Image Display Area (using QPixmapView)
        image_display_container = QWidget()
        image_display_layout = QHBoxLayout(image_display_container)
        image_display_layout.setContentsMargins(0, 0, 0, 0)
        process_layout.addWidget(image_display_container, stretch=1)

        # Left side: Image display with splitter for segment grid
        left_container = QWidget()
        left_layout = QVBoxLayout(left_container)
        left_layout.setContentsMargins(0, 0, 0, 0)
        image_display_layout.addWidget(left_container)

        # Create a splitter to allow resizing between image and segment grid
        self.image_grid_splitter = QSplitter(Qt.Orientation.Vertical)
        left_layout.addWidget(self.image_grid_splitter)

        # Top part: Image tabs
        image_view_container = QWidget()
        image_view_layout = QVBoxLayout(image_view_container)
        image_view_layout.setContentsMargins(0, 0, 0, 0)
        self.image_grid_splitter.addWidget(image_view_container)

        # Use QTabWidget to switch between original and segmented images
        self.tab_widget = QTabWidget()
        image_view_layout.addWidget(self.tab_widget)

        # Original Image Tab (using QPixmapView)
        self.original_image_view = QPixmapView()
        self.tab_widget.addTab(self.original_image_view, "Original Image")

        # Segmented Image Tab (using QPixmapView)
        self.segmented_image_view = QPixmapView()
        self.tab_widget.addTab(self.segmented_image_view, "Segmented Image")

        # Side-by-Side Synchronized View Tab
        self.side_by_side_tab = QWidget()
        side_by_side_layout = QVBoxLayout(self.side_by_side_tab)
        side_by_side_layout.setContentsMargins(0, 0, 0, 0)
        side_by_side_layout.setSpacing(5)

        # Create horizontal layout for the two synchronized views
        sync_views_layout = QHBoxLayout()
        sync_views_layout.setContentsMargins(0, 0, 0, 0)
        sync_views_layout.setSpacing(5)

        # Left view: Original image
        original_sync_container = QWidget()
        original_sync_layout = QVBoxLayout(original_sync_container)
        original_sync_layout.setContentsMargins(0, 0, 0, 0)
        original_sync_layout.setSpacing(2)

        original_sync_label = QLabel("Original Image")
        original_sync_label.setAlignment(Qt.AlignCenter)
        original_sync_label.setStyleSheet("font-weight: bold; font-size: 10pt; padding: 2px;")
        original_sync_layout.addWidget(original_sync_label)

        self.original_sync_view = SynchronizedImageView()
        self.original_sync_view.setMinimumSize(300, 300)
        original_sync_layout.addWidget(self.original_sync_view)

        # Right view: Segmented image
        segmented_sync_container = QWidget()
        segmented_sync_layout = QVBoxLayout(segmented_sync_container)
        segmented_sync_layout.setContentsMargins(0, 0, 0, 0)
        segmented_sync_layout.setSpacing(2)

        segmented_sync_label = QLabel("Segmented Image")
        segmented_sync_label.setAlignment(Qt.AlignCenter)
        segmented_sync_label.setStyleSheet("font-weight: bold; font-size: 10pt; padding: 2px;")
        segmented_sync_layout.addWidget(segmented_sync_label)

        self.segmented_sync_view = SynchronizedImageView()
        self.segmented_sync_view.setMinimumSize(300, 300)
        segmented_sync_layout.addWidget(self.segmented_sync_view)

        # Add both views to the horizontal layout with equal stretch factors (50/50 split)
        sync_views_layout.addWidget(original_sync_container, 1)  # 50% of space
        sync_views_layout.addWidget(segmented_sync_container, 1)  # 50% of space
        side_by_side_layout.addLayout(sync_views_layout)

        # Add instructions for the synchronized view
        sync_instructions = QLabel("Use mouse wheel to zoom, drag to pan, use scrollbars to navigate, or press Ctrl+0 to reset view. Both views are synchronized.")
        sync_instructions.setAlignment(Qt.AlignCenter)
        sync_instructions.setStyleSheet("color: #666666; font-style: italic; padding: 5px;")
        sync_instructions.setWordWrap(True)
        side_by_side_layout.addWidget(sync_instructions)

        # Synchronize the two views
        self.original_sync_view.add_synced_view(self.segmented_sync_view)
        self.segmented_sync_view.add_synced_view(self.original_sync_view)

        # Add the side-by-side tab to the tab widget
        self.tab_widget.addTab(self.side_by_side_tab, "Side-by-Side View")

        # Bottom part: Segment grid
        segment_grid_container = QWidget()
        segment_grid_layout = QVBoxLayout(segment_grid_container)
        segment_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.image_grid_splitter.addWidget(segment_grid_container)

        # Add a header for the segment grid with label
        segment_grid_header = QWidget()
        segment_grid_header_layout = QHBoxLayout(segment_grid_header)
        segment_grid_header_layout.setContentsMargins(0, 0, 0, 0)

        # Add a label for the segment grid
        segment_grid_label = QLabel("Segment Grid View")
        segment_grid_label.setStyleSheet("font-weight: bold; font-size: 10pt;")
        segment_grid_header_layout.addWidget(segment_grid_label)

        segment_grid_layout.addWidget(segment_grid_header)

        # Add the segment grid widget
        self.segment_grid = SegmentGridWidget()
        segment_grid_layout.addWidget(self.segment_grid)

        # Set initial sizes for the splitter (70% image, 30% grid)
        self.image_grid_splitter.setSizes([700, 300])

        # Hide the segment grid container initially
        segment_grid_container.hide()

        # Right side: Controls panel
        controls_panel = QWidget()
        controls_panel.setFixedWidth(350)
        controls_layout = QVBoxLayout(controls_panel)
        image_display_layout.addWidget(controls_panel)

        # First group: Merge action button at the top
        merge_action_group = QGroupBox("Merge Action")
        merge_action_layout = QVBoxLayout(merge_action_group)

        # Create the merge button
        self.merge_button = QPushButton("Merge Selected Segments")
        self.merge_button.setMinimumHeight(25)  # Make button taller for better visibility
        merge_action_layout.addWidget(self.merge_button)

        # Add the merge action group to the main controls layout
        controls_layout.addWidget(merge_action_group)

        # Add a small spacing between the groups
        controls_layout.addSpacing(5)

        # Create a scrollable container for the Segments & Colors group box
        segments_scroll_container = QScrollArea()
        segments_scroll_container.setWidgetResizable(True)
        segments_scroll_container.setFixedHeight(290)  # Fixed height for the scrollable container
        segments_scroll_container.setFrameShape(QFrame.NoFrame)  # Remove the frame border

        # Create the Segments & Colors group box inside the scroll area
        segments_group = QGroupBox("Segments & Colors")
        segments_layout = QVBoxLayout(segments_group)
        segments_layout.setSpacing(5)  # Add spacing between elements

        # Add description label
        description_label = QLabel("Select segments to merge:")
        segments_layout.addWidget(description_label)

        # Create a frame for the segments with checkboxes
        self.merge_inner_frame = QFrame()
        self.merge_inner_layout = QVBoxLayout(self.merge_inner_frame)
        self.merge_inner_layout.setContentsMargins(5, 5, 5, 5)  # Add margins inside the frame

        # Add the inner frame to the segments layout
        segments_layout.addWidget(self.merge_inner_frame)

        # Set the group box as the widget for the scroll area
        segments_scroll_container.setWidget(segments_group)

        # Add the scroll container to the main controls layout
        controls_layout.addWidget(segments_scroll_container)

        # For backward compatibility, create these objects that won't be used
        # This prevents errors in code that expects these widgets to exist
        self.info_frame = QFrame()
        self.info_frame.setLayout(QVBoxLayout())
        self.merge_scroll_area = segments_scroll_container  # Redirect to the new scroll area

        # Post-processing Controls
        post_proc_group = QGroupBox("Post-processing")
        post_proc_layout = QVBoxLayout(post_proc_group)

        # Add color palette selection
        self.color_palette_combo = PaletteComboBox()
        self.color_palette_combo.addItems(["tab20", "viridis", "plasma", "magma", "inferno", "cividis", "Pastel1", "Set1", "Set2", "Set3", "Paired"])
        controls_layout.addWidget(QLabel("Color Palette:"))
        controls_layout.addWidget(self.color_palette_combo)

        # Add randomize colors button
        self.change_colors_button = QPushButton("Randomize Colors")
        controls_layout.addWidget(self.change_colors_button)

        # Add pick colors button
        self.pick_colors_button = QPushButton("Pick Colors")
        post_proc_layout.addWidget(self.pick_colors_button)

        # Add save custom palette button
        self.save_palette_button = QPushButton("Save Custom Palette")
        post_proc_layout.addWidget(self.save_palette_button)

        # Add manage palettes button
        self.manage_palettes_button = QPushButton("Manage Palettes")
        post_proc_layout.addWidget(self.manage_palettes_button)

        # Add display segments button
        self.display_multi_segments_button = QPushButton("Display Segments in Grid")
        post_proc_layout.addWidget(self.display_multi_segments_button)

        # Add show full segmentation button
        self.show_full_segmentation_button = QPushButton("Show Full Segmentation")
        post_proc_layout.addWidget(self.show_full_segmentation_button)
        controls_layout.addWidget(post_proc_group)

        controls_layout.addStretch()

    def setup_sidebar(self, sidebar_layout):
        """Sets up the sidebar controls for the processing page."""
        # Image Actions Section
        upload_group = QGroupBox("Image Actions")
        upload_layout = QVBoxLayout(upload_group)

        self.download_button = QPushButton("Save Segmented Image")
        upload_layout.addWidget(self.download_button)

        self.export_coco_button = QPushButton("Export to COCO Format")
        upload_layout.addWidget(self.export_coco_button)

        self.export_annotations_button = QPushButton("Export Segments as Annotations")
        self.export_annotations_button.clicked.connect(self.show_export_annotations_dialog)
        upload_layout.addWidget(self.export_annotations_button)



        # Image Gallery Group
        gallery_group = QGroupBox("Image Gallery")
        gallery_layout = QVBoxLayout(gallery_group)
        gallery_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        gallery_layout.setSpacing(2)  # Reduce spacing between elements

        # Create the process page gallery
        self.process_gallery = ProcessPageGallery()
        gallery_layout.addWidget(self.process_gallery)

        upload_layout.addWidget(gallery_group)

        sidebar_layout.addWidget(upload_group)

        # Segmentation Parameters
        params_group = QGroupBox("Segmentation Parameters")
        params_layout = QFormLayout(params_group)

        # Training Epochs
        self.train_epoch = QSpinBox()
        self.train_epoch.setRange(1, 1000)
        self.train_epoch.setValue(25)
        params_layout.addRow("Training Epochs:", self.train_epoch)

        # Model Dimensions
        self.mod_dim1 = QSpinBox()
        self.mod_dim1.setRange(1, 100)
        self.mod_dim1.setValue(25)
        params_layout.addRow("Model Dimension 1:", self.mod_dim1)

        self.mod_dim2 = QSpinBox()
        self.mod_dim2.setRange(1, 100)
        self.mod_dim2.setValue(25)
        params_layout.addRow("Model Dimension 2:", self.mod_dim2)

        # Label Number Range
        self.min_label_num = QSpinBox()
        self.min_label_num.setRange(1, 100)
        self.min_label_num.setValue(3)
        params_layout.addRow("Min Label Number:", self.min_label_num)

        self.max_label_num = QSpinBox()
        self.max_label_num.setRange(1, 100)
        self.max_label_num.setValue(25)
        params_layout.addRow("Max Label Number:", self.max_label_num)

        # Target Size
        self.target_size_width = QSpinBox()
        self.target_size_width.setRange(100, 2000)
        self.target_size_width.setValue(750)
        params_layout.addRow("Target Width:", self.target_size_width)

        self.target_size_height = QSpinBox()
        self.target_size_height.setRange(100, 2000)
        self.target_size_height.setValue(750)
        params_layout.addRow("Target Height:", self.target_size_height)

        # Seed for reproducibility
        self.seed_spinbox = QSpinBox()
        self.seed_spinbox.setRange(0, 99999) # Allow a wide range for seed
        self.seed_spinbox.setValue(1943) # Default seed value
        params_layout.addRow("Seed:", self.seed_spinbox)

        # Segmentation Method
        self.segmentation_method = QComboBox()
        self.segmentation_method.addItems(["KMeans", "Felzenszwalb", "PCA"])
        params_layout.addRow("Method:", self.segmentation_method)

        sidebar_layout.addWidget(params_group)

        # Segmentation Controls
        control_group = QGroupBox("Controls")
        control_layout = QVBoxLayout(control_group)

        self.segment_button = QPushButton("Start Segmentation")
        control_layout.addWidget(self.segment_button)

        self.stop_button = QPushButton("Stop Training")
        control_layout.addWidget(self.stop_button)

        # Add Reload Previous Results button
        self.reload_button = QPushButton("Reload Previous Results")
        self.reload_button.setEnabled(False)  # Disabled by default until we know there are results to reload
        control_layout.addWidget(self.reload_button)

        # Progress Bar
        self.progress = QProgressBar()
        self.progress.setMinimumHeight(20)  # Increased height for better visibility
        self.progress.setStyleSheet("QProgressBar { min-height: 20px; font-weight: bold; }")
        control_layout.addWidget(self.progress)

        # Epoch Slider
        slider_layout = QHBoxLayout()
        self.epoch_label = QLabel("Epoch: 0")
        slider_layout.addWidget(self.epoch_label)
        self.epoch_slider = QSlider(Qt.Horizontal)
        self.epoch_slider.setEnabled(False)
        slider_layout.addWidget(self.epoch_slider)
        control_layout.addLayout(slider_layout)

        sidebar_layout.addWidget(control_group)
        sidebar_layout.addStretch()

        # Image gallery is now part of the sidebar