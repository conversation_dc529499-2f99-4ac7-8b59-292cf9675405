import torch
import numpy as np
import cv2
import os
import logging
from PySide6.QtWidgets import QMessageBox
from src.utils.mobilesam_wrapper import get_sam_model_registry, get_sam_predictor, is_available

# Set up logging
logger = logging.getLogger(__name__)

class SAMSegmentationHandler:
    """Class for handling MobileSAM segmentation functionality."""

    def __init__(self, lazy_init=True):
        self.predictor = None
        self.current_image = None
        self.temp_prediction = None
        self._initialized = False

        # Only initialize immediately if lazy_init is False
        if not lazy_init:
            self.setup_mobilesam()

    def setup_mobilesam(self):
        """Sets up the MobileSAM predictor using lazy loading."""
        try:
            # Check if MobileSAM is available
            logger.info("Lazy loading MobileSAM modules...")
            if not is_available():
                logger.error("MobileSAM is not available")
                return False

            # Get the required components using our wrapper
            sam_model_registry = get_sam_model_registry()
            SamPredictor = get_sam_predictor()

            if sam_model_registry is None or SamPredictor is None:
                logger.error("Failed to load MobileSAM components")
                return False

            # Find the checkpoint file
            sam_checkpoint = "D:/Github/VisionLab_Ai_V4/src/weights/mobile_sam.pt"
            if not os.path.exists(sam_checkpoint):
                # Try alternative paths
                alt_paths = [
                    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "weights", "mobile_sam.pt"),
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "weights", "mobile_sam.pt")
                ]
                for path in alt_paths:
                    if os.path.exists(path):
                        sam_checkpoint = path
                        break

            if not os.path.exists(sam_checkpoint):
                logger.error(f"MobileSAM checkpoint not found at: {sam_checkpoint}")
                return False

            model_type = "vit_t"
            device = "cuda" if torch.cuda.is_available() else "cpu"
            logger.info(f"Initializing MobileSAM on {device} with model type {model_type}")

            sam = sam_model_registry[model_type](checkpoint=sam_checkpoint)
            sam.to(device=device)
            sam.eval()  # Important: set to evaluation mode
            self.predictor = SamPredictor(sam)
            logger.info("MobileSAM initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize MobileSAM: {str(e)}")
            return False

    def set_image(self, image):
        """Sets the current image for MobileSAM prediction."""
        # Initialize MobileSAM if not already initialized
        if not self._initialized and self.predictor is None:
            if not self.setup_mobilesam():
                logger.error("Failed to initialize MobileSAM when setting image")
                return False
            self._initialized = True

        if self.predictor is None:
            logger.error("MobileSAM predictor is not available")
            return False

        try:
            self.current_image = image
            self.predictor.set_image(image)
            return True
        except Exception as e:
            logger.error(f"Error setting image for MobileSAM: {str(e)}")
            return False

    def predict_mask(self, input_data, input_type='box'):
        """Predicts mask using MobileSAM with either bounding box or point input.

        Args:
            input_data: Either a bounding box [x1, y1, x2, y2] or a point [x, y]
            input_type: Either 'box' or 'point'
        """
        # Initialize MobileSAM if not already initialized
        if not self._initialized and self.predictor is None:
            if not self.setup_mobilesam():
                logger.error("Failed to initialize MobileSAM when predicting mask")
                return None, None
            self._initialized = True

        if self.predictor is None or self.current_image is None:
            logger.error("MobileSAM predictor or current image is not available")
            return None, None

        try:
            if input_type == 'box':
                # Handle bounding box input
                x1, y1, x2, y2 = input_data
                bbox_array = np.array([min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)])

                masks, scores, _ = self.predictor.predict(
                    point_coords=None,
                    point_labels=None,
                    box=bbox_array[None, :],
                    multimask_output=False
                )
            elif input_type == 'point':
                # Handle point input
                # input_data can be a single point [x, y] or a list of points with labels [(x1, y1, label1), (x2, y2, label2), ...]
                if isinstance(input_data[0], (list, tuple)):
                    # Multiple points with labels
                    points = input_data
                    point_coords = np.array([[x, y] for x, y, _ in points])
                    point_labels = np.array([label for _, _, label in points])
                    logger.debug(f"Using multiple points: {point_coords} with labels: {point_labels}")
                else:
                    # Single point (assumed to be foreground)
                    x, y = input_data
                    point_coords = np.array([[x, y]])
                    point_labels = np.array([1])  # 1 for foreground point
                    logger.debug(f"Using single foreground point: {point_coords}")

                masks, scores, _ = self.predictor.predict(
                    point_coords=point_coords,
                    point_labels=point_labels,
                    box=None,
                    multimask_output=False
                )
            else:
                logger.error(f"Unsupported input type: {input_type}")
                return None, None

            if masks.any():
                mask = masks[0]
                contours, _ = cv2.findContours(
                    mask.astype(np.uint8),
                    cv2.RETR_EXTERNAL,
                    cv2.CHAIN_APPROX_SIMPLE
                )

                if contours:
                    largest_contour = max(contours, key=cv2.contourArea)
                    epsilon = 0.01 * cv2.arcLength(largest_contour, True)
                    approx_polygon = cv2.approxPolyDP(largest_contour, epsilon, True)
                    polygon = [coord for point in approx_polygon for coord in point[0]]

                    return mask, scores[0]

            return None, None

        except Exception as e:
            logger.error(f"Error during MobileSAM prediction: {str(e)}")
            return None, None

    def predict_from_box(self, box):
        """Predict mask from bounding box.

        Args:
            box: Bounding box in format [x1, y1, x2, y2]

        Returns:
            masks, scores, logits
        """
        # Initialize MobileSAM if not already initialized
        if not self._initialized and self.predictor is None:
            if not self.setup_mobilesam():
                logger.error("Failed to initialize MobileSAM when predicting from box")
                return None, None, None
            self._initialized = True

        if self.predictor is None or self.current_image is None:
            logger.error("MobileSAM predictor or current image is not available")
            return None, None, None

        try:
            # Convert box to numpy array
            box_np = np.array(box)

            # Ensure box is in the correct format
            if box_np.shape != (4,):
                box_np = box_np.reshape(4)

            # Add batch dimension
            box_np = box_np[None, :]

            # Predict masks
            masks, scores, logits = self.predictor.predict(
                point_coords=None,
                point_labels=None,
                box=box_np,
                multimask_output=True
            )

            return masks, scores, logits

        except Exception as e:
            logger.error(f"Error predicting from box: {str(e)}")
            return None, None, None

    def predict_from_point(self, x, y, label=1):
        """Predict mask from a single point.

        Args:
            x: X coordinate
            y: Y coordinate
            label: Point label (1 for foreground, 0 for background)

        Returns:
            masks, scores, logits
        """
        # Initialize MobileSAM if not already initialized
        if not self._initialized and self.predictor is None:
            if not self.setup_mobilesam():
                logger.error("Failed to initialize MobileSAM when predicting from point")
                return None, None, None
            self._initialized = True

        if self.predictor is None or self.current_image is None:
            logger.error("MobileSAM predictor or current image is not available")
            return None, None, None

        try:
            # Create point coordinates and labels
            point_coords = np.array([[x, y]])
            point_labels = np.array([label])

            # Predict masks
            masks, scores, logits = self.predictor.predict(
                point_coords=point_coords,
                point_labels=point_labels,
                box=None,
                multimask_output=True
            )

            return masks, scores, logits

        except Exception as e:
            logger.error(f"Error predicting from point: {str(e)}")
            return None, None, None

    def predict_from_points(self, point_coords, point_labels):
        """Predict mask from multiple points.

        Args:
            point_coords: Array of point coordinates, shape (N, 2)
            point_labels: Array of point labels, shape (N,)

        Returns:
            masks, scores, logits
        """
        # Initialize MobileSAM if not already initialized
        if not self._initialized and self.predictor is None:
            if not self.setup_mobilesam():
                logger.error("Failed to initialize MobileSAM when predicting from points")
                return None, None, None
            self._initialized = True

        if self.predictor is None or self.current_image is None:
            logger.error("MobileSAM predictor or current image is not available")
            return None, None, None

        try:
            # Predict masks
            masks, scores, logits = self.predictor.predict(
                point_coords=point_coords,
                point_labels=point_labels,
                box=None,
                multimask_output=True
            )

            return masks, scores, logits

        except Exception as e:
            logger.error(f"Error predicting from multiple points: {str(e)}")
            return None, None, None
