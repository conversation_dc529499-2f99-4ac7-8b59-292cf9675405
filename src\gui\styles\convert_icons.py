"""
Convert SVG icons to PNG format
"""

import os
import cairosvg
import logging

def convert_svg_to_png(svg_path, png_path, width=16, height=16):
    """Convert an SVG file to PNG format."""
    try:
        cairosvg.svg2png(url=svg_path, write_to=png_path, output_width=width, output_height=height)
        return True
    except Exception as e:
        logging.error(f"Error converting {svg_path} to PNG: {e}")
        return False

def main():
    """Convert all SVG icons in the icons directory to PNG format."""
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    icons_dir = os.path.join(script_dir, 'icons')
    
    # Create the icons directory if it doesn't exist
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
    
    # Get all SVG files in the icons directory
    svg_files = [f for f in os.listdir(icons_dir) if f.endswith('.svg')]
    
    # Convert each SVG file to PNG
    for svg_file in svg_files:
        svg_path = os.path.join(icons_dir, svg_file)
        png_path = os.path.join(icons_dir, svg_file.replace('.svg', '.png'))
        
        if convert_svg_to_png(svg_path, png_path):
            print(f"Converted {svg_file} to PNG")
        else:
            print(f"Failed to convert {svg_file} to PNG")

if __name__ == "__main__":
    main()
