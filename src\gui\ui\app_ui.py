# src/gui/ui/app_ui.py

from PySide6.QtWidgets import QPushButton, QWidget
from src.gui.ui.base_ui import BaseUI
from src.gui.ui.menu_ui import MenuUI
from src.gui.ui.toolbar_ui import ToolbarUI
from src.gui.ui.process_page_ui import ProcessPageUI
from src.gui.ui.analysis_page_ui import AnalysisPageUI
from src.gui.ui.settings_page_ui import SettingsPageUI
from src.gui.ui.trainable_segmentation_page_ui import TrainableSegmentationPageUI
from src.gui.ui.ai_assistant_page_ui import AIAssistantPageUI
from src.gui.ui.point_counting_page_ui import PointCountingPageUI
from src.gui.ui.advanced_segmentation_page_ui import AdvancedSegmentationPageUI
from src.gui.project_hub_page import ProjectHubPage

class VisionLabAiAppUI(BaseUI, MenuUI, ToolbarUI, ProcessPageUI, AnalysisPageUI, SettingsPageUI, TrainableSegmentationPageUI, AIAssistantPageUI, PointCountingPageUI, AdvancedSegmentationPageUI):
    """Main UI class that combines all UI components."""

    def __init__(self):
        # Initialize the base UI
        BaseUI.__init__(self)
        self.grain_analysis_btn = None
        self.grain_analysis_page_placeholder = None
        self.point_counting_btn = None

        # Initialize UI elements that will be needed in the main class
        # Original image views
        self.original_image_view = None
        self.segmented_image_view = None
        self.info_frame = None
        self.merge_inner_layout = None
        self.epoch_slider = None
        self.epoch_label = None
        self.progress = None
        self.merge_vars = {} # Added this for access.

        # Buttons to enable/disable
        self.upload_button = None
        self.segment_button = None
        self.change_colors_button = None
        self.pick_colors_button = None
        self.percentage_button = None
        self.download_button = None
        self.merge_button = None
        self.export_coco_button = None
        self.stop_button = None

        # Removed old Analysis page elements

        # Segmentation parameters
        self.train_epoch = None
        self.mod_dim1 = None
        self.mod_dim2 = None
        self.min_label_num = None
        self.max_label_num = None
        self.target_size_width = None
        self.target_size_height = None
        self.segmentation_method = None

        # Setup the UI
        self.setup_ui()

    def setup_ui(self):
        """Sets up the main UI components."""
        # Call the base UI setup
        super().setup_ui()

        # Setup the new toolbar
        self.setup_toolbar()
        self.connect_toolbar_actions()

        # Setup the pages in the requested order:
        # 1. Project Hub (added later via _replace_placeholder_tab in app.py)
        # 2. Unsupervised Segmentation
        self.setup_process_page()
        # 3. Trainable Segmentation
        self.setup_trainable_segmentation_page()
        # 4. Point Counting
        self.setup_point_counting_page()
        # 5. Grain Analysis (placeholder added below, replaced in app.py)
        # 6. Advanced Segmentation
        self.setup_advanced_segmentation_page()
        # 7. Image Lab
        self.setup_analysis_page()
        # 8. Settings
        self.setup_settings_page()
        # AI Assistant (not in the requested order, but keeping it)
        self.setup_ai_assistant_page()

        # Point counting page handler is initialized in app.py, not here
        # to avoid double initialization

        # Initialize trainable segmentation page handlers
        if hasattr(self, 'trainable_segmentation_page'):
            from src.gui.handlers.trainable_segmentation_page_handler import TrainableSegmentationPageHandler
            self.trainable_segmentation_page.handlers = self
            print("DEBUG: Initialized trainable segmentation page handlers")

        # Add a placeholder tab for Grain Analysis initially
        self.grain_analysis_page_placeholder = QWidget()
        self.stacked_widget.addTab(self.grain_analysis_page_placeholder, "Grain Analysis")


        # Set default page
        default_page_index = 0
        self.stacked_widget.setCurrentIndex(default_page_index)

        # Initialize active tab styling for the default page
        if hasattr(self, 'update_active_tab_styling'):
            try:
                default_page_name = self.stacked_widget.tabText(default_page_index)
                self.update_active_tab_styling(default_page_name)
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error initializing active tab styling: {e}")

    def reorder_tabs(self):
        """Reorders the tabs to match the requested order."""
        import logging
        logger = logging.getLogger(__name__)

        # Define the desired tab order
        desired_order = [
            "Project Hub",
            "Unsupervised Segmentation",
            "Trainable Segmentation",
            "Point Counting",
            "Grain Analysis",
            "Advanced Segmentation",
            "AI Assistant",
            "Image Lab",
            "Settings"
        ]

        # Get the current tab order
        current_tabs = {}
        for i in range(self.stacked_widget.count()):
            tab_text = self.stacked_widget.tabText(i)
            current_tabs[tab_text] = self.stacked_widget.widget(i)

        # Log the current tab order
        logger.info(f"Current tab order: {list(current_tabs.keys())}")

        # Remove all tabs (widgets are not deleted, just removed from the tab widget)
        while self.stacked_widget.count() > 0:
            self.stacked_widget.removeTab(0)

        # Add tabs back in the desired order
        for tab_name in desired_order:
            if tab_name in current_tabs:
                self.stacked_widget.addTab(current_tabs[tab_name], tab_name)
                logger.info(f"Added tab: {tab_name}")
            else:
                logger.warning(f"Tab '{tab_name}' not found in current tabs")

        # Log the new tab order
        new_order = [self.stacked_widget.tabText(i) for i in range(self.stacked_widget.count())]
        logger.info(f"New tab order: {new_order}")

    def switch_page(self, index):
        """Switches to the specified page and updates button states."""
        import logging
        logger = logging.getLogger(__name__)

        # Add debug logging
        logger.info(f"DEBUG: UI switch_page called with index {index}")
        logger.info(f"DEBUG: Current index before switch: {self.stacked_widget.currentIndex()}")

        # Check if index is valid
        if index < 0 or index >= self.stacked_widget.count():
            logger.error(f"ERROR: Invalid tab index {index}. Tab count is {self.stacked_widget.count()}")
            return

        # Get tab name before switch
        tab_name_before = self.stacked_widget.tabText(index)
        logger.info(f"DEBUG: Switching to tab: {tab_name_before}")

        # Perform the switch
        self.stacked_widget.setCurrentIndex(index)

        # Verify the switch
        current_index = self.stacked_widget.currentIndex()
        if current_index != index:
            logger.error(f"ERROR: Failed to switch to index {index}. Current index is {current_index}")
            # Try again with blockSignals
            self.stacked_widget.blockSignals(True)
            self.stacked_widget.setCurrentIndex(index)
            self.stacked_widget.blockSignals(False)
            logger.info(f"DEBUG: After second attempt, current index is {self.stacked_widget.currentIndex()}")

        # Get the tab text to identify which page we're on
        current_tab_text = self.stacked_widget.tabText(current_index)

        # Log the page switch
        logger.info(f"Switched to page: {current_tab_text}")

        # Update active tab styling if the method exists
        if hasattr(self, 'update_active_tab_styling'):
            try:
                self.update_active_tab_styling(current_tab_text)
            except Exception as e:
                logger.error(f"Error updating active tab styling: {e}")