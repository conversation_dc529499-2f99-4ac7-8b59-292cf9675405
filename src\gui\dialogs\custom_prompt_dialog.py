# src/gui/dialogs/custom_prompt_dialog.py

import logging
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                              QTextEdit, QPushButton, QListWidget, QMessageBox,
                              QDialogButtonBox, QGroupBox, QSplitter)
from PySide6.QtCore import Qt, Signal

from src.ai_assistant_components.src.gemini.custom_prompts import CustomPromptManager

logger = logging.getLogger(__name__)

class CustomPromptDialog(QDialog):
    """Dialog for managing custom prompt templates."""
    
    prompt_saved = Signal(str, str)  # name, prompt_text
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Custom Prompt Templates")
        self.resize(800, 600)
        
        # Initialize prompt manager
        self.prompt_manager = CustomPromptManager()
        
        self.setup_ui()
        self.load_prompts()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)
        
        # Create a splitter for the left and right sides
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - Prompt list
        left_widget = QGroupBox("Saved Templates")
        left_layout = QVBoxLayout(left_widget)
        
        self.prompt_list = QListWidget()
        self.prompt_list.currentRowChanged.connect(self.on_prompt_selected)
        left_layout.addWidget(self.prompt_list)
        
        # Buttons for managing prompts
        button_layout = QHBoxLayout()
        self.new_button = QPushButton("New")
        self.new_button.clicked.connect(self.on_new_prompt)
        self.delete_button = QPushButton("Delete")
        self.delete_button.clicked.connect(self.on_delete_prompt)
        button_layout.addWidget(self.new_button)
        button_layout.addWidget(self.delete_button)
        left_layout.addLayout(button_layout)
        
        # Right side - Prompt editor
        right_widget = QGroupBox("Edit Template")
        right_layout = QVBoxLayout(right_widget)
        
        # Prompt name
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("Name:"))
        self.name_edit = QLineEdit()
        name_layout.addWidget(self.name_edit)
        right_layout.addLayout(name_layout)
        
        # Prompt text
        right_layout.addWidget(QLabel("Prompt:"))
        self.prompt_edit = QTextEdit()
        right_layout.addWidget(self.prompt_edit)
        
        # Context text
        right_layout.addWidget(QLabel("Context (optional):"))
        self.context_edit = QTextEdit()
        self.context_edit.setPlaceholderText("Add optional context information here. This will be prepended to your prompt when analyzing images.")
        right_layout.addWidget(self.context_edit)
        
        # Save button
        self.save_button = QPushButton("Save Template")
        self.save_button.clicked.connect(self.on_save_prompt)
        right_layout.addWidget(self.save_button)
        
        # Add widgets to splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([200, 600])  # Set initial sizes
        
        main_layout.addWidget(splitter)
        
        # Dialog buttons
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
    
    def load_prompts(self):
        """Load saved prompts into the list."""
        self.prompt_list.clear()
        
        # Load custom prompts
        custom_prompts = self.prompt_manager.get_all_custom_prompts()
        
        # Add to list
        for name in custom_prompts.keys():
            self.prompt_list.addItem(name)
    
    def on_prompt_selected(self, row):
        """Handle prompt selection from the list."""
        if row < 0:
            return
        
        prompt_name = self.prompt_list.item(row).text()
        prompt_data = self.prompt_manager.get_custom_prompt(prompt_name)
        
        if prompt_data:
            self.name_edit.setText(prompt_name)
            self.prompt_edit.setText(prompt_data.get('prompt', ''))
            self.context_edit.setText(prompt_data.get('context', ''))
    
    def on_new_prompt(self):
        """Create a new prompt template."""
        self.name_edit.clear()
        self.prompt_edit.clear()
        self.context_edit.clear()
        self.prompt_list.clearSelection()
    
    def on_delete_prompt(self):
        """Delete the selected prompt template."""
        current_row = self.prompt_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "No Selection", "Please select a prompt template to delete.")
            return
        
        prompt_name = self.prompt_list.item(current_row).text()
        
        # Confirm deletion
        result = QMessageBox.question(
            self, 
            "Confirm Deletion",
            f"Are you sure you want to delete the prompt template '{prompt_name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if result == QMessageBox.StandardButton.Yes:
            success = self.prompt_manager.delete_custom_prompt(prompt_name)
            if success:
                self.load_prompts()
                self.on_new_prompt()
            else:
                QMessageBox.critical(self, "Error", f"Failed to delete prompt template '{prompt_name}'.")
    
    def on_save_prompt(self):
        """Save the current prompt template."""
        name = self.name_edit.text().strip()
        prompt_text = self.prompt_edit.toPlainText().strip()
        context = self.context_edit.toPlainText().strip()
        
        if not name:
            QMessageBox.warning(self, "Missing Name", "Please enter a name for the prompt template.")
            return
        
        if not prompt_text:
            QMessageBox.warning(self, "Missing Prompt", "Please enter prompt text.")
            return
        
        # Check if overwriting existing prompt
        existing_names = [self.prompt_list.item(i).text() for i in range(self.prompt_list.count())]
        if name in existing_names and self.prompt_list.currentItem() and self.prompt_list.currentItem().text() != name:
            result = QMessageBox.question(
                self, 
                "Confirm Overwrite",
                f"A prompt template with the name '{name}' already exists. Do you want to overwrite it?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if result != QMessageBox.StandardButton.Yes:
                return
        
        # Save the prompt
        success = self.prompt_manager.save_custom_prompt(name, prompt_text, context)
        
        if success:
            self.load_prompts()
            
            # Select the saved prompt
            for i in range(self.prompt_list.count()):
                if self.prompt_list.item(i).text() == name:
                    self.prompt_list.setCurrentRow(i)
                    break
            
            # Emit signal
            self.prompt_saved.emit(name, prompt_text)
            
            QMessageBox.information(self, "Success", f"Prompt template '{name}' saved successfully.")
        else:
            QMessageBox.critical(self, "Error", f"Failed to save prompt template '{name}'.")
