# src/gui/handlers/settings_handlers.py

import os
import json
import logging
from PySide6.QtWidgets import QMessageBox, QFileDialog, QColorDialog
from PySide6.QtCore import QSettings
from PySide6.QtGui import QColor

# Import the settings manager
try:
    from src.utils.settings_manager import settings_manager
except ImportError:
    settings_manager = None

logger = logging.getLogger(__name__)

class SettingsHandlers:
    """Class for handling settings-related operations."""

    def __init__(self):
        # Define the settings file path
        self.settings_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'settings.json')
        self.default_settings = {
            'app_settings': {
                'theme': 'Dark Theme',
                'color_scheme': 'Default',
                'font_family': 'Segoe UI',
                'font_size': 'normal',
                'language': 'English'
            },
            'segmentation_settings': {
                'default_method': 'KMeans',
                'default_epochs': 100,
                'default_min_labels': 3,
                'default_max_labels': 10,
                'default_color_palette': 'Default'
            },
            'trainable_segmentation_settings': {
                'default_intensity_features': True,
                'default_edge_features': False,
                'default_texture_features': True,
                'default_sigma_min': 1,
                'default_sigma_max': 16,
                'default_n_estimators': 100,
                'default_max_depth': 10,
                'default_max_samples': 0.7,
                'default_brush_size': 5
            },
            'grain_analysis_settings': {
                'default_input_size': 1024,
                'default_iou_threshold': 0.7,
                'default_conf_threshold': 0.5,
                'default_max_det': 500,
                # Include obsolete NMS parameters with default values to avoid KeyError
                'default_containment_threshold': 0.7,
                'default_size_ratio': 0.1,
                'default_border_penalty': 1.0,
                'default_angle_threshold': 5.0,
                'default_straight_edge_ratio': 0.25,
                'default_artifact_preset': 'Balanced',
                'default_artifact_sensitivity': 0.5,
                'default_duplicate_sensitivity': 0.7
                # Note: Advanced performance settings removed for better performance
                # Using hardcoded optimized defaults: timeout=60s, batch_size=100, max_pairs=10000
            },
            'mobilesam_settings': {
                'default_points_per_side': 32,
                'default_pred_iou_thresh': 0.88,
                'default_stability_score_thresh': 0.95,
                'default_box_nms_thresh': 0.3,
                'default_min_mask_area': 0
            },
            # YOLOv8 settings have been removed
            'ai_assistant_settings': {
                'default_gemini_model': 'gemini-2.5-flash-preview-04-17',
                'prompt_templates': ''
            },
            'point_counting_settings': {
                'grid_color_r': 255,
                'grid_color_g': 255,
                'grid_color_b': 255,
                'grid_color_a': 180,
                'grid_opacity': 70
            },
            'image_settings': {
                'default_width': 750,
                'default_height': 750,
                'auto_resize': True
            },
            'export_settings': {
                'export_dir': '',
                'export_format': 'PNG'
            }
        }

    def update_processing_page(self):
        """Updates the processing page parameters based on current settings."""
        # Update segmentation parameters
        self.segmentation_method.setCurrentText(self.default_seg_method.currentText())
        self.train_epoch.setValue(self.default_epochs.value())
        self.min_label_num.setValue(self.default_min_labels.value())
        self.max_label_num.setValue(self.default_max_labels.value())

        # Update image dimensions
        self.target_size_width.setValue(self.default_img_width.value())
        self.target_size_height.setValue(self.default_img_height.value())

    def update_trainable_segmentation_page(self):
        """Updates the trainable segmentation page parameters based on current settings."""
        # Check if the trainable segmentation page UI elements exist
        if not hasattr(self, 'intensity_checkbox') or not hasattr(self, 'edges_checkbox') or not hasattr(self, 'texture_checkbox'):
            return

        # Update feature selection checkboxes
        self.intensity_checkbox.setChecked(self.default_intensity_features.isChecked())
        self.edges_checkbox.setChecked(self.default_edge_features.isChecked())
        self.texture_checkbox.setChecked(self.default_texture_features.isChecked())

        # Update sigma parameters
        self.sigma_min_spinbox.setValue(self.default_sigma_min.value())
        self.sigma_max_spinbox.setValue(self.default_sigma_max.value())

        # Update classifier parameters
        self.n_estimators_spinbox.setValue(self.default_n_estimators.value())
        self.max_depth_spinbox.setValue(self.default_max_depth.value())
        self.max_samples_spinbox.setValue(self.default_max_samples.value())

        # Update brush size
        if hasattr(self, 'brush_size_slider'):
            self.brush_size_slider.setValue(self.default_brush_size.value())
            # Update the brush size label if it exists
            if hasattr(self, 'brush_size_label'):
                self.brush_size_label.setText(str(self.default_brush_size.value()))

    def update_grain_analysis_page(self):
        """Updates the grain analysis page parameters based on current settings."""
        # Check if the grain analysis widget exists
        if not hasattr(self, 'grain_analysis_widget'):
            return

        # Update segmentation parameters
        if hasattr(self.grain_analysis_widget, 'input_size'):
            self.grain_analysis_widget.input_size.setValue(self.default_input_size.value())

        if hasattr(self.grain_analysis_widget, 'iou_threshold'):
            # Convert from 0-1 to 0-100 for the slider
            self.grain_analysis_widget.iou_threshold.setValue(int(self.default_iou_threshold.value() * 100))

        if hasattr(self.grain_analysis_widget, 'conf_threshold'):
            # Convert from 0-1 to 0-100 for the slider
            self.grain_analysis_widget.conf_threshold.setValue(int(self.default_conf_threshold.value() * 100))

        if hasattr(self.grain_analysis_widget, 'max_det'):
            self.grain_analysis_widget.max_det.setText(str(self.default_max_det.value()))

        # NMS parameters have been removed and replaced by artifact handling

        # Update subgrain filtering parameters
        if hasattr(self.grain_analysis_widget, 'subgrain_angle_threshold'):
            self.grain_analysis_widget.subgrain_angle_threshold.setValue(int(self.default_angle_threshold.value()))

        # Update artifact handling parameters
        if hasattr(self.grain_analysis_widget, 'preset_combo'):
            preset_index = self.grain_analysis_widget.preset_combo.findText(self.default_artifact_preset.currentText())
            if preset_index >= 0:
                self.grain_analysis_widget.preset_combo.setCurrentIndex(preset_index)

        if hasattr(self.grain_analysis_widget, 'artifact_sensitivity_slider'):
            # Convert from 0-1 to 0-100 for the slider
            self.grain_analysis_widget.artifact_sensitivity_slider.setValue(int(self.default_artifact_sensitivity.value() * 100))

        if hasattr(self.grain_analysis_widget, 'duplicate_sensitivity_slider'):
            # Convert from 0-1 to 0-100 for the slider
            self.grain_analysis_widget.duplicate_sensitivity_slider.setValue(int(self.default_duplicate_sensitivity.value() * 100))

        if hasattr(self.grain_analysis_widget, 'straight_edge_ratio_slider'):
            # Convert from 0-1 to 0-100 for the slider
            self.grain_analysis_widget.straight_edge_ratio_slider.setValue(int(self.default_straight_edge_ratio.value() * 100))

    def update_mobilesam_settings(self):
        """Updates the MobileSAM parameters based on current settings."""
        # Check if the grain analysis widget exists (which contains MobileSAM parameters)
        if not hasattr(self, 'grain_analysis_widget'):
            return

        # Update MobileSAM parameters
        if hasattr(self.grain_analysis_widget, 'points_per_side'):
            self.grain_analysis_widget.points_per_side.setValue(self.default_points_per_side.value())

        if hasattr(self.grain_analysis_widget, 'pred_iou_thresh'):
            # Convert from 0-1 to 0-100 for the slider
            self.grain_analysis_widget.pred_iou_thresh.setValue(int(self.default_pred_iou_thresh.value() * 100))

        if hasattr(self.grain_analysis_widget, 'stability_score_thresh'):
            # Convert from 0-1 to 0-100 for the slider
            self.grain_analysis_widget.stability_score_thresh.setValue(int(self.default_stability_score_thresh.value() * 100))

        if hasattr(self.grain_analysis_widget, 'box_nms_thresh'):
            # Convert from 0-1 to 0-100 for the slider
            self.grain_analysis_widget.box_nms_thresh.setValue(int(self.default_box_nms_thresh.value() * 100))

        # Update min mask area if it exists
        if hasattr(self.grain_analysis_widget, 'min_mask_area'):
            self.grain_analysis_widget.min_mask_area.setValue(self.default_min_mask_area.value())

    def update_yolov8_settings(self):
        """Updates the YOLOv8 parameters based on current settings."""
        # YOLOv8 settings have been removed, but we keep this method for compatibility
        # This method is now a no-op
        pass

    def update_ai_assistant_settings(self):
        """Updates the AI Assistant parameters based on current settings."""
        # Update the Gemini model in the settings
        try:
            from src.gui.dialogs.gemini_settings_dialog import GeminiSettingsDialog
            from src.ai_assistant_components.src.gemini.custom_prompts import CustomPromptManager

            # Update the model in the QSettings
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            settings.setValue("gemini/model", self.default_gemini_model.currentText())
            settings.sync()  # Force settings to be written to disk immediately

            # Update the prompt templates if they exist
            if hasattr(self, 'prompt_templates_text') and self.prompt_templates_text.toPlainText():
                # Parse the prompt templates and save them as custom prompts
                prompt_manager = CustomPromptManager()
                templates = self.prompt_templates_text.toPlainText().strip().split('\n')

                for i, template in enumerate(templates):
                    if template.strip():
                        # Save each line as a separate prompt template
                        prompt_name = f"Custom Template {i+1}"
                        prompt_manager.save_custom_prompt(prompt_name, template)

            # If the AI Assistant worker exists, update its model
            if hasattr(self, 'ai_assistant_gemini_worker') and self.ai_assistant_gemini_worker:
                self.ai_assistant_gemini_worker.model_name = self.default_gemini_model.currentText()
        except Exception as e:
            print(f"Error updating AI Assistant settings: {e}")

    def save_settings(self):
        """Saves the current settings to QSettings and a JSON file for backup."""
        try:
            # Use the settings manager if available
            if settings_manager is not None:
                # App settings
                settings_manager.set_value("app/theme", self.theme_combo.currentText())
                settings_manager.set_value("app/color_scheme", self.color_scheme_combo.currentText())
                settings_manager.set_value("app/font_family", self.font_family_combo.currentText())
                settings_manager.set_value("app/font_size", self.font_size_combo.currentText())
                settings_manager.set_value("app/language", self.language_combo.currentText())

                # Segmentation settings
                settings_manager.set_value("segmentation/default_method", self.default_seg_method.currentText())
                settings_manager.set_value("segmentation/default_epochs", self.default_epochs.value())
                settings_manager.set_value("segmentation/default_min_labels", self.default_min_labels.value())
                settings_manager.set_value("segmentation/default_max_labels", self.default_max_labels.value())
                settings_manager.set_value("segmentation/default_color_palette", self.default_color_palette.currentText())

                # Trainable segmentation settings
                settings_manager.set_value("trainable_segmentation/default_intensity_features", self.default_intensity_features.isChecked())
                settings_manager.set_value("trainable_segmentation/default_edge_features", self.default_edge_features.isChecked())
                settings_manager.set_value("trainable_segmentation/default_texture_features", self.default_texture_features.isChecked())
                settings_manager.set_value("trainable_segmentation/default_sigma_min", self.default_sigma_min.value())
                settings_manager.set_value("trainable_segmentation/default_sigma_max", self.default_sigma_max.value())
                settings_manager.set_value("trainable_segmentation/default_n_estimators", self.default_n_estimators.value())
                settings_manager.set_value("trainable_segmentation/default_max_depth", self.default_max_depth.value())
                settings_manager.set_value("trainable_segmentation/default_max_samples", self.default_max_samples.value())
                settings_manager.set_value("trainable_segmentation/default_brush_size", self.default_brush_size.value())

                # Grain analysis settings
                settings_manager.set_value("grain_analysis/default_input_size", self.default_input_size.value())
                settings_manager.set_value("grain_analysis/default_iou_threshold", self.default_iou_threshold.value())
                settings_manager.set_value("grain_analysis/default_conf_threshold", self.default_conf_threshold.value())
                settings_manager.set_value("grain_analysis/default_max_det", self.default_max_det.value())
                # Removed obsolete NMS parameters
                settings_manager.set_value("grain_analysis/default_angle_threshold", self.default_angle_threshold.value())
                settings_manager.set_value("grain_analysis/default_straight_edge_ratio", self.default_straight_edge_ratio.value())

                # Artifact handling settings
                settings_manager.set_value("grain_analysis/default_artifact_preset", self.default_artifact_preset.currentText())
                settings_manager.set_value("grain_analysis/default_artifact_sensitivity", self.default_artifact_sensitivity.value())
                settings_manager.set_value("grain_analysis/default_duplicate_sensitivity", self.default_duplicate_sensitivity.value())

                # MobileSAM settings
                settings_manager.set_value("mobilesam/default_points_per_side", self.default_points_per_side.value())
                settings_manager.set_value("mobilesam/default_pred_iou_thresh", self.default_pred_iou_thresh.value())
                settings_manager.set_value("mobilesam/default_stability_score_thresh", self.default_stability_score_thresh.value())
                settings_manager.set_value("mobilesam/default_box_nms_thresh", self.default_box_nms_thresh.value())
                settings_manager.set_value("mobilesam/default_min_mask_area", self.default_min_mask_area.value())

                # YOLOv8 settings have been removed

                # AI Assistant settings
                settings_manager.set_value("ai_assistant/default_gemini_model", self.default_gemini_model.currentText())
                settings_manager.set_value("ai_assistant/prompt_templates", self.prompt_templates_text.toPlainText())

                # Point Counting settings
                # Extract color from stylesheet
                import re
                current_style = self.default_grid_color_button.styleSheet()
                rgba_match = re.search(r'rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d+)\)', current_style)
                if rgba_match:
                    r, g, b, a = map(int, rgba_match.groups())
                    settings_manager.set_value("point_counting/grid_color_r", r)
                    settings_manager.set_value("point_counting/grid_color_g", g)
                    settings_manager.set_value("point_counting/grid_color_b", b)
                    settings_manager.set_value("point_counting/grid_color_a", a)
                settings_manager.set_value("point_counting/grid_opacity", self.default_grid_opacity.value())

                # Image settings
                settings_manager.set_value("image/default_width", self.default_img_width.value())
                settings_manager.set_value("image/default_height", self.default_img_height.value())
                settings_manager.set_value("image/auto_resize", self.auto_resize.isChecked())

                # Export settings
                settings_manager.set_value("export/directory", self.export_dir.text())
                settings_manager.set_value("export/format", self.export_format.currentText())

                # Create a backup of the settings
                success, backup_file = settings_manager.backup_settings()
                if success:
                    logger.info(f"Settings backed up to {backup_file}")
            else:
                # Fallback to the old method if settings_manager is not available
                # Collect settings from UI elements
                settings = {
                    'app_settings': {
                        'theme': self.theme_combo.currentText(),
                        'color_scheme': self.color_scheme_combo.currentText(),
                        'font_family': self.font_family_combo.currentText(),
                        'font_size': self.font_size_combo.currentText(),
                        'language': self.language_combo.currentText()
                    },
                    'segmentation_settings': {
                        'default_method': self.default_seg_method.currentText(),
                        'default_epochs': self.default_epochs.value(),
                        'default_min_labels': self.default_min_labels.value(),
                        'default_max_labels': self.default_max_labels.value(),
                        'default_color_palette': self.default_color_palette.currentText()
                    },
                    'trainable_segmentation_settings': {
                        'default_intensity_features': self.default_intensity_features.isChecked(),
                        'default_edge_features': self.default_edge_features.isChecked(),
                        'default_texture_features': self.default_texture_features.isChecked(),
                        'default_sigma_min': self.default_sigma_min.value(),
                        'default_sigma_max': self.default_sigma_max.value(),
                        'default_n_estimators': self.default_n_estimators.value(),
                        'default_max_depth': self.default_max_depth.value(),
                        'default_max_samples': self.default_max_samples.value(),
                        'default_brush_size': self.default_brush_size.value()
                    },
                    'grain_analysis_settings': {
                        'default_input_size': self.default_input_size.value(),
                        'default_iou_threshold': self.default_iou_threshold.value(),
                        'default_conf_threshold': self.default_conf_threshold.value(),
                        'default_max_det': self.default_max_det.value(),
                        'default_angle_threshold': self.default_angle_threshold.value(),
                        'default_straight_edge_ratio': self.default_straight_edge_ratio.value(),
                        'default_artifact_preset': self.default_artifact_preset.currentText(),
                        'default_artifact_sensitivity': self.default_artifact_sensitivity.value(),
                        'default_duplicate_sensitivity': self.default_duplicate_sensitivity.value()
                    },
                    'mobilesam_settings': {
                        'default_points_per_side': self.default_points_per_side.value(),
                        'default_pred_iou_thresh': self.default_pred_iou_thresh.value(),
                        'default_stability_score_thresh': self.default_stability_score_thresh.value(),
                        'default_box_nms_thresh': self.default_box_nms_thresh.value(),
                        'default_min_mask_area': self.default_min_mask_area.value()
                    },
                    'yolov8_settings': {
                        'default_yolo_conf_threshold': self.default_yolo_conf_threshold.value(),
                        'default_yolo_iou_threshold': self.default_yolo_iou_threshold.value(),
                        'default_yolo_input_size': self.default_yolo_input_size.value(),
                        'default_yolo_max_det': self.default_yolo_max_det.value()
                    },
                    'ai_assistant_settings': {
                        'default_gemini_model': self.default_gemini_model.currentText(),
                        'prompt_templates': self.prompt_templates_text.toPlainText()
                    },
                    'image_settings': {
                        'default_width': self.default_img_width.value(),
                        'default_height': self.default_img_height.value(),
                        'auto_resize': self.auto_resize.isChecked()
                    },
                    'export_settings': {
                        'export_dir': self.export_dir.text(),
                        'export_format': self.export_format.currentText()
                    }
                }

                # Save settings to file
                with open(self.settings_file, 'w') as f:
                    json.dump(settings, f, indent=4)

            # Update page parameters
            self.update_processing_page()
            self.update_trainable_segmentation_page()
            self.update_grain_analysis_page()
            self.update_mobilesam_settings()
            self.update_yolov8_settings()
            self.update_ai_assistant_settings()

            # Don't automatically apply theme when saving settings
            # This prevents the theme from being applied during application closure
            # The theme will be applied when the user clicks the "Apply Theme" button
            # or when the application starts up

            # Show success message only if not called during application shutdown
            # Check if we're in a closeEvent by looking at the call stack
            import inspect
            caller_frames = inspect.stack()
            in_close_event = any('closeEvent' in frame.function for frame in caller_frames)

            if not in_close_event:
                QMessageBox.information(self, "Settings Saved", "Your settings have been saved successfully.")

            return True
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to save settings: {str(e)}")
            return False

    def load_settings(self):
        """Loads settings from QSettings or JSON file and applies them to the UI."""
        try:
            # Use the settings manager if available
            if settings_manager is not None:
                # Load settings from QSettings
                # App settings
                theme = settings_manager.get_value("app/theme", self.default_settings['app_settings']['theme'])
                color_scheme = settings_manager.get_value("app/color_scheme", self.default_settings['app_settings']['color_scheme'])
                font_family = settings_manager.get_value("app/font_family", self.default_settings['app_settings']['font_family'])
                font_size = settings_manager.get_value("app/font_size", self.default_settings['app_settings']['font_size'])
                language = settings_manager.get_value("app/language", self.default_settings['app_settings']['language'])

                # Create a settings dictionary for UI updates
                settings = {
                    'app_settings': {
                        'theme': theme,
                        'color_scheme': color_scheme,
                        'font_family': font_family,
                        'font_size': font_size,
                        'language': language
                    },
                    'segmentation_settings': {
                        'default_method': settings_manager.get_value("segmentation/default_method", self.default_settings['segmentation_settings']['default_method']),
                        'default_epochs': int(settings_manager.get_value("segmentation/default_epochs", self.default_settings['segmentation_settings']['default_epochs'])),
                        'default_min_labels': int(settings_manager.get_value("segmentation/default_min_labels", self.default_settings['segmentation_settings']['default_min_labels'])),
                        'default_max_labels': int(settings_manager.get_value("segmentation/default_max_labels", self.default_settings['segmentation_settings']['default_max_labels'])),
                        'default_color_palette': settings_manager.get_value("segmentation/default_color_palette", self.default_settings['segmentation_settings']['default_color_palette'])
                    },
                    'trainable_segmentation_settings': {
                        'default_intensity_features': settings_manager.get_value("trainable_segmentation/default_intensity_features", self.default_settings['trainable_segmentation_settings']['default_intensity_features']) == 'true',
                        'default_edge_features': settings_manager.get_value("trainable_segmentation/default_edge_features", self.default_settings['trainable_segmentation_settings']['default_edge_features']) == 'true',
                        'default_texture_features': settings_manager.get_value("trainable_segmentation/default_texture_features", self.default_settings['trainable_segmentation_settings']['default_texture_features']) == 'true',
                        'default_sigma_min': int(settings_manager.get_value("trainable_segmentation/default_sigma_min", self.default_settings['trainable_segmentation_settings']['default_sigma_min'])),
                        'default_sigma_max': int(settings_manager.get_value("trainable_segmentation/default_sigma_max", self.default_settings['trainable_segmentation_settings']['default_sigma_max'])),
                        'default_n_estimators': int(settings_manager.get_value("trainable_segmentation/default_n_estimators", self.default_settings['trainable_segmentation_settings']['default_n_estimators'])),
                        'default_max_depth': int(settings_manager.get_value("trainable_segmentation/default_max_depth", self.default_settings['trainable_segmentation_settings']['default_max_depth'])),
                        'default_max_samples': float(settings_manager.get_value("trainable_segmentation/default_max_samples", self.default_settings['trainable_segmentation_settings']['default_max_samples'])),
                        'default_brush_size': int(settings_manager.get_value("trainable_segmentation/default_brush_size", self.default_settings['trainable_segmentation_settings']['default_brush_size']))
                    },
                    'grain_analysis_settings': {
                        'default_input_size': int(settings_manager.get_value("grain_analysis/default_input_size", self.default_settings['grain_analysis_settings']['default_input_size'])),
                        'default_iou_threshold': float(settings_manager.get_value("grain_analysis/default_iou_threshold", self.default_settings['grain_analysis_settings']['default_iou_threshold'])),
                        'default_conf_threshold': float(settings_manager.get_value("grain_analysis/default_conf_threshold", self.default_settings['grain_analysis_settings']['default_conf_threshold'])),
                        'default_max_det': int(settings_manager.get_value("grain_analysis/default_max_det", self.default_settings['grain_analysis_settings']['default_max_det'])),
                        'default_angle_threshold': float(settings_manager.get_value("grain_analysis/default_angle_threshold", self.default_settings['grain_analysis_settings']['default_angle_threshold'])),
                        'default_straight_edge_ratio': float(settings_manager.get_value("grain_analysis/default_straight_edge_ratio", self.default_settings['grain_analysis_settings']['default_straight_edge_ratio'])),
                        'default_artifact_preset': settings_manager.get_value("grain_analysis/default_artifact_preset", self.default_settings['grain_analysis_settings']['default_artifact_preset']),
                        'default_artifact_sensitivity': float(settings_manager.get_value("grain_analysis/default_artifact_sensitivity", self.default_settings['grain_analysis_settings']['default_artifact_sensitivity'])),
                        'default_duplicate_sensitivity': float(settings_manager.get_value("grain_analysis/default_duplicate_sensitivity", self.default_settings['grain_analysis_settings']['default_duplicate_sensitivity'])),
                        # Add default values for obsolete parameters to avoid KeyError
                        'default_containment_threshold': 0.7,
                        'default_size_ratio': 0.1,
                        'default_border_penalty': 1.0
                    },
                    'mobilesam_settings': {
                        'default_points_per_side': int(settings_manager.get_value("mobilesam/default_points_per_side", self.default_settings['mobilesam_settings']['default_points_per_side'])),
                        'default_pred_iou_thresh': float(settings_manager.get_value("mobilesam/default_pred_iou_thresh", self.default_settings['mobilesam_settings']['default_pred_iou_thresh'])),
                        'default_stability_score_thresh': float(settings_manager.get_value("mobilesam/default_stability_score_thresh", self.default_settings['mobilesam_settings']['default_stability_score_thresh'])),
                        'default_box_nms_thresh': float(settings_manager.get_value("mobilesam/default_box_nms_thresh", self.default_settings['mobilesam_settings']['default_box_nms_thresh'])),
                        'default_min_mask_area': int(settings_manager.get_value("mobilesam/default_min_mask_area", self.default_settings['mobilesam_settings']['default_min_mask_area']))
                    },
                    # YOLOv8 settings have been removed
                    'ai_assistant_settings': {
                        'default_gemini_model': settings_manager.get_value("ai_assistant/default_gemini_model", self.default_settings['ai_assistant_settings']['default_gemini_model']),
                        'prompt_templates': settings_manager.get_value("ai_assistant/prompt_templates", self.default_settings['ai_assistant_settings']['prompt_templates'])
                    },
                    'point_counting_settings': {
                        'grid_color_r': int(settings_manager.get_value("point_counting/grid_color_r", 255)),
                        'grid_color_g': int(settings_manager.get_value("point_counting/grid_color_g", 255)),
                        'grid_color_b': int(settings_manager.get_value("point_counting/grid_color_b", 255)),
                        'grid_color_a': int(settings_manager.get_value("point_counting/grid_color_a", 180)),
                        'grid_opacity': int(settings_manager.get_value("point_counting/grid_opacity", 70))
                    },
                    'image_settings': {
                        'default_width': int(settings_manager.get_value("image/default_width", self.default_settings['image_settings']['default_width'])),
                        'default_height': int(settings_manager.get_value("image/default_height", self.default_settings['image_settings']['default_height'])),
                        'auto_resize': settings_manager.get_value("image/auto_resize", self.default_settings['image_settings']['auto_resize']) == 'true'
                    },
                    'export_settings': {
                        'export_dir': settings_manager.get_value("export/directory", self.default_settings['export_settings']['export_dir']),
                        'export_format': settings_manager.get_value("export/format", self.default_settings['export_settings']['export_format'])
                    }
                }
            else:
                # Fallback to the old method if settings_manager is not available
                # If settings file exists, load it
                if os.path.exists(self.settings_file):
                    with open(self.settings_file, 'r') as f:
                        settings = json.load(f)
                else:
                    # Use default settings if file doesn't exist
                    settings = self.default_settings

            # Apply settings to UI elements
            # App settings
            self.theme_combo.setCurrentText(settings['app_settings']['theme'])

            # Set color scheme if available
            if 'color_scheme' in settings['app_settings']:
                self.color_scheme_combo.setCurrentText(settings['app_settings']['color_scheme'])

            # Set font family if available
            if 'font_family' in settings['app_settings']:
                self.font_family_combo.setCurrentText(settings['app_settings']['font_family'])

            # Set font size if available
            if 'font_size' in settings['app_settings']:
                self.font_size_combo.setCurrentText(settings['app_settings']['font_size'])

            self.language_combo.setCurrentText(settings['app_settings']['language'])

            # Load UI style parameters if they exist
            try:
                from src.gui.styles.theme_config import UI_STYLE_PARAMS
                # Load each style parameter if it exists in settings
                for key in UI_STYLE_PARAMS.keys():
                    value = settings_manager.get_value(f"app/style/{key}", None) if settings_manager else None
                    if value is not None:
                        # Set the corresponding slider value if it exists
                        slider_name = f"{key.replace('-', '_')}_slider"
                        if hasattr(self, slider_name):
                            slider = getattr(self, slider_name)
                            slider.setValue(int(value))
                            # Update the corresponding value label if it exists
                            value_label_name = f"{key.replace('-', '_')}_value"
                            if hasattr(self, value_label_name):
                                getattr(self, value_label_name).setText(str(value))

                # Ensure section styling parameters are loaded
                # Handle border width separately as it uses a spinner
                border_width = settings_manager.get_value("app/style/section-border-width", None) if settings_manager else None
                if border_width is not None and hasattr(self, 'section_border_width_spinner'):
                    self.section_border_width_spinner.setValue(int(border_width))

                # Handle border opacity separately
                border_opacity = settings_manager.get_value("app/style/section-border-opacity", None) if settings_manager else None
                if border_opacity is not None and hasattr(self, 'section_border_opacity_slider'):
                    self.section_border_opacity_slider.setValue(int(border_opacity))
                    self.section_border_opacity_value.setText(str(border_opacity))

                # Handle other section parameters that use sliders
                section_params = [
                    "section-border-radius",
                    "section-gradient-strength"
                ]

                for param in section_params:
                    value = settings_manager.get_value(f"app/style/{param}", None) if settings_manager else None
                    if value is not None:
                        slider_name = f"{param.replace('-', '_')}_slider"
                        if hasattr(self, slider_name):
                            slider = getattr(self, slider_name)
                            slider.setValue(int(value))
                            # Update the corresponding value label
                            value_label_name = f"{param.replace('-', '_')}_value"
                            if hasattr(self, value_label_name):
                                getattr(self, value_label_name).setText(str(value))
            except (ImportError, Exception) as e:
                logger.error(f"Error loading UI style parameters: {e}")

            # Update theme preview
            try:
                if hasattr(self, 'update_theme_preview') and callable(self.update_theme_preview):
                    self.update_theme_preview()
            except Exception as e:
                logger.error(f"Error updating theme preview: {e}")

            # Segmentation settings
            self.default_seg_method.setCurrentText(settings['segmentation_settings']['default_method'])
            self.default_epochs.setValue(settings['segmentation_settings']['default_epochs'])
            self.default_min_labels.setValue(settings['segmentation_settings']['default_min_labels'])
            self.default_max_labels.setValue(settings['segmentation_settings']['default_max_labels'])
            self.default_color_palette.setCurrentText(settings['segmentation_settings']['default_color_palette'])

            # Trainable Segmentation settings
            if 'trainable_segmentation_settings' in settings:
                self.default_intensity_features.setChecked(settings['trainable_segmentation_settings']['default_intensity_features'])
                self.default_edge_features.setChecked(settings['trainable_segmentation_settings']['default_edge_features'])
                self.default_texture_features.setChecked(settings['trainable_segmentation_settings']['default_texture_features'])
                self.default_sigma_min.setValue(settings['trainable_segmentation_settings']['default_sigma_min'])
                self.default_sigma_max.setValue(settings['trainable_segmentation_settings']['default_sigma_max'])
                self.default_n_estimators.setValue(settings['trainable_segmentation_settings']['default_n_estimators'])
                self.default_max_depth.setValue(settings['trainable_segmentation_settings']['default_max_depth'])
                self.default_max_samples.setValue(settings['trainable_segmentation_settings']['default_max_samples'])
                self.default_brush_size.setValue(settings['trainable_segmentation_settings']['default_brush_size'])

            # Grain Analysis settings
            if 'grain_analysis_settings' in settings:
                self.default_input_size.setValue(settings['grain_analysis_settings']['default_input_size'])
                self.default_iou_threshold.setValue(settings['grain_analysis_settings']['default_iou_threshold'])
                self.default_conf_threshold.setValue(settings['grain_analysis_settings']['default_conf_threshold'])
                self.default_max_det.setValue(settings['grain_analysis_settings']['default_max_det'])
                # Removed obsolete NMS parameters
                self.default_angle_threshold.setValue(settings['grain_analysis_settings']['default_angle_threshold'])
                self.default_straight_edge_ratio.setValue(settings['grain_analysis_settings']['default_straight_edge_ratio'])

                # Artifact handling settings if they exist
                if 'default_artifact_preset' in settings['grain_analysis_settings']:
                    preset_index = self.default_artifact_preset.findText(settings['grain_analysis_settings']['default_artifact_preset'])
                    if preset_index >= 0:
                        self.default_artifact_preset.setCurrentIndex(preset_index)

                if 'default_artifact_sensitivity' in settings['grain_analysis_settings']:
                    self.default_artifact_sensitivity.setValue(settings['grain_analysis_settings']['default_artifact_sensitivity'])

                if 'default_duplicate_sensitivity' in settings['grain_analysis_settings']:
                    self.default_duplicate_sensitivity.setValue(settings['grain_analysis_settings']['default_duplicate_sensitivity'])

            # MobileSAM settings
            if 'mobilesam_settings' in settings:
                self.default_points_per_side.setValue(settings['mobilesam_settings']['default_points_per_side'])
                self.default_pred_iou_thresh.setValue(settings['mobilesam_settings']['default_pred_iou_thresh'])
                self.default_stability_score_thresh.setValue(settings['mobilesam_settings']['default_stability_score_thresh'])
                self.default_box_nms_thresh.setValue(settings['mobilesam_settings']['default_box_nms_thresh'])
                self.default_min_mask_area.setValue(settings['mobilesam_settings']['default_min_mask_area'])

            # YOLOv8 settings have been removed

            # AI Assistant settings
            if 'ai_assistant_settings' in settings:
                self.default_gemini_model.setCurrentText(settings['ai_assistant_settings']['default_gemini_model'])
                self.prompt_templates_text.setPlainText(settings['ai_assistant_settings'].get('prompt_templates', ''))

            # Point Counting settings
            if 'point_counting_settings' in settings:
                r = settings['point_counting_settings']['grid_color_r']
                g = settings['point_counting_settings']['grid_color_g']
                b = settings['point_counting_settings']['grid_color_b']
                a = settings['point_counting_settings']['grid_color_a']
                self.default_grid_color_button.setStyleSheet(f"background-color: rgba({r}, {g}, {b}, {a});")
                self.default_grid_opacity.setValue(settings['point_counting_settings']['grid_opacity'])
                self.default_grid_opacity_value.setText(str(settings['point_counting_settings']['grid_opacity']))

            # Image settings
            self.default_img_width.setValue(settings['image_settings']['default_width'])
            self.default_img_height.setValue(settings['image_settings']['default_height'])
            self.auto_resize.setChecked(settings['image_settings']['auto_resize'])

            # Export settings
            self.export_dir.setText(settings['export_settings']['export_dir'])
            self.export_format.setCurrentText(settings['export_settings']['export_format'])

            return True
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to load settings: {str(e)}")
            return False

    def reset_settings(self):
        """Resets all settings to default values."""
        try:
            # Use the settings manager if available
            if settings_manager is not None:
                # Reset all settings to default values
                # App settings
                settings_manager.set_value("app/theme", self.default_settings['app_settings']['theme'])
                settings_manager.set_value("app/color_scheme", self.default_settings['app_settings']['color_scheme'])
                settings_manager.set_value("app/font_family", self.default_settings['app_settings']['font_family'])
                settings_manager.set_value("app/font_size", self.default_settings['app_settings']['font_size'])
                settings_manager.set_value("app/language", self.default_settings['app_settings']['language'])

            # Apply default settings to UI elements
            # App settings
            self.theme_combo.setCurrentText(self.default_settings['app_settings']['theme'])
            self.color_scheme_combo.setCurrentText(self.default_settings['app_settings']['color_scheme'])
            self.font_family_combo.setCurrentText(self.default_settings['app_settings']['font_family'])
            self.font_size_combo.setCurrentText(self.default_settings['app_settings']['font_size'])
            self.language_combo.setCurrentText(self.default_settings['app_settings']['language'])

            # Update theme preview
            try:
                if hasattr(self, 'update_theme_preview') and callable(self.update_theme_preview):
                    self.update_theme_preview()
            except Exception as e:
                logger.error(f"Error updating theme preview: {e}")

            # Segmentation settings
            self.default_seg_method.setCurrentText(self.default_settings['segmentation_settings']['default_method'])
            self.default_epochs.setValue(self.default_settings['segmentation_settings']['default_epochs'])
            self.default_min_labels.setValue(self.default_settings['segmentation_settings']['default_min_labels'])
            self.default_max_labels.setValue(self.default_settings['segmentation_settings']['default_max_labels'])
            self.default_color_palette.setCurrentText(self.default_settings['segmentation_settings']['default_color_palette'])

            # Trainable Segmentation settings
            self.default_intensity_features.setChecked(self.default_settings['trainable_segmentation_settings']['default_intensity_features'])
            self.default_edge_features.setChecked(self.default_settings['trainable_segmentation_settings']['default_edge_features'])
            self.default_texture_features.setChecked(self.default_settings['trainable_segmentation_settings']['default_texture_features'])
            self.default_sigma_min.setValue(self.default_settings['trainable_segmentation_settings']['default_sigma_min'])
            self.default_sigma_max.setValue(self.default_settings['trainable_segmentation_settings']['default_sigma_max'])
            self.default_n_estimators.setValue(self.default_settings['trainable_segmentation_settings']['default_n_estimators'])
            self.default_max_depth.setValue(self.default_settings['trainable_segmentation_settings']['default_max_depth'])
            self.default_max_samples.setValue(self.default_settings['trainable_segmentation_settings']['default_max_samples'])
            self.default_brush_size.setValue(self.default_settings['trainable_segmentation_settings']['default_brush_size'])

            # Grain Analysis settings
            self.default_input_size.setValue(self.default_settings['grain_analysis_settings']['default_input_size'])
            self.default_iou_threshold.setValue(self.default_settings['grain_analysis_settings']['default_iou_threshold'])
            self.default_conf_threshold.setValue(self.default_settings['grain_analysis_settings']['default_conf_threshold'])
            self.default_max_det.setValue(self.default_settings['grain_analysis_settings']['default_max_det'])
            # Removed obsolete NMS parameters
            self.default_angle_threshold.setValue(self.default_settings['grain_analysis_settings']['default_angle_threshold'])
            self.default_straight_edge_ratio.setValue(self.default_settings['grain_analysis_settings']['default_straight_edge_ratio'])

            # Reset artifact handling settings
            preset_index = self.default_artifact_preset.findText(self.default_settings['grain_analysis_settings']['default_artifact_preset'])
            if preset_index >= 0:
                self.default_artifact_preset.setCurrentIndex(preset_index)
            self.default_artifact_sensitivity.setValue(self.default_settings['grain_analysis_settings']['default_artifact_sensitivity'])
            self.default_duplicate_sensitivity.setValue(self.default_settings['grain_analysis_settings']['default_duplicate_sensitivity'])

            # MobileSAM settings
            self.default_points_per_side.setValue(self.default_settings['mobilesam_settings']['default_points_per_side'])
            self.default_pred_iou_thresh.setValue(self.default_settings['mobilesam_settings']['default_pred_iou_thresh'])
            self.default_stability_score_thresh.setValue(self.default_settings['mobilesam_settings']['default_stability_score_thresh'])
            self.default_box_nms_thresh.setValue(self.default_settings['mobilesam_settings']['default_box_nms_thresh'])
            self.default_min_mask_area.setValue(self.default_settings['mobilesam_settings']['default_min_mask_area'])

            # YOLOv8 settings have been removed

            # AI Assistant settings
            self.default_gemini_model.setCurrentText(self.default_settings['ai_assistant_settings']['default_gemini_model'])
            self.prompt_templates_text.setPlainText(self.default_settings['ai_assistant_settings']['prompt_templates'])

            # Point Counting settings
            r = self.default_settings['point_counting_settings']['grid_color_r']
            g = self.default_settings['point_counting_settings']['grid_color_g']
            b = self.default_settings['point_counting_settings']['grid_color_b']
            a = self.default_settings['point_counting_settings']['grid_color_a']
            self.default_grid_color_button.setStyleSheet(f"background-color: rgba({r}, {g}, {b}, {a});")
            self.default_grid_opacity.setValue(self.default_settings['point_counting_settings']['grid_opacity'])
            self.default_grid_opacity_value.setText(str(self.default_settings['point_counting_settings']['grid_opacity']))

            # Image settings
            self.default_img_width.setValue(self.default_settings['image_settings']['default_width'])
            self.default_img_height.setValue(self.default_settings['image_settings']['default_height'])
            self.auto_resize.setChecked(self.default_settings['image_settings']['auto_resize'])

            # Export settings
            self.export_dir.setText(self.default_settings['export_settings']['export_dir'])
            self.export_format.setCurrentText(self.default_settings['export_settings']['export_format'])

            QMessageBox.information(self, "Settings Reset", "Settings have been reset to default values.")
            return True
        except Exception as e:
            QMessageBox.warning(self, "Error", f"Failed to reset settings: {str(e)}")
            return False

    def setup_handlers(self):
        """Sets up event handlers for the settings page."""
        # Connect buttons
        self.save_settings_btn.clicked.connect(self.save_settings)
        self.reset_settings_btn.clicked.connect(self.reset_settings)
        self.browse_export_dir.clicked.connect(self.browse_export_directory)

        # Connect point counting settings
        if hasattr(self, 'default_grid_color_button'):
            self.default_grid_color_button.clicked.connect(self.choose_grid_color)
        if hasattr(self, 'default_grid_opacity'):
            self.default_grid_opacity.valueChanged.connect(
                lambda v: self.default_grid_opacity_value.setText(str(v)) if hasattr(self, 'default_grid_opacity_value') else None
            )

        # Connect Apply Theme button if it exists
        if hasattr(self, 'apply_theme_btn'):
            # Use lambda to ensure show_message=True is passed when the button is clicked
            self.apply_theme_btn.clicked.connect(lambda: self.apply_theme(show_message=True))

        # Load settings when the page is initialized
        self.load_settings()

    def apply_theme(self, show_message=True):
        """Applies the selected theme to the application.

        Args:
            show_message (bool): Whether to show a success message after applying the theme.
                                Set to False when called during application startup or shutdown.
        """
        try:
            # Get theme settings
            theme_base = self.theme_combo.currentText().lower().replace(" theme", "")
            color_scheme = self.color_scheme_combo.currentText().lower()
            font_family = self.font_family_combo.currentText()

            # Get font size
            font_size_name = self.font_size_combo.currentText()
            try:
                from src.gui.styles.theme_config import FONT_SIZES, UI_STYLE_PARAMS
                font_size = FONT_SIZES.get(font_size_name, 10)
            except ImportError:
                # Default font sizes if import fails
                font_sizes = {"small": 8, "normal": 10, "medium": 12, "large": 14, "extra-large": 16}
                font_size = font_sizes.get(font_size_name, 10)

            # Get UI style parameters from sliders if they exist
            style_params = UI_STYLE_PARAMS.copy() if 'UI_STYLE_PARAMS' in locals() else {
                "border-radius": 4,
                "button-radius": 6,
                "padding": 6,
                "shadow-strength": 15,
                "animation-speed": 150,
                "opacity": 100,
                "control-height": 28,
                "slider-height": 2,
                "slider-handle-size": 12,
                "section-border-width": 1,
                "section-border-radius": 6,
                "section-gradient-strength": 3
            }

            # Update style parameters from UI controls if they exist
            if hasattr(self, 'border_radius_slider'):
                style_params["border-radius"] = self.border_radius_slider.value()

            if hasattr(self, 'button_radius_slider'):
                style_params["button-radius"] = self.button_radius_slider.value()

            if hasattr(self, 'padding_slider'):
                style_params["padding"] = self.padding_slider.value()

            if hasattr(self, 'control_height_slider'):
                style_params["control-height"] = self.control_height_slider.value()

            # Section styling parameters
            if hasattr(self, 'section_border_width_spinner'):
                style_params["section-border-width"] = self.section_border_width_spinner.value()

            if hasattr(self, 'section_border_opacity_slider'):
                style_params["section-border-opacity"] = self.section_border_opacity_slider.value()

            if hasattr(self, 'section_border_radius_slider'):
                style_params["section-border-radius"] = self.section_border_radius_slider.value()

            if hasattr(self, 'section_gradient_slider'):
                style_params["section-gradient-strength"] = self.section_gradient_slider.value()

            # Construct theme name
            theme_name = f"{color_scheme}-{theme_base}"

            # Try to apply theme using the new theme config
            try:
                from src.gui.styles.theme_config import apply_theme
                from PySide6.QtWidgets import QApplication

                # Apply theme to the entire application
                app = QApplication.instance()
                if app:
                    # Apply to the entire application
                    apply_theme(app, theme_name, font_family, font_size, style_params)

                    # Also apply to all top-level windows
                    for widget in app.topLevelWidgets():
                        apply_theme(widget, theme_name, font_family, font_size, style_params)

                    # Apply theme to specific pages
                    self._apply_theme_to_pages(theme_name, style_params)

                    # Save the theme settings to QSettings
                    if settings_manager is not None:
                        settings_manager.set_value("app/theme", self.theme_combo.currentText())
                        settings_manager.set_value("app/color_scheme", self.color_scheme_combo.currentText())
                        settings_manager.set_value("app/font_family", font_family)
                        settings_manager.set_value("app/font_size", font_size_name)

                        # Save UI style parameters
                        for key, value in style_params.items():
                            settings_manager.set_value(f"app/style/{key}", value)

                    # Show success message only if requested
                    if show_message:
                        QMessageBox.information(self, "Theme Applied", "The theme has been applied successfully.")

                    logger.info(f"Theme '{theme_name}' applied successfully")
                    return True
                else:
                    # Get the main window as fallback
                    main_window = self.parent()
                    while main_window and not main_window.isWindow():
                        main_window = main_window.parent()

                    if main_window:
                        apply_theme(main_window, theme_name, font_family, font_size)
                        logger.info(f"Theme '{theme_name}' applied to main window")
                        return True
            except ImportError as e:
                logger.error(f"Error importing theme_config: {e}")
                pass  # Fall back to legacy theme application
            except Exception as e:
                logger.error(f"Error applying theme with theme_config: {e}")
                pass  # Fall back to legacy theme application

            # Legacy theme application as fallback
            if hasattr(self, 'apply_dark_theme') and hasattr(self, 'apply_light_theme'):
                if theme_base == "dark":
                    self.apply_dark_theme()
                elif theme_base == "light":
                    self.apply_light_theme()
                # System Default would use the system theme
                logger.info(f"Applied legacy theme: {theme_base}")
                return True
            elif hasattr(self, 'grain_analysis_widget') and hasattr(self.grain_analysis_widget, 'apply_theme'):
                # Try to use the grain analysis widget's theme function
                self.grain_analysis_widget.apply_theme(theme_base)
                logger.info(f"Applied theme via grain analysis widget: {theme_base}")
                return True

            # If we got here, no theme was applied
            logger.warning("No theme application method was found")
            return False
        except Exception as e:
            logger.error(f"Error applying theme: {e}")
            if show_message:
                QMessageBox.warning(self, "Error", f"Failed to apply theme: {str(e)}")
            return False

    def save_project_settings(self, project_dir):
        """Saves project-specific settings to the project directory."""
        if not project_dir or not os.path.exists(project_dir):
            logger.warning(f"Invalid project directory: {project_dir}")
            return False

        try:
            # Use the settings manager if available
            if settings_manager is not None:
                # Create project-specific settings
                project_settings = {
                    'segmentation': {
                        'default_method': self.default_seg_method.currentText(),
                        'default_epochs': self.default_epochs.value(),
                        'default_min_labels': self.default_min_labels.value(),
                        'default_max_labels': self.default_max_labels.value(),
                        'default_color_palette': self.default_color_palette.currentText()
                    },
                    'trainable_segmentation': {
                        'default_intensity_features': self.default_intensity_features.isChecked(),
                        'default_edge_features': self.default_edge_features.isChecked(),
                        'default_texture_features': self.default_texture_features.isChecked(),
                        'default_sigma_min': self.default_sigma_min.value(),
                        'default_sigma_max': self.default_sigma_max.value(),
                        'default_n_estimators': self.default_n_estimators.value(),
                        'default_max_depth': self.default_max_depth.value(),
                        'default_max_samples': self.default_max_samples.value(),
                        'default_brush_size': self.default_brush_size.value()
                    },
                    'grain_analysis': {
                        'default_input_size': self.default_input_size.value(),
                        'default_iou_threshold': self.default_iou_threshold.value(),
                        'default_conf_threshold': self.default_conf_threshold.value(),
                        'default_max_det': self.default_max_det.value(),
                        'default_angle_threshold': self.default_angle_threshold.value(),
                        'default_straight_edge_ratio': self.default_straight_edge_ratio.value(),
                        'default_artifact_preset': self.default_artifact_preset.currentText(),
                        'default_artifact_sensitivity': self.default_artifact_sensitivity.value(),
                        'default_duplicate_sensitivity': self.default_duplicate_sensitivity.value()
                    },
                    'mobilesam': {
                        'default_points_per_side': self.default_points_per_side.value(),
                        'default_pred_iou_thresh': self.default_pred_iou_thresh.value(),
                        'default_stability_score_thresh': self.default_stability_score_thresh.value(),
                        'default_box_nms_thresh': self.default_box_nms_thresh.value(),
                        'default_min_mask_area': self.default_min_mask_area.value()
                    },
                    # YOLOv8 settings have been removed
                    'image': {
                        'default_width': self.default_img_width.value(),
                        'default_height': self.default_img_height.value(),
                        'auto_resize': self.auto_resize.isChecked()
                    },
                    'export': {
                        'export_dir': self.export_dir.text(),
                        'export_format': self.export_format.currentText()
                    }
                }

                # Save project-specific settings
                return settings_manager.save_project_settings(project_dir, project_settings)
            else:
                # Fallback to the old method if settings_manager is not available
                project_settings_file = os.path.join(project_dir, "project_settings.json")

                # Create project-specific settings
                project_settings = {
                    'segmentation': {
                        'default_method': self.default_seg_method.currentText(),
                        'default_epochs': self.default_epochs.value(),
                        'default_min_labels': self.default_min_labels.value(),
                        'default_max_labels': self.default_max_labels.value(),
                        'default_color_palette': self.default_color_palette.currentText()
                    },
                    # Add other settings as needed
                }

                # Save project-specific settings
                with open(project_settings_file, 'w') as f:
                    json.dump(project_settings, f, indent=4)

                return True
        except Exception as e:
            logger.error(f"Failed to save project settings: {e}")
            return False

    def load_project_settings(self, project_dir):
        """Loads project-specific settings from the project directory."""
        if not project_dir or not os.path.exists(project_dir):
            logger.warning(f"Invalid project directory: {project_dir}")
            return False

        try:
            # Use the settings manager if available
            if settings_manager is not None:
                # Load project-specific settings
                project_settings = settings_manager.get_project_settings(project_dir)

                if not project_settings:
                    logger.info(f"No project settings found for {project_dir}")
                    return False

                # Apply project-specific settings to UI elements
                # Segmentation settings
                if 'segmentation' in project_settings:
                    seg_settings = project_settings['segmentation']
                    if 'default_method' in seg_settings:
                        self.default_seg_method.setCurrentText(seg_settings['default_method'])
                    if 'default_epochs' in seg_settings:
                        self.default_epochs.setValue(seg_settings['default_epochs'])
                    if 'default_min_labels' in seg_settings:
                        self.default_min_labels.setValue(seg_settings['default_min_labels'])
                    if 'default_max_labels' in seg_settings:
                        self.default_max_labels.setValue(seg_settings['default_max_labels'])
                    if 'default_color_palette' in seg_settings:
                        self.default_color_palette.setCurrentText(seg_settings['default_color_palette'])

                # Trainable segmentation settings
                if 'trainable_segmentation' in project_settings:
                    ts_settings = project_settings['trainable_segmentation']
                    if 'default_intensity_features' in ts_settings:
                        self.default_intensity_features.setChecked(ts_settings['default_intensity_features'])
                    if 'default_edge_features' in ts_settings:
                        self.default_edge_features.setChecked(ts_settings['default_edge_features'])
                    if 'default_texture_features' in ts_settings:
                        self.default_texture_features.setChecked(ts_settings['default_texture_features'])
                    if 'default_sigma_min' in ts_settings:
                        self.default_sigma_min.setValue(ts_settings['default_sigma_min'])
                    if 'default_sigma_max' in ts_settings:
                        self.default_sigma_max.setValue(ts_settings['default_sigma_max'])
                    if 'default_n_estimators' in ts_settings:
                        self.default_n_estimators.setValue(ts_settings['default_n_estimators'])
                    if 'default_max_depth' in ts_settings:
                        self.default_max_depth.setValue(ts_settings['default_max_depth'])
                    if 'default_max_samples' in ts_settings:
                        self.default_max_samples.setValue(ts_settings['default_max_samples'])
                    if 'default_brush_size' in ts_settings:
                        self.default_brush_size.setValue(ts_settings['default_brush_size'])

                # Add other settings as needed

                return True
            else:
                # Fallback to the old method if settings_manager is not available
                project_settings_file = os.path.join(project_dir, "project_settings.json")

                if not os.path.exists(project_settings_file):
                    logger.info(f"No project settings file found at {project_settings_file}")
                    return False

                # Load project-specific settings
                with open(project_settings_file, 'r') as f:
                    project_settings = json.load(f)

                # Apply project-specific settings to UI elements
                # Segmentation settings
                if 'segmentation' in project_settings:
                    seg_settings = project_settings['segmentation']
                    if 'default_method' in seg_settings:
                        self.default_seg_method.setCurrentText(seg_settings['default_method'])
                    # Add other settings as needed

                return True
        except Exception as e:
            logger.error(f"Failed to load project settings: {e}")
            return False

    def _apply_theme_to_pages(self, theme_name, style_params=None):
        """Apply theme to specific pages that need special handling."""
        try:
            # Apply theme to point counting page
            if hasattr(self, 'point_counting_page_handler') and self.point_counting_page_handler:
                if hasattr(self.point_counting_page_handler, 'apply_theme'):
                    logger.info("Applying theme to point counting page")
                    if style_params:
                        self.point_counting_page_handler.apply_theme(theme_name, style_params)
                    else:
                        self.point_counting_page_handler.apply_theme(theme_name)

            # Apply theme to trainable segmentation page
            if hasattr(self, 'trainable_segmentation_page_handler') and self.trainable_segmentation_page_handler:
                if hasattr(self.trainable_segmentation_page_handler, 'apply_theme'):
                    logger.info("Applying theme to trainable segmentation page")
                    if style_params:
                        self.trainable_segmentation_page_handler.apply_theme(theme_name, style_params)
                    else:
                        self.trainable_segmentation_page_handler.apply_theme(theme_name)

            # Update tab styling
            if hasattr(self, 'stacked_widget') and hasattr(self.stacked_widget, 'update_tab_styling'):
                logger.info("Updating tab styling")
                self.stacked_widget.update_tab_styling()
            elif hasattr(self, 'update_tab_styling'):
                logger.info("Updating tab styling")
                self.update_tab_styling()
        except Exception as e:
            logger.error(f"Error applying theme to specific pages: {e}")

    def browse_export_directory(self):
        """Opens a file dialog to select the export directory."""
        directory = QFileDialog.getExistingDirectory(self, "Select Export Directory")
        if directory:
            self.export_dir.setText(directory)

    def choose_grid_color(self):
        """Opens a color dialog to select the default grid color."""
        color_dialog = QColorDialog(self)
        color_dialog.setOption(QColorDialog.ShowAlphaChannel, True)

        # Get current color from button's stylesheet
        current_style = self.default_grid_color_button.styleSheet()
        current_color = QColor(255, 255, 255, 180)  # Default white with transparency

        # Try to parse the current color from the stylesheet
        import re
        rgba_match = re.search(r'rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d+)\)', current_style)
        if rgba_match:
            r, g, b, a = map(int, rgba_match.groups())
            current_color = QColor(r, g, b, a)

        color_dialog.setCurrentColor(current_color)

        if color_dialog.exec():
            new_color = color_dialog.selectedColor()
            if new_color.isValid():
                # Update the button's background color
                self.default_grid_color_button.setStyleSheet(
                    f"background-color: rgba({new_color.red()}, {new_color.green()}, {new_color.blue()}, {new_color.alpha()});"
                )

                # Update the opacity slider to match the alpha value
                opacity_percent = int(new_color.alpha() * 100 / 255)
                self.default_grid_opacity.setValue(opacity_percent)
                self.default_grid_opacity_value.setText(str(opacity_percent))