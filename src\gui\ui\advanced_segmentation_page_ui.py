# src/gui/ui/advanced_segmentation_page_ui.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QScrollArea, QFrame, QTabWidget, QSlider, QSpinBox, QDoubleSpinBox,
    QComboBox, QProgressBar, QFormLayout, QMessageBox, QSplitter, QListWidget,
    QListWidgetItem, QToolButton, QGridLayout, QCheckBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon

from src.widgets.scrollable_frame import <PERSON><PERSON>able<PERSON>rame
from src.widgets.pixmap_view import QPixmapView
from src.widgets.collapsible_section import CollapsibleSection
from src.gui.widgets.synchronized_image_view import SynchronizedImageView
from src.widgets.advanced_segmentation_gallery import AdvancedSegmentationGallery

class AdvancedSegmentationPageUI:
    """UI class for the Advanced Segmentation page."""

    def setup_advanced_segmentation_page(self):
        """Sets up the Advanced Segmentation page with annotation tools and model training."""
        main_layout = QHBoxLayout(self)

        # Left panel for image gallery and tools
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(5)

        # Image Gallery
        gallery_group = QGroupBox("Image Gallery")
        gallery_layout = QVBoxLayout(gallery_group)
        gallery_layout.setContentsMargins(5, 5, 5, 5)
        gallery_layout.setSpacing(2)

        # Set fixed height for the gallery group
        gallery_group.setFixedHeight(200)

        # Create the advanced segmentation gallery
        self.adv_seg_gallery = AdvancedSegmentationGallery()

        # Add the gallery to the layout
        gallery_layout.addWidget(self.adv_seg_gallery)

        left_layout.addWidget(gallery_group)

        # Annotation Tools
        tools_group = QGroupBox("Annotation Tools")
        tools_layout = QVBoxLayout(tools_group)
        tools_layout.setContentsMargins(5, 5, 5, 5)
        tools_layout.setSpacing(5)

        # Create a grid layout for the annotation tools
        tools_grid = QGridLayout()
        tools_grid.setContentsMargins(0, 0, 0, 0)
        tools_grid.setSpacing(5)

        # Add annotation tools buttons
        self.select_tool_btn = QPushButton("Select")
        self.polygon_tool_btn = QPushButton("Polygons")
        self.rectangle_tool_btn = QPushButton("Rectangles")
        self.point_prompt_tool_btn = QPushButton("+ Point")
        self.negative_point_prompt_tool_btn = QPushButton("- Point")
        self.brush_tool_btn = QPushButton("Brush")
        self.erase_tool_btn = QPushButton("Erase")
        self.magic_wand_tool_btn = QPushButton("Magic Wand")

        # Add accept/reject buttons for SAM predictions
        self.accept_sam_btn = QPushButton("Accept")
        self.reject_sam_btn = QPushButton("Reject")
        self.reset_points_btn = QPushButton("Reset Points")

        # Set minimum height for buttons and add visual styling
        for btn in [self.select_tool_btn, self.polygon_tool_btn, self.rectangle_tool_btn,
                   self.point_prompt_tool_btn, self.negative_point_prompt_tool_btn,
                   self.brush_tool_btn, self.erase_tool_btn, self.magic_wand_tool_btn]:
            btn.setMinimumHeight(30)
            btn.setCheckable(True)

        # Instead of using a stylesheet, we'll use the theme's palette and just modify the checked state
        tool_buttons = [
            self.select_tool_btn,
            self.polygon_tool_btn,
            self.rectangle_tool_btn,
            self.point_prompt_tool_btn,
            self.negative_point_prompt_tool_btn,
            self.brush_tool_btn,
            self.erase_tool_btn,
            self.magic_wand_tool_btn
        ]

        # We'll only add minimal styling for the checked state
        for btn in tool_buttons:
            # Make the button use the system theme for normal state
            btn.setAutoFillBackground(True)

            # Only add a simple style for the checked state
            btn.setStyleSheet("""
                QPushButton:checked {
                    background-color: palette(highlight);
                    color: palette(highlighted-text);
                    font-weight: bold;
                }
            """)

        # Set tooltips
        self.select_tool_btn.setToolTip("Select and edit annotations (S)")
        self.polygon_tool_btn.setToolTip("Draw polygon annotations (P)")
        self.rectangle_tool_btn.setToolTip("Draw rectangle annotations (R)")
        self.point_prompt_tool_btn.setToolTip("Add positive (foreground) point prompt (F)")
        self.negative_point_prompt_tool_btn.setToolTip("Add negative (background) point prompt (B)")
        self.brush_tool_btn.setToolTip("Paint with brush (B)")
        self.erase_tool_btn.setToolTip("Erase with brush (E)")
        self.magic_wand_tool_btn.setToolTip("Magic wand selection (M)")
        self.accept_sam_btn.setToolTip("Accept the current mask (Enter)")
        self.reject_sam_btn.setToolTip("Reject the current mask (Escape)")
        self.reset_points_btn.setToolTip("Reset all points and start over (C)")

        # Add brush size slider
        brush_size_layout = QHBoxLayout()
        brush_size_layout.addWidget(QLabel("Brush Size:"))
        self.brush_size_slider = QSlider(Qt.Horizontal)
        self.brush_size_slider.setMinimum(1)
        self.brush_size_slider.setMaximum(50)
        self.brush_size_slider.setValue(10)
        self.brush_size_slider.setTickPosition(QSlider.TicksBelow)
        self.brush_size_slider.setTickInterval(5)
        self.brush_size_value_label = QLabel("10")
        brush_size_layout.addWidget(self.brush_size_slider)
        brush_size_layout.addWidget(self.brush_size_value_label)

        # Add buttons to the grid
        tools_grid.addWidget(self.select_tool_btn, 0, 0, 1, 2)  # Select tool spans both columns
        tools_grid.addWidget(self.polygon_tool_btn, 1, 0)
        tools_grid.addWidget(self.rectangle_tool_btn, 1, 1)
        tools_grid.addWidget(self.point_prompt_tool_btn, 2, 0)
        tools_grid.addWidget(self.negative_point_prompt_tool_btn, 2, 1)
        tools_grid.addWidget(self.brush_tool_btn, 3, 0)
        tools_grid.addWidget(self.erase_tool_btn, 3, 1)
        tools_grid.addWidget(self.magic_wand_tool_btn, 4, 0, 1, 2)

        # Add brush size slider to tools layout
        tools_layout.addLayout(brush_size_layout)

        # Add SAM control buttons
        sam_controls_layout = QHBoxLayout()
        sam_controls_layout.addWidget(self.accept_sam_btn)
        sam_controls_layout.addWidget(self.reject_sam_btn)
        sam_controls_layout.addWidget(self.reset_points_btn)

        # Let the SAM control buttons use the system theme
        sam_buttons = [self.accept_sam_btn, self.reject_sam_btn, self.reset_points_btn]
        for btn in sam_buttons:
            btn.setAutoFillBackground(True)
            # Clear any existing stylesheet
            btn.setStyleSheet("")

        tools_layout.addLayout(sam_controls_layout)

        # Create a container widget for the tools to ensure proper styling
        tools_container = QWidget()
        tools_container_layout = QVBoxLayout(tools_container)
        tools_container_layout.setContentsMargins(0, 0, 0, 0)
        tools_container_layout.addLayout(tools_grid)

        # Add the container to the tools layout
        tools_layout.addWidget(tools_container)

        left_layout.addWidget(tools_group)

        # Class Management
        class_group = QGroupBox("Class Management")
        class_layout = QVBoxLayout(class_group)
        class_layout.setContentsMargins(5, 5, 5, 5)
        class_layout.setSpacing(5)

        # Class selector
        class_selector_layout = QHBoxLayout()
        class_selector_layout.addWidget(QLabel("Current Class:"))
        self.class_selector = QComboBox()
        self.class_selector.addItem("Class 1", 1)
        self.class_selector.addItem("Class 2", 2)
        self.class_selector.addItem("Class 3", 3)
        class_selector_layout.addWidget(self.class_selector, stretch=1)

        # Manage classes button
        self.manage_classes_btn = QPushButton("Manage Classes")
        class_selector_layout.addWidget(self.manage_classes_btn)

        class_layout.addLayout(class_selector_layout)
        left_layout.addWidget(class_group)

        # Annotations List
        annotations_group = QGroupBox("Annotations")
        annotations_layout = QVBoxLayout(annotations_group)
        annotations_layout.setContentsMargins(5, 5, 5, 5)
        annotations_layout.setSpacing(5)

        # Create a list widget for annotations
        self.annotations_list = QListWidget()
        annotations_layout.addWidget(self.annotations_list)

        # Set the selection mode to allow multiple selection
        self.annotations_list.setSelectionMode(QListWidget.ExtendedSelection)

        # Add edit, remove, and clear all buttons
        annotations_buttons_layout = QHBoxLayout()
        self.edit_annotation_btn = QPushButton("Edit")
        self.remove_annotation_btn = QPushButton("Remove")
        self.clear_all_annotations_btn = QPushButton("Clear All")
        annotations_buttons_layout.addWidget(self.edit_annotation_btn)
        annotations_buttons_layout.addWidget(self.remove_annotation_btn)
        annotations_buttons_layout.addWidget(self.clear_all_annotations_btn)
        annotations_layout.addLayout(annotations_buttons_layout)

        # Add save and load annotations buttons
        save_load_buttons_layout = QHBoxLayout()
        self.save_annotations_btn = QPushButton("Save Annotations")
        self.load_annotations_btn = QPushButton("Load Annotations")
        self.save_annotations_btn.setToolTip("Save annotations to a file")
        self.load_annotations_btn.setToolTip("Load annotations from a file")
        save_load_buttons_layout.addWidget(self.save_annotations_btn)
        save_load_buttons_layout.addWidget(self.load_annotations_btn)
        annotations_layout.addLayout(save_load_buttons_layout)

        left_layout.addWidget(annotations_group)



        # Add stretch to push everything up
        left_layout.addStretch()

        # Set fixed width for the left panel
        left_panel.setFixedWidth(350)
        main_layout.addWidget(left_panel)

        # Center panel with tabs and image view
        center_panel = QWidget()
        center_layout = QVBoxLayout(center_panel)
        center_layout.setContentsMargins(0, 0, 0, 0)
        center_layout.setSpacing(0)

        # Create tabs for different views
        self.adv_seg_tabs = QTabWidget()

        # Annotate tab
        self.annotate_tab = QWidget()
        annotate_layout = QVBoxLayout(self.annotate_tab)
        annotate_layout.setContentsMargins(0, 0, 0, 0)

        # Create synchronized image view for annotation
        self.annotation_image_view = SynchronizedImageView()
        self.annotation_image_view.setMinimumSize(400, 400)
        annotate_layout.addWidget(self.annotation_image_view)

        # Segmentation Results tab
        self.segmentation_results_tab = QWidget()
        segmentation_results_layout = QVBoxLayout(self.segmentation_results_tab)
        segmentation_results_layout.setContentsMargins(0, 0, 0, 0)

        # Create synchronized image view for segmentation results
        self.segmentation_results_view = SynchronizedImageView()
        self.segmentation_results_view.setMinimumSize(400, 400)
        segmentation_results_layout.addWidget(self.segmentation_results_view)

        # Synchronize the two views
        self.annotation_image_view.add_synced_view(self.segmentation_results_view)
        self.segmentation_results_view.add_synced_view(self.annotation_image_view)

        # Add instructions for zooming, panning, and scrolling
        annotation_instructions = QLabel("Use mouse wheel to zoom, drag to pan, use scrollbars to navigate, or press Ctrl+0 to reset view")
        annotation_instructions.setAlignment(Qt.AlignCenter)
        annotation_instructions.setStyleSheet("color: #666666; font-style: italic;")
        annotate_layout.addWidget(annotation_instructions)

        segmentation_instructions = QLabel("Use mouse wheel to zoom, drag to pan, use scrollbars to navigate, or press Ctrl+0 to reset view")
        segmentation_instructions.setAlignment(Qt.AlignCenter)
        segmentation_instructions.setStyleSheet("color: #666666; font-style: italic;")
        segmentation_results_layout.addWidget(segmentation_instructions)

        # Add tabs to the tab widget
        self.adv_seg_tabs.addTab(self.annotate_tab, "Annotate")
        self.adv_seg_tabs.addTab(self.segmentation_results_tab, "Segmentation Results")

        center_layout.addWidget(self.adv_seg_tabs)
        main_layout.addWidget(center_panel, stretch=1)

        # Right panel for model training and export
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(10)

        # Dataset Preparation
        dataset_group = QGroupBox("Dataset Preparation")
        dataset_layout = QVBoxLayout(dataset_group)
        dataset_layout.setContentsMargins(5, 5, 5, 5)
        dataset_layout.setSpacing(5)

        self.dataset_preparation_btn = QPushButton("Prepare & Export Dataset\n(Splitting, Augmentation, Export)")
        self.dataset_preparation_btn.setMinimumHeight(40)
        dataset_layout.addWidget(self.dataset_preparation_btn)

        # Import Dataset button
        dataset_buttons_layout = QHBoxLayout()
        self.import_dataset_btn = QPushButton("Import Dataset")
        dataset_buttons_layout.addWidget(self.import_dataset_btn)
        dataset_layout.addLayout(dataset_buttons_layout)

        right_layout.addWidget(dataset_group)

        # Model Training section
        model_training_group = QGroupBox("Model Training")
        model_training_layout = QVBoxLayout(model_training_group)
        model_training_layout.setContentsMargins(5, 5, 5, 5)
        model_training_layout.setSpacing(5)

        # Model Trainer
        self.model_trainer_btn = QPushButton("Model Trainer")
        self.model_trainer_btn.setMinimumHeight(40)
        model_training_layout.addWidget(self.model_trainer_btn)

        # Model Inference
        self.model_inference_btn = QPushButton("Model Inference")
        self.model_inference_btn.setMinimumHeight(40)
        model_training_layout.addWidget(self.model_inference_btn)

        # Model Evaluation
        self.model_evaluation_btn = QPushButton("Model Evaluation")
        self.model_evaluation_btn.setMinimumHeight(40)
        model_training_layout.addWidget(self.model_evaluation_btn)

        # Refine Mask with SAM
        self.refine_mask_sam_btn = QPushButton("Refine Mask with SAM")
        self.refine_mask_sam_btn.setMinimumHeight(40)
        self.refine_mask_sam_btn.setToolTip("Refine model predictions using MobileSAM for more precise masks")
        model_training_layout.addWidget(self.refine_mask_sam_btn)

        right_layout.addWidget(model_training_group)

        # Add stretch to push everything up
        right_layout.addStretch()

        # Set fixed width for the right panel
        right_panel.setFixedWidth(200)
        main_layout.addWidget(right_panel)
