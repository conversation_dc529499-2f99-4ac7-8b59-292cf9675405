@echo off
echo ===== Building VisionLab Ai Executable =====
echo.

:: Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python 3.10 or higher and try again.
    pause
    exit /b 1
)

:: Check if the build script exists
if not exist build_visionlab_ai_exe.py (
    echo Error: build_visionlab_ai_exe.py not found.
    echo Please make sure you're running this batch file from the correct directory.
    pause
    exit /b 1
)

:: Run the build script
echo Running build script...
echo.
python build_visionlab_ai_exe.py

:: Check if the build was successful
if %errorlevel% neq 0 (
    echo.
    echo Build failed. Please check the error messages above.
    pause
    exit /b 1
)

:: Check if the executable was created
if not exist dist\VisionLab_Ai\VisionLab_Ai.exe (
    echo.
    echo Warning: Build completed but executable not found at expected location.
    echo Please check the build logs for details.
    pause
    exit /b 1
)

echo.
echo ===== Build Successful =====
echo.
echo The executable is located at: dist\VisionLab_Ai\VisionLab_Ai.exe
echo.
echo To run the executable, navigate to the dist\VisionLab_Ai directory and run VisionLab_Ai.exe
echo.
echo Note: If you encounter issues with missing XGBoost DLL, check the README for solutions.
echo.
pause
