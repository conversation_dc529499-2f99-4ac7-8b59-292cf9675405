# src/widgets/advanced_segmentation_gallery.py

from PySide6.QtWidgets import QPushButton, QHBoxLayout, QWidget
from PySide6.QtCore import Signal

from src.widgets.page_image_gallery import PageImageGallery

class AdvancedSegmentationGallery(PageImageGallery):
    """Image gallery specifically for the Advanced Segmentation Page."""

    def __init__(self, parent=None):
        super().__init__(parent, thumbnail_size=100)  # Larger thumbnails for better visibility

    def clear_images(self):
        """Removes all images and thumbnails from the gallery."""
        # Call the parent class's clear method to handle the actual clearing
        self.clear()
        print("AdvancedSegmentationGallery: All images cleared.")
