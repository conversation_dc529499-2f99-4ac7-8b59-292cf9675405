"""
Dialog for displaying grain size statistical reports.
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                              QTabWidget, QWidget, QTableWidget, QTableWidgetItem,
                              QHeaderView, QGroupBox, QFormLayout, QPushButton,
                              QTextEdit, QScrollArea, QFileDialog, QMessageBox)
from PySide6.QtCore import Qt, QSize, Slot
from PySide6.QtGui import QFont

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import logging
import os
import csv
from datetime import datetime
from typing import Dict, Optional, List, Tuple

from src.core.grain_statistics import generate_grain_statistics_report

logger = logging.getLogger(__name__)

class GrainStatisticsDialog(QDialog):
    """Dialog for displaying grain size statistical reports."""

    def __init__(self, parent=None, df: Optional[pd.DataFrame] = None, scale_factor: float = 1.0,
                 sample_info: Dict = None, measurement_type: str = "ecd"):
        super().__init__(parent)
        self.df = df
        self.scale_factor = scale_factor
        self.sample_info = sample_info or {}
        self.measurement_type = measurement_type  # "ecd" or "length"
        self.statistics = {}

        self.setWindowTitle("Grain Size Statistical Report")
        self.resize(800, 600)

        self.setup_ui()

        if df is not None and not df.empty:
            self.generate_statistics()

    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Summary tab
        self.summary_tab = QWidget()
        self.setup_summary_tab()
        self.tab_widget.addTab(self.summary_tab, "Summary")

        # Detailed Statistics tab
        self.detailed_tab = QWidget()
        self.setup_detailed_tab()
        self.tab_widget.addTab(self.detailed_tab, "Detailed Statistics")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

        # Plots tab
        self.plots_tab = QWidget()
        self.setup_plots_tab()
        self.tab_widget.addTab(self.plots_tab, "Plots")

        # Bottom buttons
        button_layout = QHBoxLayout()

        # Export button
        self.export_csv_button = QPushButton("Save as CSV")
        self.export_csv_button.setToolTip("Export statistics to CSV file")
        self.export_csv_button.clicked.connect(self.export_to_csv)
        button_layout.addWidget(self.export_csv_button)

        # Save plots button
        self.save_plots_button = QPushButton("Save Plots")
        self.save_plots_button.setToolTip("Save plots as image files")
        self.save_plots_button.clicked.connect(self.save_plots)
        button_layout.addWidget(self.save_plots_button)

        button_layout.addStretch()

        # Close button
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)

        main_layout.addLayout(button_layout)

    def setup_summary_tab(self):
        """Set up the Summary tab."""
        layout = QVBoxLayout(self.summary_tab)

        # Sample information section
        sample_group = QGroupBox("SAMPLE STATISTICS")
        sample_layout = QFormLayout(sample_group)

        self.sample_identity_label = QLabel("")
        self.analyst_date_label = QLabel("")
        self.sample_type_label = QLabel("")
        self.textural_group_label = QLabel("")
        self.sediment_name_label = QLabel("")

        sample_layout.addRow("SAMPLE IDENTITY:", self.sample_identity_label)
        sample_layout.addRow("ANALYST & DATE:", self.analyst_date_label)
        sample_layout.addRow("SAMPLE TYPE:", self.sample_type_label)
        sample_layout.addRow("TEXTURAL GROUP:", self.textural_group_label)
        sample_layout.addRow("SEDIMENT NAME:", self.sediment_name_label)

        layout.addWidget(sample_group)

        # Grain size distribution table
        distribution_group = QGroupBox("GRAIN SIZE DISTRIBUTION")
        distribution_layout = QVBoxLayout(distribution_group)

        self.distribution_table = QTableWidget(10, 4)
        self.distribution_table.setHorizontalHeaderLabels(["", "", "", ""])
        self.distribution_table.verticalHeader().setVisible(False)
        self.distribution_table.horizontalHeader().setVisible(False)
        self.distribution_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Set up the table structure
        self.setup_distribution_table()

        distribution_layout.addWidget(self.distribution_table)
        layout.addWidget(distribution_group)

        # Method of Moments table
        moments_group = QGroupBox("METHOD OF MOMENTS")
        moments_layout = QVBoxLayout(moments_group)

        self.moments_table = QTableWidget(4, 4)
        # Set headers based on measurement type
        if self.measurement_type == "ecd":
            self.moments_table.setHorizontalHeaderLabels(["", "Arithmetic (mm, ECD)", "Geometric (mm, ECD)", "Logarithmic (Φ, ECD)"])
        elif self.measurement_type == "length":
            self.moments_table.setHorizontalHeaderLabels(["", "Arithmetic (mm, Length)", "Geometric (mm, Length)", "Logarithmic (Φ, Length)"])
        else:
            self.moments_table.setHorizontalHeaderLabels(["", "Arithmetic (mm)", "Geometric (mm)", "Logarithmic (Φ)"])
        self.moments_table.verticalHeader().setVisible(False)
        self.moments_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Set up the table structure
        self.setup_moments_table()

        moments_layout.addWidget(self.moments_table)
        layout.addWidget(moments_group)

        # Folk & Ward Method table
        folk_ward_group = QGroupBox("FOLK & WARD METHOD")
        folk_ward_layout = QVBoxLayout(folk_ward_group)

        self.folk_ward_table = QTableWidget(4, 4)
        # Set headers based on measurement type
        if self.measurement_type == "ecd":
            self.folk_ward_table.setHorizontalHeaderLabels(["", "Geometric (mm, ECD)", "Logarithmic (Φ, ECD)", "Description"])
        elif self.measurement_type == "length":
            self.folk_ward_table.setHorizontalHeaderLabels(["", "Geometric (mm, Length)", "Logarithmic (Φ, Length)", "Description"])
        else:
            self.folk_ward_table.setHorizontalHeaderLabels(["", "Geometric (mm)", "Logarithmic (Φ)", "Description"])
        self.folk_ward_table.verticalHeader().setVisible(False)
        self.folk_ward_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Set up the table structure
        self.setup_folk_ward_table()

        folk_ward_layout.addWidget(self.folk_ward_table)
        layout.addWidget(folk_ward_group)

    def setup_detailed_tab(self):
        """Set up the Detailed Statistics tab."""
        layout = QVBoxLayout(self.detailed_tab)

        # Create a text edit for detailed statistics
        self.detailed_text = QTextEdit()
        self.detailed_text.setReadOnly(True)
        self.detailed_text.setFont(QFont("Monospace", 10))

        layout.addWidget(self.detailed_text)

    def setup_distribution_table(self):
        """Set up the grain size distribution table structure."""
        # First column
        self.distribution_table.setItem(0, 0, self._create_bold_item("MODE 1:"))
        self.distribution_table.setItem(1, 0, self._create_bold_item("MODE 2:"))
        self.distribution_table.setItem(2, 0, self._create_bold_item("MODE 3:"))
        self.distribution_table.setItem(3, 0, self._create_bold_item("D10:"))
        self.distribution_table.setItem(4, 0, self._create_bold_item("MEDIAN (D50):"))
        self.distribution_table.setItem(5, 0, self._create_bold_item("D90:"))
        self.distribution_table.setItem(6, 0, self._create_bold_item("(D90 / D10):"))
        self.distribution_table.setItem(7, 0, self._create_bold_item("(D90 - D10):"))
        self.distribution_table.setItem(8, 0, self._create_bold_item("(D75 / D25):"))
        self.distribution_table.setItem(9, 0, self._create_bold_item("(D75 - D25):"))

        # Second column
        self.distribution_table.setItem(0, 1, self._create_bold_item("GRAVEL:"))
        self.distribution_table.setItem(1, 1, self._create_bold_item("SAND:"))
        self.distribution_table.setItem(2, 1, self._create_bold_item("MUD:"))
        self.distribution_table.setItem(3, 1, self._create_bold_item("V FINE SAND:"))
        self.distribution_table.setItem(4, 1, self._create_bold_item("V COARSE SILT:"))
        self.distribution_table.setItem(5, 1, self._create_bold_item("COARSE GRAVEL:"))
        self.distribution_table.setItem(6, 1, self._create_bold_item("MEDIUM GRAVEL:"))
        self.distribution_table.setItem(7, 1, self._create_bold_item("FINE GRAVEL:"))
        self.distribution_table.setItem(8, 1, self._create_bold_item("V FINE GRAVEL:"))
        self.distribution_table.setItem(9, 1, self._create_bold_item("V COARSE SAND:"))

        # Third column
        self.distribution_table.setItem(0, 2, self._create_bold_item("COARSE SAND:"))
        self.distribution_table.setItem(1, 2, self._create_bold_item("MEDIUM SAND:"))
        self.distribution_table.setItem(2, 2, self._create_bold_item("FINE SAND:"))
        self.distribution_table.setItem(3, 2, self._create_bold_item(""))
        self.distribution_table.setItem(4, 2, self._create_bold_item(""))
        self.distribution_table.setItem(5, 2, self._create_bold_item("COARSE SILT:"))
        self.distribution_table.setItem(6, 2, self._create_bold_item("MEDIUM SILT:"))
        self.distribution_table.setItem(7, 2, self._create_bold_item("FINE SILT:"))
        self.distribution_table.setItem(8, 2, self._create_bold_item("V FINE SILT:"))
        self.distribution_table.setItem(9, 2, self._create_bold_item("CLAY:"))

        # Initialize empty value cells
        for row in range(10):
            for col in range(1, 4):
                if self.distribution_table.item(row, col-1) and self.distribution_table.item(row, col-1).text():
                    self.distribution_table.setItem(row, col, QTableWidgetItem(""))

    def setup_moments_table(self):
        """Set up the method of moments table structure."""
        self.moments_table.setItem(0, 0, self._create_bold_item("MEAN:"))
        self.moments_table.setItem(1, 0, self._create_bold_item("SORTING (σ):"))
        self.moments_table.setItem(2, 0, self._create_bold_item("SKEWNESS (Sk):"))
        self.moments_table.setItem(3, 0, self._create_bold_item("KURTOSIS (K):"))

        # Initialize with zeros
        for row in range(4):
            for col in range(1, 4):
                self.moments_table.setItem(row, col, QTableWidgetItem("0.000"))

    def setup_folk_ward_table(self):
        """Set up the Folk & Ward method table structure."""
        self.folk_ward_table.setItem(0, 0, self._create_bold_item("MEAN:"))
        self.folk_ward_table.setItem(1, 0, self._create_bold_item("SORTING (σ):"))
        self.folk_ward_table.setItem(2, 0, self._create_bold_item("SKEWNESS (Sk):"))
        self.folk_ward_table.setItem(3, 0, self._create_bold_item("KURTOSIS (K):"))

        # Initialize with empty cells
        for row in range(4):
            for col in range(1, 4):
                self.folk_ward_table.setItem(row, col, QTableWidgetItem(""))

    def _create_bold_item(self, text):
        """Create a bold, non-editable table item."""
        item = QTableWidgetItem(text)
        font = item.font()
        font.setBold(True)
        item.setFont(font)
        item.setFlags(item.flags() & ~Qt.ItemIsEditable)
        return item

    def generate_statistics(self):
        """Generate statistics from the provided DataFrame."""
        if self.df is None or self.df.empty:
            logger.warning("No data available for statistics generation")
            return

        try:
            # Generate the statistics report
            self.statistics = generate_grain_statistics_report(self.df, self.scale_factor)

            # Update the UI with the statistics
            self.update_sample_info()
            self.update_distribution_table()
            self.update_moments_table()
            self.update_folk_ward_table()
            self.update_detailed_text()

            logger.info("Grain statistics generated successfully")
        except Exception as e:
            logger.error(f"Error generating grain statistics: {e}")

    def update_sample_info(self):
        """Update the sample information section."""
        # Update from provided sample info
        self.sample_identity_label.setText(self.sample_info.get("identity", ""))
        self.analyst_date_label.setText(self.sample_info.get("analyst_date", ""))
        self.sample_type_label.setText(self.sample_info.get("type", ""))

        # Try to determine textural group and sediment name from statistics
        if self.statistics and "grain_size_distribution" in self.statistics:
            class_percentages = self.statistics["grain_size_distribution"].get("class_percentages", {})

            # Simple classification based on percentages
            gravel_pct = class_percentages.get("GRAVEL", 0)
            sand_pct = class_percentages.get("SAND", 0)
            mud_pct = class_percentages.get("MUD", 0)

            # Determine textural group
            if gravel_pct > 30:
                textural_group = "Gravelly"
            elif sand_pct > 75:
                textural_group = "Sandy"
            elif mud_pct > 75:
                textural_group = "Muddy"
            else:
                textural_group = "Mixed"

            self.textural_group_label.setText(textural_group)

            # Determine sediment name (simplified)
            if gravel_pct > 50:
                sediment_name = "Gravel"
            elif sand_pct > 50:
                sediment_name = "Sand"
            elif mud_pct > 50:
                sediment_name = "Mud"
            else:
                sediment_name = "Mixed Sediment"

            self.sediment_name_label.setText(sediment_name)

    def update_distribution_table(self):
        """Update the grain size distribution table with statistics."""
        if not self.statistics or "grain_size_distribution" not in self.statistics:
            return

        dist_stats = self.statistics["grain_size_distribution"]

        # Update modes
        modes = dist_stats.get("modes", {})
        for i, mode_key in enumerate(["mode_1", "mode_2", "mode_3"]):
            if mode_key in modes:
                self.distribution_table.item(i, 3).setText(f"{modes[mode_key]:.3f}")

        # Update percentiles
        percentiles = dist_stats.get("percentiles", {})
        self.distribution_table.setItem(3, 3, QTableWidgetItem(f"{percentiles.get('D10', 0):.3f}"))
        self.distribution_table.setItem(4, 3, QTableWidgetItem(f"{percentiles.get('D50', 0):.3f}"))
        self.distribution_table.setItem(5, 3, QTableWidgetItem(f"{percentiles.get('D90', 0):.3f}"))

        # Update ratios and differences
        ratios = dist_stats.get("ratios", {})
        self.distribution_table.setItem(6, 3, QTableWidgetItem(f"{ratios.get('d90_d10_ratio', 0):.3f}"))
        self.distribution_table.setItem(7, 3, QTableWidgetItem(f"{ratios.get('d90_d10_diff', 0):.3f}"))
        self.distribution_table.setItem(8, 3, QTableWidgetItem(f"{ratios.get('d75_d25_ratio', 0):.3f}"))
        self.distribution_table.setItem(9, 3, QTableWidgetItem(f"{ratios.get('d75_d25_diff', 0):.3f}"))

        # Update class percentages
        class_percentages = dist_stats.get("class_percentages", {})

        # First column of percentages
        self.distribution_table.setItem(0, 2, QTableWidgetItem(f"{class_percentages.get('GRAVEL', 0):.1f}%"))
        self.distribution_table.setItem(1, 2, QTableWidgetItem(f"{class_percentages.get('SAND', 0):.1f}%"))
        self.distribution_table.setItem(2, 2, QTableWidgetItem(f"{class_percentages.get('MUD', 0):.1f}%"))
        self.distribution_table.setItem(3, 2, QTableWidgetItem(f"{class_percentages.get('V FINE SAND', 0):.1f}%"))
        self.distribution_table.setItem(4, 2, QTableWidgetItem(f"{class_percentages.get('V COARSE SILT', 0):.1f}%"))
        self.distribution_table.setItem(5, 2, QTableWidgetItem(f"{class_percentages.get('COARSE GRAVEL', 0):.1f}%"))
        self.distribution_table.setItem(6, 2, QTableWidgetItem(f"{class_percentages.get('MEDIUM GRAVEL', 0):.1f}%"))
        self.distribution_table.setItem(7, 2, QTableWidgetItem(f"{class_percentages.get('FINE GRAVEL', 0):.1f}%"))
        self.distribution_table.setItem(8, 2, QTableWidgetItem(f"{class_percentages.get('V FINE GRAVEL', 0):.1f}%"))
        self.distribution_table.setItem(9, 2, QTableWidgetItem(f"{class_percentages.get('V COARSE SAND', 0):.1f}%"))

        # Second column of percentages
        self.distribution_table.setItem(0, 3, QTableWidgetItem(f"{class_percentages.get('COARSE SAND', 0):.1f}%"))
        self.distribution_table.setItem(1, 3, QTableWidgetItem(f"{class_percentages.get('MEDIUM SAND', 0):.1f}%"))
        self.distribution_table.setItem(2, 3, QTableWidgetItem(f"{class_percentages.get('FINE SAND', 0):.1f}%"))
        self.distribution_table.setItem(5, 3, QTableWidgetItem(f"{class_percentages.get('COARSE SILT', 0):.1f}%"))
        self.distribution_table.setItem(6, 3, QTableWidgetItem(f"{class_percentages.get('MEDIUM SILT', 0):.1f}%"))
        self.distribution_table.setItem(7, 3, QTableWidgetItem(f"{class_percentages.get('FINE SILT', 0):.1f}%"))
        self.distribution_table.setItem(8, 3, QTableWidgetItem(f"{class_percentages.get('V FINE SILT', 0):.1f}%"))
        self.distribution_table.setItem(9, 3, QTableWidgetItem(f"{class_percentages.get('CLAY', 0):.1f}%"))

    def update_moments_table(self):
        """Update the method of moments table with statistics."""
        if not self.statistics or "method_of_moments" not in self.statistics:
            return

        moments = self.statistics["method_of_moments"]

        # Arithmetic column
        arith = moments.get("arithmetic", {})
        self.moments_table.setItem(0, 1, QTableWidgetItem(f"{arith.get('mean', 0):.3f}"))
        self.moments_table.setItem(1, 1, QTableWidgetItem(f"{arith.get('sorting', 0):.3f}"))
        self.moments_table.setItem(2, 1, QTableWidgetItem(f"{arith.get('skewness', 0):.3f}"))
        self.moments_table.setItem(3, 1, QTableWidgetItem(f"{arith.get('kurtosis', 0):.3f}"))

        # Geometric column
        geo = moments.get("geometric", {})
        self.moments_table.setItem(0, 2, QTableWidgetItem(f"{geo.get('mean', 0):.3f}"))
        self.moments_table.setItem(1, 2, QTableWidgetItem(f"{geo.get('sorting', 0):.3f}"))
        self.moments_table.setItem(2, 2, QTableWidgetItem(f"{geo.get('skewness', 0):.3f}"))
        self.moments_table.setItem(3, 2, QTableWidgetItem(f"{geo.get('kurtosis', 0):.3f}"))

        # Logarithmic column
        log = moments.get("logarithmic", {})
        self.moments_table.setItem(0, 3, QTableWidgetItem(f"{log.get('mean', 0):.3f}"))
        self.moments_table.setItem(1, 3, QTableWidgetItem(f"{log.get('sorting', 0):.3f}"))
        self.moments_table.setItem(2, 3, QTableWidgetItem(f"{log.get('skewness', 0):.3f}"))
        self.moments_table.setItem(3, 3, QTableWidgetItem(f"{log.get('kurtosis', 0):.3f}"))

    def update_folk_ward_table(self):
        """Update the Folk & Ward method table with statistics."""
        if not self.statistics or "folk_ward_method" not in self.statistics:
            return

        folk_ward = self.statistics["folk_ward_method"]

        # Geometric column
        geo = folk_ward.get("geometric", {})
        self.folk_ward_table.setItem(0, 1, QTableWidgetItem(f"{geo.get('mean', 0):.3f}"))
        self.folk_ward_table.setItem(1, 1, QTableWidgetItem(f"{geo.get('sorting', 0):.3f}"))
        self.folk_ward_table.setItem(2, 1, QTableWidgetItem(f"{geo.get('skewness', 0):.3f}"))
        self.folk_ward_table.setItem(3, 1, QTableWidgetItem(f"{geo.get('kurtosis', 0):.3f}"))

        # Logarithmic column
        log = folk_ward.get("logarithmic", {})
        self.folk_ward_table.setItem(0, 2, QTableWidgetItem(f"{log.get('mean', 0):.3f}"))
        self.folk_ward_table.setItem(1, 2, QTableWidgetItem(f"{log.get('sorting', 0):.3f}"))
        self.folk_ward_table.setItem(2, 2, QTableWidgetItem(f"{log.get('skewness', 0):.3f}"))
        self.folk_ward_table.setItem(3, 2, QTableWidgetItem(f"{log.get('kurtosis', 0):.3f}"))

        # Description column
        desc = folk_ward.get("description", {})
        self.folk_ward_table.setItem(0, 3, QTableWidgetItem(""))  # No description for mean
        self.folk_ward_table.setItem(1, 3, QTableWidgetItem(desc.get("sorting", "")))
        self.folk_ward_table.setItem(2, 3, QTableWidgetItem(desc.get("skewness", "")))
        self.folk_ward_table.setItem(3, 3, QTableWidgetItem(desc.get("kurtosis", "")))

    def update_detailed_text(self):
        """Update the detailed statistics text."""
        if not self.statistics:
            self.detailed_text.setText("No statistics available.")
            return

        try:
            # Format the statistics as a detailed text report
            text = "DETAILED GRAIN SIZE STATISTICS\n"
            text += "==============================\n\n"

            # Sample information
            text += "SAMPLE INFORMATION\n"
            text += "------------------\n"
            text += f"Sample Identity: {self.sample_info.get('identity', '')}\n"
            text += f"Analyst & Date: {self.sample_info.get('analyst_date', '')}\n"
            text += f"Sample Type: {self.sample_info.get('type', '')}\n"
            text += f"Textural Group: {self.textural_group_label.text()}\n"
            text += f"Sediment Name: {self.sediment_name_label.text()}\n"
            text += f"Total Grain Count: {self.statistics.get('sample_statistics', {}).get('grain_count', 0)}\n"
            text += f"Scale Factor: {self.scale_factor} µm/pixel\n"

            # Add note about the measurement type
            if self.measurement_type == "ecd":
                text += "Note: Grain size is measured as the Equivalent Circular Diameter (diameter of a circle with the same area as the grain).\n\n"
            elif self.measurement_type == "length":
                text += "Note: Grain size is measured as the Length (longest Feret diameter of the grain).\n\n"
            else:
                text += f"Note: Grain size is measured using {self.measurement_type}.\n\n"

            # Grain size distribution
            if "grain_size_distribution" in self.statistics:
                dist = self.statistics["grain_size_distribution"]
                text += "GRAIN SIZE DISTRIBUTION\n"
                text += "-----------------------\n"

                # Modes
                text += "Modes:\n"
                for i, (key, value) in enumerate(dist.get("modes", {}).items()):
                    text += f"  Mode {i+1}: {value:.3f} mm\n"

                # Percentiles
                text += "\nPercentiles:\n"
                for key, value in dist.get("percentiles", {}).items():
                    text += f"  {key}: {value:.3f} mm\n"

                # Ratios
                text += "\nRatios and Differences:\n"
                for key, value in dist.get("ratios", {}).items():
                    text += f"  {key}: {value:.3f}\n"

                # Class percentages
                text += "\nSize Class Percentages:\n"
                for key, value in dist.get("class_percentages", {}).items():
                    text += f"  {key}: {value:.1f}%\n"

                text += "\n"

            # Method of moments
            if "method_of_moments" in self.statistics:
                moments = self.statistics["method_of_moments"]
                text += "METHOD OF MOMENTS\n"
                text += "-----------------\n"

                # Arithmetic
                if self.measurement_type == "ecd":
                    text += "Arithmetic (mm, Equivalent Circular Diameter):\n"
                elif self.measurement_type == "length":
                    text += "Arithmetic (mm, Length - Longest Feret Diameter):\n"
                else:
                    text += "Arithmetic (mm):\n"

                for key, value in moments.get("arithmetic", {}).items():
                    text += f"  {key.capitalize()}: {value:.3f}\n"

                # Geometric
                if self.measurement_type == "ecd":
                    text += "\nGeometric (mm, Equivalent Circular Diameter):\n"
                elif self.measurement_type == "length":
                    text += "\nGeometric (mm, Length - Longest Feret Diameter):\n"
                else:
                    text += "\nGeometric (mm):\n"

                for key, value in moments.get("geometric", {}).items():
                    text += f"  {key.capitalize()}: {value:.3f}\n"

                # Logarithmic
                if self.measurement_type == "ecd":
                    text += "\nLogarithmic (Φ, Equivalent Circular Diameter):\n"
                elif self.measurement_type == "length":
                    text += "\nLogarithmic (Φ, Length - Longest Feret Diameter):\n"
                else:
                    text += "\nLogarithmic (Φ):\n"
                for key, value in moments.get("logarithmic", {}).items():
                    text += f"  {key.capitalize()}: {value:.3f}\n"

                text += "\n"

            # Folk & Ward method
            if "folk_ward_method" in self.statistics:
                folk_ward = self.statistics["folk_ward_method"]
                text += "FOLK & WARD METHOD\n"
                text += "------------------\n"

                # Geometric
                if self.measurement_type == "ecd":
                    text += "Geometric (mm, Equivalent Circular Diameter):\n"
                elif self.measurement_type == "length":
                    text += "Geometric (mm, Length - Longest Feret Diameter):\n"
                else:
                    text += "Geometric (mm):\n"

                for key, value in folk_ward.get("geometric", {}).items():
                    text += f"  {key.capitalize()}: {value:.3f}\n"

                # Logarithmic
                if self.measurement_type == "ecd":
                    text += "\nLogarithmic (Φ, Equivalent Circular Diameter):\n"
                elif self.measurement_type == "length":
                    text += "\nLogarithmic (Φ, Length - Longest Feret Diameter):\n"
                else:
                    text += "\nLogarithmic (Φ):\n"
                for key, value in folk_ward.get("logarithmic", {}).items():
                    text += f"  {key.capitalize()}: {value:.3f}\n"

                # Descriptions
                text += "\nDescriptions:\n"
                for key, value in folk_ward.get("description", {}).items():
                    if value:  # Only show non-empty descriptions
                        text += f"  {key.capitalize()}: {value}\n"

            self.detailed_text.setText(text)
        except Exception as e:
            logger.error(f"Error updating detailed statistics text: {e}")
            self.detailed_text.setText(f"Error generating detailed statistics: {str(e)}")


    def setup_plots_tab(self):
        """Set up the Plots tab with grain size distribution plots."""
        layout = QVBoxLayout(self.plots_tab)

        # Create a widget to hold the plots
        plots_container = QWidget()
        plots_layout = QVBoxLayout(plots_container)

        # Create matplotlib figures for the plots
        self.figures = []
        self.canvases = []

        # Grain Size Distribution Histogram
        fig1 = Figure(figsize=(8, 4))
        canvas1 = FigureCanvas(fig1)
        self.figures.append(fig1)
        self.canvases.append(canvas1)
        plots_layout.addWidget(canvas1)

        # Cumulative Frequency Curve
        fig2 = Figure(figsize=(8, 4))
        canvas2 = FigureCanvas(fig2)
        self.figures.append(fig2)
        self.canvases.append(canvas2)
        plots_layout.addWidget(canvas2)

        # Probability Plot
        fig3 = Figure(figsize=(8, 4))
        canvas3 = FigureCanvas(fig3)
        self.figures.append(fig3)
        self.canvases.append(canvas3)
        plots_layout.addWidget(canvas3)

        # Add the plots container to a scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidget(plots_container)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

    @Slot()
    def export_to_csv(self, file_path=None, show_message=True):
        """
        Export the statistics to a CSV file.

        Args:
            file_path: Optional path to save the CSV file. If not provided, a file dialog will be shown.
            show_message: Whether to show a success message box after saving. Default is True.

        Returns:
            The path to the saved CSV file, or None if the operation was cancelled or failed.
        """
        if not self.statistics:
            if show_message:
                QMessageBox.warning(self, "No Data", "No statistics available to export.")
            return None

        # If no file path is provided, get it from the user
        if not file_path:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Save Statistics as CSV", "", "CSV Files (*.csv);;All Files (*)"
            )

            if not file_path:
                return None  # User cancelled

        try:
            # Ensure the file has a .csv extension
            if not file_path.lower().endswith('.csv'):
                file_path += '.csv'

            # Create the CSV content with UTF-8 encoding to handle special characters like Φ
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write header
                writer.writerow(["Grain Size Statistical Report"])
                writer.writerow([f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"])
                writer.writerow([])

                # Sample information
                writer.writerow(["SAMPLE STATISTICS"])
                writer.writerow(["Sample Identity", self.sample_info.get("identity", "")])
                writer.writerow(["Analyst & Date", self.sample_info.get("analyst_date", "")])
                writer.writerow(["Sample Type", self.sample_info.get("type", "")])
                writer.writerow(["Textural Group", self.textural_group_label.text()])
                writer.writerow(["Sediment Name", self.sediment_name_label.text()])
                writer.writerow(["Total Grain Count", self.statistics.get("sample_statistics", {}).get("grain_count", 0)])
                writer.writerow(["Scale Factor (µm/pixel)", self.scale_factor])
                writer.writerow([])

                # Grain size distribution
                if "grain_size_distribution" in self.statistics:
                    dist = self.statistics["grain_size_distribution"]

                    writer.writerow(["GRAIN SIZE DISTRIBUTION"])

                    # Modes
                    writer.writerow(["Modes (mm)"])
                    for i, (key, value) in enumerate(dist.get("modes", {}).items()):
                        writer.writerow([f"Mode {i+1}", f"{value:.3f}"])
                    writer.writerow([])

                    # Percentiles
                    writer.writerow(["Percentiles (mm)"])
                    for key, value in dist.get("percentiles", {}).items():
                        writer.writerow([key, f"{value:.3f}"])
                    writer.writerow([])

                    # Ratios
                    writer.writerow(["Ratios and Differences"])
                    for key, value in dist.get("ratios", {}).items():
                        writer.writerow([key, f"{value:.3f}"])
                    writer.writerow([])

                    # Class percentages
                    writer.writerow(["Size Class Percentages (%)"])
                    for key, value in dist.get("class_percentages", {}).items():
                        writer.writerow([key, f"{value:.1f}"])
                    writer.writerow([])

                # Method of moments
                if "method_of_moments" in self.statistics:
                    moments = self.statistics["method_of_moments"]

                    writer.writerow(["METHOD OF MOMENTS"])

                    # Headers for the three types - use 'phi' instead of the Greek letter to avoid encoding issues
                    if self.measurement_type == "ecd":
                        writer.writerow(["Parameter", "Arithmetic (mm, ECD)", "Geometric (mm, ECD)", "Logarithmic (phi, ECD)"])
                    elif self.measurement_type == "length":
                        writer.writerow(["Parameter", "Arithmetic (mm, Length)", "Geometric (mm, Length)", "Logarithmic (phi, Length)"])
                    else:
                        writer.writerow(["Parameter", "Arithmetic (mm)", "Geometric (mm)", "Logarithmic (phi)"])

                    # Mean
                    writer.writerow([
                        "Mean",
                        f"{moments.get('arithmetic', {}).get('mean', 0):.3f}",
                        f"{moments.get('geometric', {}).get('mean', 0):.3f}",
                        f"{moments.get('logarithmic', {}).get('mean', 0):.3f}"
                    ])

                    # Sorting
                    writer.writerow([
                        "Sorting (σ)",
                        f"{moments.get('arithmetic', {}).get('sorting', 0):.3f}",
                        f"{moments.get('geometric', {}).get('sorting', 0):.3f}",
                        f"{moments.get('logarithmic', {}).get('sorting', 0):.3f}"
                    ])

                    # Skewness
                    writer.writerow([
                        "Skewness (Sk)",
                        f"{moments.get('arithmetic', {}).get('skewness', 0):.3f}",
                        f"{moments.get('geometric', {}).get('skewness', 0):.3f}",
                        f"{moments.get('logarithmic', {}).get('skewness', 0):.3f}"
                    ])

                    # Kurtosis
                    writer.writerow([
                        "Kurtosis (K)",
                        f"{moments.get('arithmetic', {}).get('kurtosis', 0):.3f}",
                        f"{moments.get('geometric', {}).get('kurtosis', 0):.3f}",
                        f"{moments.get('logarithmic', {}).get('kurtosis', 0):.3f}"
                    ])
                    writer.writerow([])

                # Folk & Ward method
                if "folk_ward_method" in self.statistics:
                    folk_ward = self.statistics["folk_ward_method"]

                    writer.writerow(["FOLK & WARD METHOD"])

                    # Headers - use 'phi' instead of the Greek letter to avoid encoding issues
                    if self.measurement_type == "ecd":
                        writer.writerow(["Parameter", "Geometric (mm, ECD)", "Logarithmic (phi, ECD)", "Description"])
                    elif self.measurement_type == "length":
                        writer.writerow(["Parameter", "Geometric (mm, Length)", "Logarithmic (phi, Length)", "Description"])
                    else:
                        writer.writerow(["Parameter", "Geometric (mm)", "Logarithmic (phi)", "Description"])

                    # Mean
                    writer.writerow([
                        "Mean",
                        f"{folk_ward.get('geometric', {}).get('mean', 0):.3f}",
                        f"{folk_ward.get('logarithmic', {}).get('mean', 0):.3f}",
                        ""
                    ])

                    # Sorting
                    writer.writerow([
                        "Sorting (σ)",
                        f"{folk_ward.get('geometric', {}).get('sorting', 0):.3f}",
                        f"{folk_ward.get('logarithmic', {}).get('sorting', 0):.3f}",
                        folk_ward.get('description', {}).get('sorting', "")
                    ])

                    # Skewness
                    writer.writerow([
                        "Skewness (Sk)",
                        f"{folk_ward.get('geometric', {}).get('skewness', 0):.3f}",
                        f"{folk_ward.get('logarithmic', {}).get('skewness', 0):.3f}",
                        folk_ward.get('description', {}).get('skewness', "")
                    ])

                    # Kurtosis
                    writer.writerow([
                        "Kurtosis (K)",
                        f"{folk_ward.get('geometric', {}).get('kurtosis', 0):.3f}",
                        f"{folk_ward.get('logarithmic', {}).get('kurtosis', 0):.3f}",
                        folk_ward.get('description', {}).get('kurtosis', "")
                    ])

            # Only show the success message if show_message is True
            if show_message:
                QMessageBox.information(self, "Export Successful", f"Statistics exported to {file_path}")

            # Return the file path for potential use by the caller
            return file_path

        except Exception as e:
            logger.exception(f"Error exporting statistics to CSV: {e}")
            if show_message:
                QMessageBox.critical(self, "Export Error", f"Failed to export statistics: {str(e)}")
            return None

    @Slot()
    def save_plots(self, directory=None, show_message=True):
        """
        Save the plots as image files.

        Args:
            directory: Optional directory to save the plots. If not provided, a directory dialog will be shown.
            show_message: Whether to show a success message box after saving. Default is True.

        Returns:
            A list of paths to the saved plot files, or None if the operation was cancelled or failed.
        """
        if not self.figures:
            if show_message:
                QMessageBox.warning(self, "No Plots", "No plots available to save.")
            return None

        # If no directory is provided, get it from the user
        if not directory:
            directory = QFileDialog.getExistingDirectory(
                self, "Select Directory to Save Plots", ""
            )

            if not directory:
                return None  # User cancelled

        try:
            saved_files = []
            plot_names = ["Grain_Size_Histogram", "Cumulative_Frequency", "Probability_Plot"]

            for i, fig in enumerate(self.figures):
                # Use fixed filenames without timestamps when saving to project state
                # This prevents accumulating multiple versions of the same plot
                if i < len(plot_names):
                    file_name = f"{plot_names[i]}.png"
                else:
                    file_name = f"Plot_{i+1}.png"

                file_path = os.path.join(directory, file_name)
                fig.savefig(file_path, dpi=300, bbox_inches='tight')
                saved_files.append(file_path)

            if saved_files:
                # Only show the success message if show_message is True
                if show_message:
                    QMessageBox.information(
                        self, "Plots Saved",
                        f"Saved {len(saved_files)} plots to {directory}"
                    )

                # Return the list of saved files for potential use by the caller
                return saved_files
            else:
                # Always show warning if no plots were saved
                if show_message:
                    QMessageBox.warning(self, "No Plots Saved", "No plots were saved.")
                return None

        except Exception as e:
            logger.exception(f"Error saving plots: {e}")
            if show_message:
                QMessageBox.critical(self, "Save Error", f"Failed to save plots: {str(e)}")
            return None

    def generate_plots(self):
        """Generate the plots based on the statistics."""
        if not self.statistics or "grain_size_distribution" not in self.statistics:
            return

        try:
            # Extract grain size data
            dist_stats = self.statistics["grain_size_distribution"]

            # Get grain sizes from the statistics
            # For this example, we'll use synthetic data based on the percentiles
            percentiles = dist_stats.get("percentiles", {})

            # Create a synthetic distribution based on the percentiles
            # This is a simplified approach - in a real implementation, you would use the actual grain size data
            d10 = percentiles.get("D10", 0)
            d50 = percentiles.get("D50", 0)
            d90 = percentiles.get("D90", 0)

            if d10 > 0 and d50 > 0 and d90 > 0:
                # Generate synthetic grain sizes based on the percentiles
                # This is a log-normal distribution approximation
                mean_log = np.log(d50)
                sigma_log = (np.log(d90) - np.log(d10)) / 2.56  # Approximation for log-normal

                # Generate synthetic grain sizes
                np.random.seed(42)  # For reproducibility
                grain_sizes = np.random.lognormal(mean=mean_log, sigma=sigma_log, size=1000)

                # Plot 1: Grain Size Distribution Histogram
                ax1 = self.figures[0].add_subplot(111)
                ax1.hist(grain_sizes, bins=30, alpha=0.7, color='blue', edgecolor='black')

                # Set x-axis label based on measurement type
                if self.measurement_type == "ecd":
                    ax1.set_xlabel('Equivalent Circular Diameter (mm)')
                elif self.measurement_type == "length":
                    ax1.set_xlabel('Length - Longest Feret Diameter (mm)')
                else:
                    ax1.set_xlabel('Grain Size (mm)')

                ax1.set_ylabel('Frequency')
                ax1.set_title('Grain Size Distribution Histogram')
                ax1.grid(True, linestyle='--', alpha=0.7)
                self.figures[0].tight_layout()
                self.canvases[0].draw()

                # Plot 2: Cumulative Frequency Curve
                ax2 = self.figures[1].add_subplot(111)

                # Sort grain sizes for cumulative plot
                sorted_sizes = np.sort(grain_sizes)
                cumulative = np.arange(1, len(sorted_sizes) + 1) / len(sorted_sizes) * 100

                ax2.plot(sorted_sizes, cumulative, 'b-', linewidth=2)

                # Set x-axis label based on measurement type
                if self.measurement_type == "ecd":
                    ax2.set_xlabel('Equivalent Circular Diameter (mm)')
                elif self.measurement_type == "length":
                    ax2.set_xlabel('Length - Longest Feret Diameter (mm)')
                else:
                    ax2.set_xlabel('Grain Size (mm)')

                ax2.set_ylabel('Cumulative Frequency (%)')
                ax2.set_title('Cumulative Frequency Curve')
                ax2.grid(True, linestyle='--', alpha=0.7)

                # Add percentile markers
                for p, label in [(10, 'D10'), (50, 'D50'), (90, 'D90')]:
                    p_val = percentiles.get(f"D{p}", 0)
                    if p_val > 0:
                        ax2.axvline(x=p_val, color='r', linestyle='--', alpha=0.7)
                        ax2.axhline(y=p, color='r', linestyle='--', alpha=0.7)
                        ax2.text(p_val, p+5, f"{label}: {p_val:.3f} mm",
                                 ha='center', va='bottom', bbox=dict(facecolor='white', alpha=0.7))

                self.figures[1].tight_layout()
                self.canvases[1].draw()

                # Plot 3: Probability Plot (log scale)
                ax3 = self.figures[2].add_subplot(111)

                # Use log scale for x-axis
                ax3.semilogx(sorted_sizes, cumulative, 'b-', linewidth=2)

                # Set x-axis label based on measurement type
                if self.measurement_type == "ecd":
                    ax3.set_xlabel('Equivalent Circular Diameter (mm) - Log Scale')
                elif self.measurement_type == "length":
                    ax3.set_xlabel('Length - Longest Feret Diameter (mm) - Log Scale')
                else:
                    ax3.set_xlabel('Grain Size (mm) - Log Scale')

                ax3.set_ylabel('Cumulative Frequency (%)')
                ax3.set_title('Grain Size Probability Plot')
                ax3.grid(True, linestyle='--', alpha=0.7)

                # Add phi scale on top
                ax3_top = ax3.twiny()
                phi_ticks = [-4, -3, -2, -1, 0, 1, 2, 3, 4]
                mm_ticks = [2**(-phi) for phi in phi_ticks]
                ax3_top.semilogx(mm_ticks, [0]*len(mm_ticks), alpha=0)  # Invisible line to set scale
                ax3_top.set_xticks(mm_ticks)
                ax3_top.set_xticklabels([str(phi) for phi in phi_ticks])
                # Use 'phi' instead of the Greek letter to avoid encoding issues
                ax3_top.set_xlabel('Grain Size (phi)')

                self.figures[2].tight_layout()
                self.canvases[2].draw()

        except Exception as e:
            logger.exception(f"Error generating plots: {e}")

    def generate_statistics(self):
        """Generate statistics from the provided DataFrame."""
        if self.df is None or self.df.empty:
            logger.warning("No data available for statistics generation")
            return

        try:
            # Generate the statistics report
            self.statistics = generate_grain_statistics_report(
                self.df,
                self.scale_factor,
                measurement_type=self.measurement_type
            )

            # Update the UI with the statistics
            self.update_sample_info()
            self.update_distribution_table()
            self.update_moments_table()
            self.update_folk_ward_table()
            self.update_detailed_text()

            # Generate the plots
            self.generate_plots()

            logger.info(f"Grain statistics generated successfully using {self.measurement_type} measurement")
        except Exception as e:
            logger.error(f"Error generating grain statistics: {e}")


def show_grain_statistics_dialog(parent, df, scale_factor, sample_info=None, measurement_type="ecd"):
    """
    Show the grain statistics dialog.

    Args:
        parent: Parent widget
        df: DataFrame containing grain measurements
        scale_factor: Scale factor in microns/pixel
        sample_info: Dictionary containing sample information
        measurement_type: Type of measurement to use for grain size statistics.
                         Options: "ecd" (Equivalent Circular Diameter) or "length" (Longest Feret Diameter)

    Returns:
        The dialog instance
    """
    dialog = GrainStatisticsDialog(parent, df, scale_factor, sample_info, measurement_type)
    dialog.exec()
    return dialog
