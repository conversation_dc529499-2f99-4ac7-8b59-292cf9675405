# src/widgets/segment_grid_widget.py

from PySide6.QtWidgets import (QWidget, QGridLayout, QLabel, QScrollArea,
                              QVBoxLayout, QSizePolicy, QFrame, QMenu)
from PySide6.QtGui import QPixmap, QImage, QCursor, QAction
from PySide6.QtCore import Qt, QSize, Signal

class SegmentGridWidget(QScrollArea):
    """Widget for displaying multiple segments in a grid layout."""

    # Signals
    segment_selected = Signal(tuple)  # Emits the color of the selected segment (or None for all)
    save_segment_as_png = Signal(tuple)  # Emits the color of the segment to save as PNG
    export_segment_as_annotations = Signal(tuple)  # Emits the color of the segment to export as annotations

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWidgetResizable(True)

        # Create the container widget
        self.container = QWidget()
        self.setWidget(self.container)

        # Create the grid layout
        self.grid_layout = QGridLayout(self.container)
        self.grid_layout.setContentsMargins(5, 5, 5, 5)
        self.grid_layout.setSpacing(5)

        # Set minimum height
        self.setMinimumHeight(150)

        # Set vertical scroll policy
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # Store segment widgets and their colors
        self.segment_widgets = []
        self.segment_colors = {}

    def clear_segments(self):
        """Clear all segments from the grid."""
        # Remove all widgets from the grid
        for i in reversed(range(self.grid_layout.count())):
            widget = self.grid_layout.itemAt(i).widget()
            if widget:
                widget.setParent(None)
                widget.deleteLater()

        # Clear the list of segment widgets
        self.segment_widgets = []

    def add_segment(self, pixmap, segment_name, segment_color, percentage=None):
        """Add a segment to the grid.

        Args:
            pixmap: QPixmap of the segment
            segment_name: Name of the segment
            segment_color: RGB tuple of the segment color
            percentage: Optional percentage of the segment (float)
        """
        # Create a frame to hold the segment
        frame = QFrame()
        frame.setFrameShape(QFrame.Shape.Box)
        frame.setFrameShadow(QFrame.Shadow.Raised)
        frame.setLineWidth(1)
        frame.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        frame.customContextMenuRequested.connect(lambda pos, f=frame: self.show_context_menu(pos, f))

        # Make the frame clickable
        frame.mousePressEvent = lambda event, f=frame: self.handle_frame_click(event, f)
        frame.setCursor(Qt.CursorShape.PointingHandCursor)  # Change cursor to hand when hovering

        # Store the segment color with the frame
        frame.setProperty("segment_color", segment_color)

        # Create a layout for the frame
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)

        # Create a label for the image
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Scale the pixmap to a reasonable size
        scaled_pixmap = pixmap.scaled(QSize(200, 200),
                                     Qt.AspectRatioMode.KeepAspectRatio,
                                     Qt.TransformationMode.SmoothTransformation)
        image_label.setPixmap(scaled_pixmap)
        layout.addWidget(image_label)

        # Create a label for the segment name and percentage
        name_text = segment_name
        if percentage is not None:
            name_text = f"{segment_name}: {percentage:.2f}%"

        name_label = QLabel(name_text)
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(name_label)

        # Add the frame to the grid
        row = len(self.segment_widgets) // 4  # 4 columns
        col = len(self.segment_widgets) % 4
        self.grid_layout.addWidget(frame, row, col)

        # Add to the list of segment widgets and store the color
        self.segment_widgets.append(frame)
        self.segment_colors[frame] = segment_color

    def show_context_menu(self, pos, frame):
        """Show context menu for a segment frame.

        Args:
            pos: Position where the context menu should be shown
            frame: The frame that was right-clicked
        """
        # Get the segment color from the frame
        segment_color = frame.property("segment_color")
        if not segment_color:
            return

        # Create context menu
        context_menu = QMenu(self)

        # Add action to show full segmentation
        show_all_action = QAction("Show Full Segmentation", self)
        show_all_action.triggered.connect(lambda: self.segment_selected.emit(None))
        context_menu.addAction(show_all_action)

        # Add separator
        context_menu.addSeparator()

        # Add action to save segment as PNG
        save_png_action = QAction("Save Segment as PNG", self)
        save_png_action.triggered.connect(lambda: self.save_segment_as_png.emit(segment_color))
        context_menu.addAction(save_png_action)

        # Add action to export segment as annotations
        export_annotations_action = QAction("Export as Annotations (NPZ)", self)
        export_annotations_action.triggered.connect(lambda: self.export_segment_as_annotations.emit(segment_color))
        context_menu.addAction(export_annotations_action)

        # Show the menu at the cursor position
        context_menu.exec(QCursor.pos())

    def handle_frame_click(self, event, frame):
        """Handle mouse click on a segment frame.

        Args:
            event: The mouse event
            frame: The frame that was clicked
        """
        # Only handle left-click events
        if event.button() == Qt.MouseButton.LeftButton:
            self.select_segment(frame)

        # Call the parent class's mousePressEvent to maintain default behavior
        super(QFrame, frame).mousePressEvent(event)

    def select_segment(self, frame):
        """Emit signal to display the selected segment in the main view.

        Args:
            frame: The frame containing the segment to display
        """
        # Get the segment color from the frame
        segment_color = frame.property("segment_color")

        # Emit the signal with the segment color
        if segment_color:
            self.segment_selected.emit(segment_color)

            # Provide visual feedback that the segment was selected
            for widget in self.segment_widgets:
                if widget == frame:
                    widget.setStyleSheet("border: 2px solid blue;")
                else:
                    widget.setStyleSheet("")

    def set_segments(self, segment_pixmaps, segment_names, segment_colors, percentages=None):
        """Set all segments at once.

        Args:
            segment_pixmaps: List of QPixmap objects
            segment_names: List of segment names
            segment_colors: List of segment colors (RGB tuples)
            percentages: Optional list of percentages for each segment
        """
        # Clear existing segments
        self.clear_segments()

        # Add each segment
        if percentages is None:
            percentages = [None] * len(segment_pixmaps)

        for pixmap, name, color, percentage in zip(segment_pixmaps, segment_names, segment_colors, percentages):
            self.add_segment(pixmap, name, color, percentage)
