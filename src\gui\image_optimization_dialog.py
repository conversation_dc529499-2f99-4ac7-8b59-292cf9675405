# src/gui/image_optimization_dialog.py

import os
import cv2
import logging
import numpy as np
from typing import List, Tu<PERSON>, Dict, Optional, Set
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                              QPushButton, QRadioButton, QButtonGroup,
                              QSpinBox, QGroupBox, QCheckBox, QComboBox,
                              QProgressBar, QMessageBox, QTableWidget,
                              QTableWidgetItem, QHeaderView, QAbstractItemView,
                              QSplitter, QWidget, QTabWidget, QScrollArea)
from PySide6.QtCore import Qt, Signal, Slot, QSize, QFileInfo, QTimer
from PySide6.QtGui import QPixmap, QImage

from src.utils.image_utils import resize_image, convert_cvimage_to_qpixmap, get_image_info

logger = logging.getLogger(__name__)

class ImageOptimizationDialog(QDialog):
    """Dialog for optimizing large images during import."""

    # Signal to report progress
    progress_updated = Signal(int)

    def __init__(self, image_paths: List[str], parent=None):
        super().__init__(parent)
        self.setWindowTitle("Image Optimization")
        self.setMinimumSize(800, 600)  # Larger size for the enhanced UI

        self.image_paths = image_paths
        self.optimized_images = {}  # Will store optimized images
        self.optimization_info = {}  # Will store info about optimizations applied
        self.selected_images = set()  # Will store which images are selected for optimization

        # Default optimization settings
        self.max_size_mb = 20
        self.target_size = (1024, 1024)
        self.target_format = "jpg"
        self.patch_size = (1024, 1024)
        self.patch_overlap = (64, 64)

        # Analyze images
        self.all_images = self._analyze_all_images()
        self.large_images = {path: info for path, info in self.all_images.items()
                            if info.get("needs_optimization", False)}

        # Setup UI
        self._setup_ui()

    def _analyze_all_images(self) -> Dict[str, Dict]:
        """Analyzes all images and returns information about them."""
        all_images = {}

        for path in self.image_paths:
            # Use the utility function to get image info
            info = get_image_info(path)

            if info is None:
                logger.warning(f"Could not read image: {path}")
                continue

            # Check if image needs optimization
            needs_optimization = False
            reasons = []

            if info["size_mb"] > self.max_size_mb:
                needs_optimization = True
                reasons.append(f"File size: {info['size_mb']:.1f}MB > {self.max_size_mb}MB")

            width, height = info["dimensions"]
            if width > 1500 or height > 1500:
                needs_optimization = True
                reasons.append(f"Dimensions: {width}x{height} > 1500x1500")

            # Store image info
            all_images[path] = {
                "path": path,
                "size_mb": info["size_mb"],
                "dimensions": info["dimensions"],
                "reasons": reasons if needs_optimization else [],
                "format": info["format"],
                "needs_optimization": needs_optimization
            }

            # Add to selected images if it needs optimization
            if needs_optimization:
                self.selected_images.add(path)

        return all_images

    def _identify_large_images(self) -> Dict[str, Dict]:
        """Identifies images that are too large and need optimization.
        This method is kept for backward compatibility.
        """
        return {path: info for path, info in self.all_images.items()
                if info.get("needs_optimization", False)}

    def _setup_ui(self):
        """Sets up the enhanced dialog UI with per-image optimization settings."""
        main_layout = QVBoxLayout(self)

        # Info label
        if self.large_images:
            info_text = f"Found {len(self.large_images)} large images that could be optimized for better performance."
        else:
            info_text = "All images are within optimal size parameters."

        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)

        # If no large images, just show OK button
        if not self.large_images and not self.all_images:
            ok_button = QPushButton("OK")
            ok_button.clicked.connect(self.accept)
            main_layout.addWidget(ok_button)
            return

        # Create a splitter for the main content
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter, 1)  # Give it stretch factor

        # Left side: Image table with checkboxes
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # Create table for images
        self.image_table = QTableWidget()
        self.image_table.setColumnCount(4)  # Filename, Size, Dimensions, Format
        self.image_table.setHorizontalHeaderLabels(["Filename", "Size (MB)", "Dimensions", "Format"])

        # Set column widths
        self.image_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.image_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.image_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.image_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)

        # Set row height to be taller
        self.image_table.verticalHeader().setDefaultSectionSize(40)  # Increase row height

        self.image_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.image_table.setSelectionMode(QAbstractItemView.ExtendedSelection)

        # Set alternating row colors for better readability
        self.image_table.setAlternatingRowColors(True)

        # Connect double-click to preview
        self.image_table.doubleClicked.connect(self._on_table_double_clicked)

        # Populate table with all images
        self.image_table.setRowCount(len(self.all_images))
        for i, (path, info) in enumerate(self.all_images.items()):
            # Filename
            filename_item = QTableWidgetItem(os.path.basename(path))
            filename_item.setFlags((filename_item.flags() & ~Qt.ItemIsEditable) | Qt.ItemIsSelectable)
            # Store path in all items for easier access
            filename_item.setData(Qt.UserRole, path)
            self.image_table.setItem(i, 0, filename_item)

            # Size
            size_item = QTableWidgetItem(f"{info['size_mb']:.2f}")
            size_item.setFlags((size_item.flags() & ~Qt.ItemIsEditable) | Qt.ItemIsSelectable)
            size_item.setData(Qt.UserRole, path)
            # Use red text for large sizes instead of highlighting
            if info['size_mb'] > self.max_size_mb:
                size_item.setForeground(Qt.red)
            self.image_table.setItem(i, 1, size_item)

            # Dimensions
            width, height = info["dimensions"]
            dim_item = QTableWidgetItem(f"{width} x {height}")
            dim_item.setFlags((dim_item.flags() & ~Qt.ItemIsEditable) | Qt.ItemIsSelectable)
            dim_item.setData(Qt.UserRole, path)
            # Use red text for large dimensions instead of highlighting
            if width > 1500 or height > 1500:
                dim_item.setForeground(Qt.red)
            self.image_table.setItem(i, 2, dim_item)

            # Format
            format_item = QTableWidgetItem(info["format"])
            format_item.setFlags((format_item.flags() & ~Qt.ItemIsEditable) | Qt.ItemIsSelectable)
            format_item.setData(Qt.UserRole, path)
            self.image_table.setItem(i, 3, format_item)

            # Add to selected images if it needs optimization
            if info.get("needs_optimization", False):
                self.selected_images.add(path)

        # Connect selection changes
        self.image_table.itemSelectionChanged.connect(self._on_selection_changed)

        # Create a group box for selection buttons to make them more prominent
        selection_group = QGroupBox("Image Selection")
        selection_group_layout = QVBoxLayout(selection_group)

        # Create buttons with increased size and better styling
        select_all_btn = QPushButton("Select All Images")
        select_all_btn.setMinimumHeight(40)  # Increase button height
        select_all_btn.setStyleSheet("font-weight: bold;")  # Make text bold
        select_all_btn.clicked.connect(self._select_all_images)
        selection_group_layout.addWidget(select_all_btn)

        select_none_btn = QPushButton("Deselect All Images")
        select_none_btn.setMinimumHeight(40)  # Increase button height
        select_none_btn.clicked.connect(self._select_no_images)
        selection_group_layout.addWidget(select_none_btn)

        select_large_btn = QPushButton("Select Only Large Images")
        select_large_btn.setMinimumHeight(40)  # Increase button height
        select_large_btn.setStyleSheet("font-weight: bold;")  # Make text bold without specific color
        select_large_btn.clicked.connect(self._select_large_images)
        selection_group_layout.addWidget(select_large_btn)

        # Add the group to the layout
        left_layout.addWidget(selection_group)
        left_layout.addWidget(self.image_table)

        # Right side: Optimization options
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Optimization options
        options_group = QGroupBox("Optimization Options")
        options_layout = QVBoxLayout(options_group)

        # Option 1: Resize
        self.resize_check = QCheckBox("Resize large images")
        self.resize_check.setChecked(True)
        options_layout.addWidget(self.resize_check)

        resize_options = QHBoxLayout()
        resize_options.addWidget(QLabel("Target size:"))

        self.resize_width = QSpinBox()
        self.resize_width.setRange(256, 4096)
        self.resize_width.setValue(1024)
        resize_options.addWidget(self.resize_width)

        resize_options.addWidget(QLabel("x"))

        self.resize_height = QSpinBox()
        self.resize_height.setRange(256, 4096)
        self.resize_height.setValue(1024)
        resize_options.addWidget(self.resize_height)

        # Add maintain aspect ratio checkbox
        self.maintain_aspect_ratio = QCheckBox("Maintain aspect ratio")
        self.maintain_aspect_ratio.setChecked(True)
        resize_options.addWidget(self.maintain_aspect_ratio)

        options_layout.addLayout(resize_options)

        # Option 2: Convert format
        self.convert_check = QCheckBox("Convert to more compressed format")
        options_layout.addWidget(self.convert_check)

        format_options = QHBoxLayout()
        format_options.addWidget(QLabel("Target format:"))

        self.format_combo = QComboBox()
        self.format_combo.addItems(["jpg", "png", "webp"])
        format_options.addWidget(self.format_combo)

        options_layout.addLayout(format_options)

        # Option 3: Patch
        self.patch_check = QCheckBox("Apply patching (for very large images)")
        options_layout.addWidget(self.patch_check)

        patch_options = QHBoxLayout()
        patch_options.addWidget(QLabel("Patch size:"))

        self.patch_width = QSpinBox()
        self.patch_width.setRange(256, 2048)
        self.patch_width.setValue(1024)
        patch_options.addWidget(self.patch_width)

        patch_options.addWidget(QLabel("x"))

        self.patch_height = QSpinBox()
        self.patch_height.setRange(256, 2048)
        self.patch_height.setValue(1024)
        patch_options.addWidget(self.patch_height)

        # Add overlap controls for patching
        patch_options.addWidget(QLabel("Overlap:"))

        self.patch_overlap_spin = QSpinBox()
        self.patch_overlap_spin.setRange(0, 512)
        self.patch_overlap_spin.setValue(64)
        self.patch_overlap_spin.setSuffix(" px")
        patch_options.addWidget(self.patch_overlap_spin)

        options_layout.addLayout(patch_options)

        # Add preview section
        preview_group = QGroupBox("Preview")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_label = QLabel("Select an image to preview optimization")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumHeight(200)
        preview_layout.addWidget(self.preview_label)

        self.preview_button = QPushButton("Generate Preview")
        self.preview_button.clicked.connect(self._generate_preview)
        preview_layout.addWidget(self.preview_button)

        # Add options and preview to right layout
        right_layout.addWidget(options_group)
        right_layout.addWidget(preview_group)

        # Add widgets to splitter
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        # Set initial splitter sizes
        splitter.setSizes([400, 400])

        # Select the first row by default if there are images
        if self.image_table.rowCount() > 0:
            self.image_table.selectRow(0)
            # Generate preview for the first image after a short delay to ensure UI is fully loaded
            QTimer.singleShot(100, self._generate_preview)

        # Progress bar (hidden initially)
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # Main action buttons with improved styling
        buttons_layout = QHBoxLayout()

        self.optimize_button = QPushButton("Optimize Selected Images")
        self.optimize_button.setMinimumHeight(50)  # Taller button
        self.optimize_button.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.optimize_button.clicked.connect(self.optimize_images)
        buttons_layout.addWidget(self.optimize_button)

        self.finish_button = QPushButton("Finish")
        self.finish_button.setMinimumHeight(50)  # Taller button
        self.finish_button.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.finish_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.finish_button)

        self.skip_button = QPushButton("Skip Optimization")
        self.skip_button.setMinimumHeight(50)  # Taller button
        self.skip_button.setStyleSheet("font-size: 14px;")
        self.skip_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.skip_button)

        main_layout.addLayout(buttons_layout)

    def _on_selection_changed(self):
        """Handle changes in table row selection."""
        self.selected_images.clear()
        selected_rows = self.image_table.selectionModel().selectedRows()
        for index in selected_rows:
            row = index.row()
            path = self.image_table.item(row, 0).data(Qt.UserRole)
            self.selected_images.add(path)

        # Update the preview if a single row is selected
        if len(selected_rows) == 1:
            self._generate_preview()

    def _select_all_images(self):
        """Select all images in the table."""
        self.image_table.blockSignals(True)
        self.image_table.selectAll()
        self.selected_images.clear()
        for row in range(self.image_table.rowCount()):
            path = self.image_table.item(row, 0).data(Qt.UserRole)
            self.selected_images.add(path)
        self.image_table.blockSignals(False)

    def _select_no_images(self):
        """Deselect all images in the table."""
        self.image_table.blockSignals(True)
        self.image_table.clearSelection()
        self.selected_images.clear()
        self.image_table.blockSignals(False)

    def _select_large_images(self):
        """Select only large images that need optimization."""
        self.image_table.blockSignals(True)
        self.image_table.clearSelection()
        self.selected_images.clear()

        # Select rows with large images
        for row in range(self.image_table.rowCount()):
            path = self.image_table.item(row, 0).data(Qt.UserRole)
            if path in self.large_images:
                self.image_table.selectRow(row)
                self.selected_images.add(path)
        self.image_table.blockSignals(False)

    def _on_table_double_clicked(self, index):
        """Handle double-click on table row to select and preview the image."""
        # Select the entire row
        row = index.row()
        self.image_table.selectRow(row)

        # Generate preview for the selected image
        self._generate_preview()

    def _generate_preview(self):
        """Generate a preview of the optimization for the selected image."""
        # Get the currently selected row in the table
        selected_rows = self.image_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.information(self, "Preview", "Please select an image to preview.")
            return

        # Get the path from the selected row
        row = selected_rows[0].row()
        path = self.image_table.item(row, 0).data(Qt.UserRole)

        try:
            # Load the image
            img = cv2.imread(path)
            if img is None:
                QMessageBox.warning(self, "Preview Error", f"Could not load image: {path}")
                return

            # Convert BGR to RGB
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

            # Apply resize if selected
            if self.resize_check.isChecked():
                target_width = self.resize_width.value()
                target_height = self.resize_height.value()

                if self.maintain_aspect_ratio.isChecked():
                    # Use the resize_image function that maintains aspect ratio
                    preview_img = resize_image(img_rgb, (target_width, target_height))
                else:
                    # Force exact dimensions
                    preview_img = cv2.resize(img_rgb, (target_width, target_height), interpolation=cv2.INTER_AREA)
            else:
                preview_img = img_rgb

            # Convert to QPixmap for display
            height, width = preview_img.shape[:2]
            bytes_per_line = 3 * width
            q_img = QImage(preview_img.data, width, height, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_img)

            # Scale pixmap to fit in preview label while maintaining aspect ratio
            pixmap = pixmap.scaled(self.preview_label.width(), self.preview_label.height(),
                                  Qt.KeepAspectRatio, Qt.SmoothTransformation)

            # Display the preview
            self.preview_label.setPixmap(pixmap)

            # Show optimization info
            original_size = os.path.getsize(path) / (1024 * 1024)  # MB
            original_dims = img_rgb.shape[:2][::-1]  # width, height
            new_dims = preview_img.shape[:2][::-1]  # width, height

            info_text = f"Original: {original_dims[0]}x{original_dims[1]}, {original_size:.2f} MB\n"
            info_text += f"Optimized: {new_dims[0]}x{new_dims[1]}"

            # Add format info if conversion is selected
            if self.convert_check.isChecked():
                target_format = self.format_combo.currentText()
                info_text += f", Format: {target_format}"

            self.preview_label.setToolTip(info_text)

        except Exception as e:
            QMessageBox.warning(self, "Preview Error", f"Error generating preview: {str(e)}")
            logger.exception(f"Error generating preview for {path}: {e}")

    def _create_patches(self, image, patch_size, overlap):
        """Creates patches from a large image with specified overlap.

        Args:
            image: The input image (numpy array)
            patch_size: Tuple of (width, height) for patch size
            overlap: Overlap in pixels between patches

        Returns:
            List of patches as numpy arrays and their positions
        """
        height, width = image.shape[:2]
        patch_width, patch_height = patch_size

        patches = []
        positions = []

        # Calculate step size (accounting for overlap)
        step_x = patch_width - overlap
        step_y = patch_height - overlap

        # Ensure at least one step in each direction
        if step_x <= 0 or step_y <= 0:
            logger.warning(f"Overlap too large for patch size. Using smaller overlap.")
            step_x = max(patch_width // 2, 1)
            step_y = max(patch_height // 2, 1)

        # Create patches
        for y in range(0, height, step_y):
            for x in range(0, width, step_x):
                # Adjust patch boundaries to not exceed image dimensions
                end_x = min(x + patch_width, width)
                end_y = min(y + patch_height, height)

                # Adjust start position to ensure patch size if possible
                start_x = max(0, end_x - patch_width)
                start_y = max(0, end_y - patch_height)

                # Extract patch
                patch = image[start_y:end_y, start_x:end_x]

                # Only add if patch has reasonable size
                if patch.shape[0] > 20 and patch.shape[1] > 20:
                    patches.append(patch)
                    positions.append((start_x, start_y, end_x, end_y))

        return patches, positions

    def optimize_images(self):
        """Optimizes selected images based on chosen options."""
        # Check if any images are selected
        if not self.selected_images:
            QMessageBox.warning(self, "Warning", "No images selected for optimization.")
            return

        # Setup progress bar
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(self.selected_images))
        self.progress_bar.setValue(0)

        # Disable buttons during processing
        self.optimize_button.setEnabled(False)
        self.finish_button.setEnabled(False)
        self.skip_button.setEnabled(False)
        self.image_table.setEnabled(False)

        # Get selected options
        do_resize = self.resize_check.isChecked()
        do_convert = self.convert_check.isChecked()
        do_patch = self.patch_check.isChecked()
        maintain_aspect = self.maintain_aspect_ratio.isChecked()

        # Get parameters for each option
        if do_resize:
            target_width = self.resize_width.value()
            target_height = self.resize_height.value()
            self.target_size = (target_width, target_height)

        if do_convert:
            self.target_format = self.format_combo.currentText()

        if do_patch:
            patch_width = self.patch_width.value()
            patch_height = self.patch_height.value()
            self.patch_size = (patch_width, patch_height)
            self.patch_overlap = self.patch_overlap_spin.value()

        # Process each selected image
        processed_count = 0
        selected_paths = list(self.selected_images)

        for i, path in enumerate(selected_paths):
            try:
                # Get image info
                info = self.all_images.get(path)
                if not info:
                    logger.warning(f"No info found for image: {path}")
                    continue

                # Load image
                img = cv2.imread(path)
                if img is None:
                    logger.warning(f"Could not load image: {path}")
                    continue

                # Convert BGR to RGB for processing
                img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

                # Apply selected optimizations
                optimization_methods = []

                # Handle patching first (it creates multiple images)
                if do_patch:
                    patches, positions = self._create_patches(
                        img_rgb,
                        self.patch_size,
                        self.patch_overlap
                    )

                    # Store each patch
                    for j, (patch, pos) in enumerate(zip(patches, positions)):
                        patch_key = f"{path}#patch{j}"

                        # Apply resize to patch if needed
                        if do_resize:
                            if maintain_aspect:
                                patch = resize_image(patch, self.target_size)
                            else:
                                patch = cv2.resize(patch, self.target_size, interpolation=cv2.INTER_AREA)
                            optimization_methods.append("resize")

                        # Store the patch
                        self.optimized_images[patch_key] = patch

                        # Store patch info
                        self.optimization_info[patch_key] = {
                            "method": "patching" + ("+resize" if do_resize else ""),
                            "original_path": path,
                            "patch_index": j,
                            "patch_position": pos,
                            "patch_size": self.patch_size,
                            "patch_overlap": self.patch_overlap,
                            "format": self.target_format if do_convert else info["format"],
                            "maintain_aspect_ratio": maintain_aspect if do_resize else False
                        }

                    # Mark the original path as having patches
                    self.optimization_info[path] = {
                        "method": "patching",
                        "has_patches": True,
                        "patch_count": len(patches),
                        "patch_size": self.patch_size,
                        "patch_overlap": self.patch_overlap
                    }

                    optimization_methods.append("patching")
                    processed_count += 1
                else:
                    # Apply resize if selected
                    if do_resize:
                        if maintain_aspect:
                            img_rgb = resize_image(img_rgb, self.target_size)
                        else:
                            img_rgb = cv2.resize(img_rgb, self.target_size, interpolation=cv2.INTER_AREA)
                        optimization_methods.append("resize")

                    # Store the optimized image
                    self.optimized_images[path] = img_rgb

                    # Store optimization info
                    self.optimization_info[path] = {
                        "method": "+".join(optimization_methods) or "none",
                        "original_size": info["dimensions"],
                        "new_size": img_rgb.shape[:2][::-1] if do_resize else info["dimensions"],
                        "format": self.target_format if do_convert else info["format"],
                        "maintain_aspect_ratio": maintain_aspect if do_resize else False
                    }

                    processed_count += 1

                # Add format conversion info if selected
                if do_convert and path in self.optimization_info:
                    self.optimization_info[path]["format_conversion"] = True
                    self.optimization_info[path]["original_format"] = info["format"]
                    self.optimization_info[path]["new_format"] = self.target_format
                    optimization_methods.append("format_conversion")

            except Exception as e:
                logger.exception(f"Error optimizing image {path}: {e}")

            # Update progress
            self.progress_bar.setValue(i + 1)

        # Calculate total output images (including patches)
        regular_images = sum(1 for k in self.optimized_images.keys() if '#' not in k)
        patch_count = sum(info.get("patch_count", 0) for info in self.optimization_info.values()
                         if info.get("has_patches", False))
        total_output = regular_images + patch_count

        # Show completion message
        QMessageBox.information(
            self,
            "Optimization Complete",
            f"Successfully optimized {processed_count} of {len(self.selected_images)} selected images, "
            f"creating {total_output} output images/patches.\n\n"
            f"You can continue optimizing more images or click 'Finish' when done."
        )

        # Re-enable UI elements
        self.optimize_button.setEnabled(True)
        self.finish_button.setEnabled(True)
        self.skip_button.setEnabled(True)
        self.image_table.setEnabled(True)

        # Clear selection to allow for new selections
        self.image_table.clearSelection()

    def _resize_image(self, img):
        """Resizes an image to the target size."""
        return resize_image(img, self.target_size)

    def get_optimized_images(self) -> Dict[str, np.ndarray]:
        """Returns the dictionary of optimized images."""
        return self.optimized_images

    def get_optimization_info(self) -> Dict[str, Dict]:
        """Returns information about the optimizations applied."""
        return self.optimization_info
