"""
Modern Dark Theme for VisionLab Ai Application
"""

from PySide6.QtGui import QPalette, QColor
from PySide6.QtCore import Qt

def define_modern_dark_palette():
    """Creates a modern dark palette for the application."""
    palette = QPalette()
    
    # Main colors - darker base with blue accent
    dark_base = QColor(30, 30, 30)        # Very dark gray (almost black)
    dark_alt = QColor(45, 45, 45)         # Dark gray
    dark_widget = QColor(53, 53, 53)      # Medium dark gray
    dark_button = QColor(60, 60, 60)      # Slightly lighter gray
    accent_color = QColor(0, 120, 212)    # Blue accent color
    text_color = QColor(240, 240, 240)    # Almost white
    disabled_text = QColor(128, 128, 128) # Medium gray
    
    # Set the color palette
    palette.setColor(QPalette.ColorRole.Window, dark_widget)
    palette.setColor(QPalette.ColorRole.WindowText, text_color)
    palette.setColor(QPalette.ColorRole.Base, dark_base)
    palette.setColor(QPalette.ColorRole.AlternateBase, dark_alt)
    palette.setColor(QPalette.ColorRole.ToolTipBase, dark_widget)
    palette.setColor(QPalette.ColorRole.ToolTipText, text_color)
    palette.setColor(QPalette.ColorRole.Text, text_color)
    palette.setColor(QPalette.ColorRole.Button, dark_button)
    palette.setColor(QPalette.ColorRole.ButtonText, text_color)
    palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.white)
    
    # Highlight and link colors
    palette.setColor(QPalette.ColorRole.Link, accent_color)
    palette.setColor(QPalette.ColorRole.Highlight, accent_color)
    palette.setColor(QPalette.ColorRole.HighlightedText, Qt.GlobalColor.white)
    
    # Disabled states
    palette.setColor(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Text, disabled_text)
    palette.setColor(QPalette.ColorGroup.Disabled, QPalette.ColorRole.ButtonText, disabled_text)
    palette.setColor(QPalette.ColorGroup.Disabled, QPalette.ColorRole.Highlight, QColor(80, 80, 80))
    palette.setColor(QPalette.ColorGroup.Disabled, QPalette.ColorRole.HighlightedText, disabled_text)
    
    return palette

# Modern dark stylesheet with subtle gradients and rounded corners
MODERN_DARK_STYLESHEET = """
/* Global Styles */
QWidget {
    background-color: #1e1e1e;
    color: #f0f0f0;
    font-family: 'Segoe UI', Arial, sans-serif;
}

QMainWindow, QDialog {
    background-color: #1e1e1e;
}

/* Group Box Styling */
QGroupBox {
    border: 1px solid #3a3a3a;
    border-radius: 4px;
    margin-top: 12px;
    padding-top: 8px;
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    left: 10px;
    padding: 0 5px;
    color: #0078d4;
}

/* Button Styling */
QPushButton {
    background-color: #3c3c3c;
    border: 1px solid #5a5a5a;
    border-radius: 4px;
    padding: 5px 15px;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #505050;
    border: 1px solid #6a6a6a;
}

QPushButton:pressed {
    background-color: #0078d4;
    color: white;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    color: #707070;
    border: 1px solid #3a3a3a;
}

/* Tool Button Styling */
QToolButton {
    background-color: #3c3c3c;
    border: 1px solid #5a5a5a;
    border-radius: 4px;
    padding: 3px;
}

QToolButton:hover {
    background-color: #505050;
}

QToolButton:pressed {
    background-color: #0078d4;
    color: white;
}

QToolButton:disabled {
    background-color: #2a2a2a;
    color: #707070;
}

/* Combobox Styling */
QComboBox {
    background-color: #2d2d2d;
    border: 1px solid #5a5a5a;
    border-radius: 4px;
    padding: 2px 10px 2px 5px;
    min-height: 20px;
}

QComboBox:hover {
    background-color: #3a3a3a;
}

QComboBox:disabled {
    background-color: #2a2a2a;
    color: #707070;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #5a5a5a;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

QComboBox::down-arrow {
    image: url(src/gui/styles/icons/down_arrow.png);
}

QComboBox QAbstractItemView {
    background-color: #2d2d2d;
    border: 1px solid #5a5a5a;
    selection-background-color: #0078d4;
    selection-color: white;
}

/* Spinbox Styling */
QSpinBox, QDoubleSpinBox {
    background-color: #2d2d2d;
    border: 1px solid #5a5a5a;
    border-radius: 4px;
    padding: 2px 5px;
    min-height: 20px;
}

QSpinBox:hover, QDoubleSpinBox:hover {
    background-color: #3a3a3a;
}

QSpinBox:disabled, QDoubleSpinBox:disabled {
    background-color: #2a2a2a;
    color: #707070;
}

/* Slider Styling */
QSlider::groove:horizontal {
    border: 1px solid #5a5a5a;
    height: 4px;
    background: #2d2d2d;
    margin: 2px 0;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background: #0078d4;
    border: 1px solid #0078d4;
    width: 12px;
    height: 12px;
    margin: -5px 0;
    border-radius: 6px;
}

QSlider::handle:horizontal:hover {
    background: #1a8fe3;
    border: 1px solid #1a8fe3;
}

/* Checkbox Styling */
QCheckBox {
    spacing: 5px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #5a5a5a;
    border-radius: 3px;
    background-color: #2d2d2d;
}

QCheckBox::indicator:checked {
    background-color: #0078d4;
    border: 1px solid #0078d4;
    image: url(src/gui/styles/icons/check.png);
}

QCheckBox::indicator:hover {
    border: 1px solid #0078d4;
}

/* Radio Button Styling */
QRadioButton {
    spacing: 5px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #5a5a5a;
    border-radius: 8px;
    background-color: #2d2d2d;
}

QRadioButton::indicator:checked {
    background-color: #0078d4;
    border: 1px solid #0078d4;
    image: url(src/gui/styles/icons/radio_check.png);
}

QRadioButton::indicator:hover {
    border: 1px solid #0078d4;
}

/* Line Edit Styling */
QLineEdit {
    background-color: #2d2d2d;
    border: 1px solid #5a5a5a;
    border-radius: 4px;
    padding: 2px 5px;
    min-height: 20px;
}

QLineEdit:hover {
    border: 1px solid #0078d4;
}

QLineEdit:focus {
    border: 1px solid #0078d4;
    background-color: #333333;
}

/* Text Edit Styling */
QTextEdit, QPlainTextEdit {
    background-color: #2d2d2d;
    border: 1px solid #5a5a5a;
    border-radius: 4px;
}

QTextEdit:hover, QPlainTextEdit:hover {
    border: 1px solid #0078d4;
}

QTextEdit:focus, QPlainTextEdit:focus {
    border: 1px solid #0078d4;
    background-color: #333333;
}

/* Tab Widget Styling */
QTabWidget::pane {
    border: 1px solid #3a3a3a;
    border-radius: 4px;
    top: -1px;
}

QTabBar::tab {
    background-color: #2d2d2d;
    border: 1px solid #3a3a3a;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 5px 10px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #0078d4;
    color: white;
}

QTabBar::tab:!selected {
    margin-top: 2px;
}

QTabBar::tab:hover:!selected {
    background-color: #3a3a3a;
}

/* List and Tree View Styling */
QListView, QTreeView, QTableView {
    background-color: #2d2d2d;
    border: 1px solid #3a3a3a;
    border-radius: 4px;
    alternate-background-color: #333333;
}

QListView::item, QTreeView::item, QTableView::item {
    padding: 4px;
}

QListView::item:selected, QTreeView::item:selected, QTableView::item:selected {
    background-color: #0078d4;
    color: white;
}

QListView::item:hover, QTreeView::item:hover, QTableView::item:hover {
    background-color: #3a3a3a;
}

/* Header View Styling */
QHeaderView::section {
    background-color: #3c3c3c;
    border: 1px solid #5a5a5a;
    padding: 4px;
}

QHeaderView::section:hover {
    background-color: #505050;
}

/* Scrollbar Styling */
QScrollBar:vertical {
    background-color: #2d2d2d;
    width: 12px;
    margin: 12px 0 12px 0;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #5a5a5a;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #0078d4;
}

QScrollBar::add-line:vertical {
    border: none;
    background: none;
    height: 12px;
    subcontrol-position: bottom;
    subcontrol-origin: margin;
}

QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 12px;
    subcontrol-position: top;
    subcontrol-origin: margin;
}

QScrollBar:horizontal {
    background-color: #2d2d2d;
    height: 12px;
    margin: 0 12px 0 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #5a5a5a;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #0078d4;
}

QScrollBar::add-line:horizontal {
    border: none;
    background: none;
    width: 12px;
    subcontrol-position: right;
    subcontrol-origin: margin;
}

QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
    width: 12px;
    subcontrol-position: left;
    subcontrol-origin: margin;
}

/* Progress Bar Styling */
QProgressBar {
    border: 3px solid #3a3a3a;
    border-radius: 8px;
    background-color: #2d2d2d;
    text-align: center;
    color: white;
}

QProgressBar::chunk {
    background-color: #0078d4;
    border-radius: 8px;
}

/* Menu Styling */
QMenuBar {
    background-color: #2d2d2d;
    border-bottom: 1px solid #3a3a3a;
}

QMenuBar::item {
    background: transparent;
    padding: 5px 10px;
}

QMenuBar::item:selected {
    background-color: #0078d4;
    color: white;
}

QMenu {
    background-color: #2d2d2d;
    border: 1px solid #3a3a3a;
}

QMenu::item {
    padding: 5px 30px 5px 20px;
}

QMenu::item:selected {
    background-color: #0078d4;
    color: white;
}

QMenu::separator {
    height: 1px;
    background-color: #3a3a3a;
    margin: 5px 0;
}

/* Status Bar Styling */
QStatusBar {
    background-color: #2d2d2d;
    border-top: 1px solid #3a3a3a;
    color: #f0f0f0;
}

/* Dock Widget Styling */
QDockWidget {
    titlebar-close-icon: url(src/gui/styles/icons/close.png);
    titlebar-normal-icon: url(src/gui/styles/icons/undock.png);
}

QDockWidget::title {
    background-color: #3c3c3c;
    padding-left: 5px;
    padding-top: 2px;
}

/* Graphics View Styling */
QGraphicsView {
    background-color: #1e1e1e;
    border: 1px solid #3a3a3a;
    border-radius: 4px;
}

/* Specific Widget Styling */
#grain_image_counter_label, #process_image_counter_label {
    color: #0078d4;
    font-weight: bold;
}

/* Navigation buttons styling */
#grain_prev_image_btn, #grain_next_image_btn, 
#process_prev_image_btn, #process_next_image_btn,
#prev_image_button, #next_image_button {
    background-color: #0078d4;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-weight: bold;
}

#grain_prev_image_btn:hover, #grain_next_image_btn:hover,
#process_prev_image_btn:hover, #process_next_image_btn:hover,
#prev_image_button:hover, #next_image_button:hover {
    background-color: #1a8fe3;
}

#grain_prev_image_btn:pressed, #grain_next_image_btn:pressed,
#process_prev_image_btn:pressed, #process_next_image_btn:pressed,
#prev_image_button:pressed, #next_image_button:pressed {
    background-color: #005fa3;
}

#grain_prev_image_btn:disabled, #grain_next_image_btn:disabled,
#process_prev_image_btn:disabled, #process_next_image_btn:disabled,
#prev_image_button:disabled, #next_image_button:disabled {
    background-color: #3c3c3c;
    color: #707070;
}
"""

def apply_modern_dark_theme(app):
    """Apply the modern dark theme to the application."""
    # Set the palette
    palette = define_modern_dark_palette()
    app.setPalette(palette)
    
    # Apply the stylesheet
    app.setStyleSheet(MODERN_DARK_STYLESHEET)
    
    return True
