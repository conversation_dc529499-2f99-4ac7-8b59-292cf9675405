# src/gui/handlers/trainable_segmentation_handlers.py

import os
import re
import json
import pickle
import logging
from functools import partial

# Use our custom JSON utility module
from src.utils import json_utils
from datetime import datetime

import cv2
import matplotlib.pyplot as plt
import numpy as np

# Configure logger
logger = logging.getLogger(__name__)
from PySide6.QtGui import QImage, QPixmap, QColor, QCursor, QIcon
from PySide6.QtWidgets import QFileDialog, QMessageBox, QColorDialog, QInputDialog, QToolTip, QProgressDialog, QDialog
import xgboost as xgb
from skimage import feature, future, segmentation
from PySide6.QtCore import Qt, QTimer  # <--- IMPORTANT: Import Qt and QTimer here

# Import theme-aware button styles
from src.gui.styles.theme_aware_buttons import (
    clear_button_styles,
    get_normal_button_style,
    get_active_button_style,
    get_toggleable_button_style,
    get_sam_tool_button_style,
    get_positive_point_button_style,
    get_negative_point_button_style,
    get_accept_button_style,
    get_reject_button_style,
    get_clear_button_style
)

from src.utils.image_utils import convert_cvimage_to_qimage
from src.gui.multi_image_gallery import MultiImageGallery

# Define constants for default label names and colors
DEFAULT_LABEL_NAMES = [
    "Segment 1",
    "Segment 2",
    "Segment 3",
    "Segment 4",
    "Segment 5"
]

# Define a constant for mask colors to avoid repetition
DEFAULT_MASK_COLORS = np.array([
    [255, 0, 0],  # Segment 1 (red)
    [0, 255, 0],  # Segment 2 (green)
    [0, 0, 255],  # Segment 3 (blue)
    [255, 255, 0],  # Segment 4 (yellow)
    [255, 0, 255],  # Segment 5 (magenta)
    [0, 0, 0]  # Background (transparent)
])

# Use the constants for the actual mask colors
MASK_COLORS = DEFAULT_MASK_COLORS.copy()


class TrainableSegmentationHandlers:
    """Class for handling trainable segmentation-related functions."""

    def __init__(self):
        # Initialize multi-image handler
        from src.gui.handlers.multi_image_trainable_handler import MultiImageTrainableHandler
        from src.gui.multi_image_gallery import MultiImageGallery
        from src.segmentation.sam_segmentation_handler import SAMSegmentationHandler
        from src.gui.handlers.annotation_path_manager import AnnotationPathManager
        from src.gui.handlers.label_settings_manager import LabelSettingsManager
        from PySide6.QtCore import QTimer
        self.multi_image_handler = MultiImageTrainableHandler()

        # Initialize annotation path manager
        self.annotation_path_manager = AnnotationPathManager()
        logger.debug("Initialized annotation_path_manager in TrainableSegmentationHandlers.__init__")

        # Initialize label settings manager
        self.label_settings_manager = LabelSettingsManager()
        logger.debug("Initialized label_settings_manager in TrainableSegmentationHandlers.__init__")

        # The project will be set later in set_project method

        # Initialize SAM handler
        self.sam_handler = SAMSegmentationHandler()
        self.sam_magic_wand_active = False
        self.temp_sam_prediction = None
        self.sam_bbox = None
        self.drawing_sam_bbox = False

        # Initialize other attributes
        self.training_labels = None
        self.features = None
        self.classifier = None
        self.result = None
        self.current_label = 1
        self.drawing_mode = False
        self.erasing_mode = False
        self.mask_overlay_alpha = 0.5  # Transparency of mask overlay
        self.brush_size = 5  # Size of the drawing brush
        # Initialize feature_params with default values
        self.feature_params = {
            'intensity': True,
            'edges': True,
            'texture': True,
            'sigma_min': 1,
            'sigma_max': 16,
            'num_sigma': 10
        }
        self.last_draw_pos = None  # Store the last drawn position
        self.preview_labels = None # Store temporary labels for preview
        self.current_image_index = 0  # Track current image index
        self.image_paths = []  # Store all loaded image paths
        self.current_point_type = 1  # Default to positive points (1=positive, 0=negative)

        # Initialize annotation history stack for undo functionality
        self.annotation_history = {}  # Dictionary to store history for each image
        self.max_history_size = 20  # Maximum number of history states to keep per image

        # Initialize label management
        # Check if we have saved label settings
        saved_label_names = self.label_settings_manager.get_label_names()
        saved_label_colors = self.label_settings_manager.get_label_colors()

        if saved_label_names and len(saved_label_names) > 0:
            # Use saved label names
            self.label_names = saved_label_names
            logger.debug(f"Using saved label_names: {self.label_names}")
        else:
            # Use default label names
            self.label_names = {}
            for i, name in enumerate(DEFAULT_LABEL_NAMES):
                self.label_names[i+1] = name  # 1-indexed labels
            logger.debug(f"Initialized label_names with defaults: {self.label_names}")

            # Save the default label names
            self.label_settings_manager.update_label_names(self.label_names)

        if saved_label_colors and len(saved_label_colors) > 0:
            # Convert saved colors to numpy array
            max_label = max(saved_label_colors.keys())
            self.mask_colors = np.zeros((max_label, 3), dtype=np.uint8)
            for label, color in saved_label_colors.items():
                if 1 <= label <= max_label:
                    self.mask_colors[label-1] = color
            logger.debug("Using saved mask_colors")
        else:
            # Use default colors
            self.mask_colors = DEFAULT_MASK_COLORS.copy()
            logger.debug("Initialized mask_colors with defaults")

            # Save the default colors
            self.label_settings_manager.update_label_colors(self.mask_colors)

        self.max_label_index = max(len(self.label_names), len(DEFAULT_LABEL_NAMES))

        # Initialize button styles
        self.setup_button_styles()


        # Connect mouse events for drawing - Correct way to connect if you're using a custom QGraphicsView
        if hasattr(self, 'trainable_original_view'):  # Check if the view exists.  Crucial!
            self.trainable_original_view.mouse_pressed.connect(self.on_mouse_pressed)
            self.trainable_original_view.mouse_moved.connect(self.on_mouse_moved)
            self.trainable_original_view.mouse_released.connect(self.on_mouse_released)

        # Connect apply to current image button
        if hasattr(self, 'apply_to_current_button'):
            self.apply_to_current_button.clicked.connect(self.apply_to_current_image)

        # Connect SAM buttons
        if hasattr(self, 'sam_magic_wand_button'):
            self.sam_magic_wand_button.clicked.connect(self.toggle_sam_magic_wand)
        if hasattr(self, 'sam_magic_wand_point_button'):
            self.sam_magic_wand_point_button.clicked.connect(self.toggle_sam_point_prompt)
        if hasattr(self, 'sam_magic_wand_neg_point_button'):
            self.sam_magic_wand_neg_point_button.clicked.connect(self.toggle_sam_neg_point_prompt)
        if hasattr(self, 'accept_sam_button'):
            self.accept_sam_button.clicked.connect(self.accept_sam_prediction)
        if hasattr(self, 'reject_sam_button'):
            self.reject_sam_button.clicked.connect(self.reject_sam_prediction)

        # Connect tab widget change signal
        if hasattr(self, 'trainable_tab_widget'):
            self.trainable_tab_widget.currentChanged.connect(self.on_tab_changed)

        # Connect manage labels button
        if hasattr(self, 'manage_labels_button'):
            self.manage_labels_button.clicked.connect(self.show_label_management_dialog)
            logger.debug("Connected manage_labels_button to show_label_management_dialog")

        # Connect brush size slider
        # This section is commented out as it's been replaced by the code below

        # Initialize brush_size from the slider value, or set a default.
        # Also initialize the label text.
        if hasattr(self, 'trainable_brush_size_slider'):
            self.brush_size = self.trainable_brush_size_slider.value()
            logger.debug(f"Initialized self.brush_size to {self.brush_size} from trainable_brush_size_slider")
            if hasattr(self, 'trainable_brush_size_label'):
                self.trainable_brush_size_label.setText(str(self.brush_size))
                logger.debug(f"Initialized trainable_brush_size_label text to {self.brush_size}")
            else:
                logger.debug("trainable_brush_size_label not found for initialization")
        else:
            self.brush_size = 5  # Default brush size if slider isn't found
            logger.debug(f"trainable_brush_size_slider not found, self.brush_size set to default {self.brush_size}")

        # Connect undo button
        if hasattr(self, 'undo_last_button'):
            self.undo_last_button.clicked.connect(self.undo_last_annotation)
            logger.debug("Connected undo_last_button to undo_last_annotation")


        # Annotation loading/saving functionality has been removed

        # Initialize label selection combo box
        if hasattr(self, 'label_selection_combo'):
            self.update_label_color_indicator()

    def setup_button_styles(self):
        """Sets up the initial button styles and tooltips."""
        # Drawing tools
        if hasattr(self, 'draw_button'):
            self.draw_button.setStyleSheet(clear_button_styles())
            self.draw_button.setToolTip("Click to activate drawing mode. Click again to deactivate.")

        if hasattr(self, 'erase_button'):
            self.erase_button.setStyleSheet(clear_button_styles())
            self.erase_button.setToolTip("Click to activate erasing mode. Click again to deactivate.")

        if hasattr(self, 'undo_last_button'):
            self.undo_last_button.setStyleSheet(clear_button_styles())
            self.undo_last_button.setToolTip("Undo the last annotation action.")
            self.undo_last_button.setEnabled(False)  # Initially disabled until there's something to undo

        if hasattr(self, 'clear_labels_button'):
            self.clear_labels_button.setStyleSheet(clear_button_styles())
            self.clear_labels_button.setToolTip("Clear all labels from the current image.")

        # SAM tools
        if hasattr(self, 'sam_magic_wand_button'):
            self.sam_magic_wand_button.setStyleSheet(clear_button_styles())
            self.sam_magic_wand_button.setToolTip("Click to activate magic wand tool. Draw a box around the object.")

        if hasattr(self, 'sam_magic_wand_point_button'):
            self.sam_magic_wand_point_button.setStyleSheet(clear_button_styles())
            self.sam_magic_wand_point_button.setToolTip("Click to activate positive point tool. Click on foreground objects.")

        if hasattr(self, 'sam_magic_wand_neg_point_button'):
            self.sam_magic_wand_neg_point_button.setStyleSheet(clear_button_styles())
            self.sam_magic_wand_neg_point_button.setToolTip("Click to toggle negative point mode. Use with positive points.")
            # Initially disable the negative point button until positive point is activated
            self.sam_magic_wand_neg_point_button.setEnabled(False)

        # Accept/Reject buttons - initially disabled
        if hasattr(self, 'accept_sam_button'):
            self.accept_sam_button.setStyleSheet(clear_button_styles())
            self.accept_sam_button.setToolTip("Accept the current mask and add it to annotations.")
            self.accept_sam_button.setEnabled(False)

        if hasattr(self, 'reject_sam_button'):
            self.reject_sam_button.setStyleSheet(clear_button_styles())
            self.reject_sam_button.setToolTip("Reject the current mask and try again.")
            self.reject_sam_button.setEnabled(False)

    def on_tab_changed(self, index):
        """Handle tab changes in the trainable segmentation page.

        Args:
            index (int): The index of the newly selected tab
        """
        try:
            # Get the tab text to identify which tab we're on
            if hasattr(self, 'trainable_tab_widget'):
                # Get the previous tab text
                previous_index = self.trainable_tab_widget.currentIndex()
                previous_tab_text = self.trainable_tab_widget.tabText(previous_index)

                # Get the new tab text
                current_tab_text = self.trainable_tab_widget.tabText(index)
                logger.info(f"Switched from tab: {previous_tab_text} to tab: {current_tab_text}")

                # Save annotations to multi_image_handler before switching tabs
                if previous_tab_text == "Image & Mask" and hasattr(self, 'multi_image_handler') and hasattr(self, 'training_labels') and self.training_labels is not None:
                    current_path = self.multi_image_handler.current_image_path
                    if current_path:
                        logger.debug(f"Saving annotations for {current_path} before switching tabs")

                        # Create a fresh copy to avoid reference issues
                        annotations_copy = self.training_labels.copy()

                        # Ensure the annotations are a numpy array of type uint8
                        if not isinstance(annotations_copy, np.ndarray):
                            logger.debug("Converting annotations to numpy array")
                            annotations_copy = np.array(annotations_copy, dtype=np.uint8)
                        elif annotations_copy.dtype != np.uint8:
                            logger.debug(f"Converting annotations dtype from {annotations_copy.dtype} to uint8")
                            annotations_copy = annotations_copy.astype(np.uint8)

                        # Save to multi_image_handler
                        self.multi_image_handler.set_annotations(current_path, annotations_copy)
                        logger.debug("Successfully saved annotations before switching tabs")

                        # Also save label names and colors to make them persistent
                        if hasattr(self, 'label_names') and self.label_names:
                            logger.debug("Saving label_names before switching tabs")
                            if not hasattr(self.multi_image_handler, 'label_names'):
                                self.multi_image_handler.label_names = {}
                            self.multi_image_handler.label_names = self.label_names.copy()

                        # Save mask colors
                        if hasattr(self, 'mask_colors') and self.mask_colors is not None:
                            logger.debug("Saving mask_colors before switching tabs")
                            self.multi_image_handler.mask_colors = self.mask_colors.copy()

                # Update UI based on the selected tab
                if current_tab_text == "Image & Mask":
                    # Show the original image with mask overlay
                    if hasattr(self, 'trainable_image') and self.trainable_image is not None:
                        # Get fresh annotations from multi_image_handler
                        if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
                            current_path = self.multi_image_handler.current_image_path
                            annotations = self.multi_image_handler.get_annotations(current_path)
                            if annotations is not None:
                                logger.debug(f"Retrieved annotations for {current_path} from multi_image_handler")
                                self.training_labels = annotations.copy()

                        self.display_trainable_image()
                elif current_tab_text == "Segmentation Result":
                    # Show the segmentation result
                    if hasattr(self, 'result') and self.result is not None:
                        self.display_segmentation_result()
                elif current_tab_text == "Feature Importance":
                    # Show feature importance if available
                    if hasattr(self, 'classifier') and self.classifier is not None:
                        self.display_feature_importance()
        except Exception as e:
            logger.error(f"Error handling tab change: {e}")
            import traceback
            traceback.print_exc()

    def update_status_message(self, message):
        """Updates the status bar with the current tool information."""
        # Update the main status bar if available
        if hasattr(self, 'statusBar'):
            self.statusBar().showMessage(message, 3000)  # Show for 3 seconds

        # Also update the trainable image info label if available
        if hasattr(self, 'trainable_image_info_label') and self.trainable_image_info_label:
            current_text = self.trainable_image_info_label.text()
            # Keep the image info but add the tool status
            if "Size:" in current_text:
                # Extract the image info part (first two lines)
                image_info = "\n".join(current_text.split("\n")[:2])
                self.trainable_image_info_label.setText(f"{image_info}\n{message}")
            else:
                # Just append the message if no image info
                self.trainable_image_info_label.setText(f"{current_text}\n{message}")

    def open_image_dialog(self, title):
        """Opens a file dialog for selecting an image."""
        file_types = "Image files (*.jpg *.jpeg *.png *.bmp *.tiff *.tif *.webp)"
        file_path, _ = QFileDialog.getOpenFileName(self, title, "", file_types)
        return file_path

    def open_file_dialog(self, title, file_types):
        """Opens a file dialog for opening a file."""
        file_path, _ = QFileDialog.getOpenFileName(self, title, "", file_types)
        return file_path

    def save_file_dialog(self, title, file_types):
        """Opens a file dialog for saving a file."""
        file_path, _ = QFileDialog.getSaveFileName(self, title, "", file_types)
        return file_path


    def trainable_upload_image(self):
        """Uploads multiple images for trainable segmentation.
        NOTE: This method is no longer used as upload buttons have been removed.
        Images are now only loaded from the project hub.
        """
        # This method is kept for compatibility but should not be called directly anymore
        file_types = "Image files (*.jpg *.jpeg *.png *.bmp *.tiff *.tif *.webp)"
        file_paths, _ = QFileDialog.getOpenFileNames(self, "Select Images for Trainable Segmentation", "", file_types)

        if not file_paths:
            return

        # Check if we already have images loaded
        has_existing_images = hasattr(self, 'gallery_dialog') and hasattr(self, 'image_paths') and len(self.image_paths) > 0

        if has_existing_images:
            # Ask if user wants to add to current gallery
            add_to_gallery = QMessageBox.question(
                self,
                "Add to Gallery",
                "Do you want to add these images to the current gallery?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if add_to_gallery == QMessageBox.No:
                # Ask if user wants to keep current annotations
                keep_annotations = QMessageBox.question(
                    self,
                    "Keep Annotations",
                    "Do you want to keep current annotations?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if keep_annotations == QMessageBox.No:
                    # Reset annotations
                    self.multi_image_handler.clear()

                # Create new gallery
                self.gallery_dialog = MultiImageGallery(self)
                self.image_paths = file_paths  # Replace existing paths
                self.current_image_index = 0  # Reset current image index
            else:
                # Add to existing gallery
                self.image_paths.extend(file_paths)  # Add to existing paths
        else:
            # First time loading images, create new gallery
            self.gallery_dialog = MultiImageGallery(self)
            self.image_paths = file_paths  # Store all image paths
            self.current_image_index = 0  # Reset current image index

        # Load the images
        for file_path in file_paths:
            try:
                image = cv2.imread(file_path)
                if image is None:
                    QMessageBox.warning(self, "Warning", f"Error loading image: {os.path.basename(file_path)}")
                    continue

                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                # Add to gallery and multi-image handler
                self.gallery_dialog.add_image(image, os.path.basename(file_path))
                self.multi_image_handler.add_image(file_path, image)
            except Exception as e:
                QMessageBox.warning(self, "Warning", f"Failed to load image {os.path.basename(file_path)}: {str(e)}")

        # Connect gallery selection to image display
        self.gallery_dialog.images_selected.connect(self.on_gallery_images_selected)

        # Connect navigation buttons
        if hasattr(self, 'prev_image_button'):
            self.prev_image_button.clicked.connect(self.show_previous_image)
        if hasattr(self, 'next_image_button'):
            self.next_image_button.clicked.connect(self.show_next_image)
        if hasattr(self, 'remove_image_button'):
            self.remove_image_button.clicked.connect(self.remove_current_image)

        # Update image counter
        self.update_image_counter()

        # Thumbnail display functionality has been removed

        # Show gallery dialog
        self.gallery_dialog.show()

        # Load first image
        if file_paths:
            self.load_image_at_index(0)

    def update_image_counter(self):
        """Updates the image counter label."""
        if hasattr(self, 'image_counter_label'):
            total_images = len(self.image_paths)
            current_index = self.current_image_index + 1 if total_images > 0 else 0
            self.image_counter_label.setText(f"Image {current_index} of {total_images}")

    def sync_gallery_selection_with_current_image(self):
        """Synchronizes the gallery selection with the current image path."""
        if not hasattr(self, 'trainable_gallery') or not hasattr(self, 'multi_image_handler'):
            return

        current_path = self.multi_image_handler.current_image_path
        if not current_path:
            return

        logger.debug(f"Synchronizing gallery selection with current image: {current_path}")

        # Find the index of the current image in the gallery
        for i, path in enumerate(self.trainable_gallery.file_paths):
            if path == current_path:
                logger.debug(f"Found current image in gallery at index {i}")
                # Only update if the selection has changed
                if self.trainable_gallery.selected_index != i:
                    logger.debug(f"Updating gallery selection from {self.trainable_gallery.selected_index} to {i}")
                    self.trainable_gallery.select_image(i)
                    self.current_image_index = i
                return

        logger.debug(f"Current image not found in gallery: {current_path}")

    def remove_current_image(self):
        """Removes the current image from the gallery and multi-image handler."""
        if not hasattr(self, 'image_paths') or not self.image_paths or self.current_image_index < 0 or self.current_image_index >= len(self.image_paths):
            QMessageBox.warning(self, "Warning", "No image selected to remove.")
            return

        # Ask for confirmation
        reply = QMessageBox.question(
            self,
            "Confirm Removal",
            "Are you sure you want to remove this image?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Get the current image path
            current_path = self.image_paths[self.current_image_index]
            image_id = os.path.basename(current_path)

            # Clean up state in the project if available
            if hasattr(self, 'project') and self.project:
                print(f"DEBUG: Cleaning up state for image: {image_id}")
                try:
                    # Clean up state files for this image
                    self.project.cleanup_image_state(image_id)
                    print(f"DEBUG: State cleaned up for image: {image_id}")
                except Exception as e:
                    print(f"DEBUG: Error cleaning up state: {e}")

            # Remove from multi-image handler
            # This will remove the image, annotations, and segmentation results
            self.multi_image_handler.remove_image(current_path)
            print(f"DEBUG: Removed image, annotations, and segmentation results for {current_path}")

            # Remove from image paths list
            self.image_paths.pop(self.current_image_index)

            # Update thumbnail display
            self.update_thumbnail_display()

            # Adjust current index if needed
            if self.image_paths:
                if self.current_image_index >= len(self.image_paths):
                    self.current_image_index = len(self.image_paths) - 1
                # Load the new current image
                self.load_image_at_index(self.current_image_index)
            else:
                # No images left
                self.current_image_index = -1
                self.trainable_image = None
                self.training_labels = None
                self.features = None  # Clear features
                self.result = None    # Clear result
                self.trainable_original_view.clear()
                self.trainable_result_view.clear()
                self.trainable_image_info_label.setText("No image loaded")

            # Update image counter
            self.update_image_counter()

    def show_previous_image(self):
        """Shows the previous image in the sequence."""
        if self.current_image_index > 0:
            self.load_image_at_index(self.current_image_index - 1)

    def show_next_image(self):
        """Shows the next image in the sequence."""
        if self.current_image_index < len(self.image_paths) - 1:
            self.load_image_at_index(self.current_image_index + 1)

    def set_project(self, project):
        """Set the current project and initialize project-specific settings.

        Args:
            project: The project object
        """
        try:
            self.project = project
            print(f"DEBUG: Set project for TrainableSegmentationHandlers: {project.name if project else 'None'}")

            if project:
                # Create the project state directory structure if it doesn't exist
                project_file = project.project_file
                project_dir = os.path.dirname(project_file)
                project_name = project.name

                # Create the state directory structure
                state_dir = os.path.join(project_dir, f"{project_name}_data", "state")
                trainable_dir = os.path.join(state_dir, "trainable_segmentation")

                try:
                    os.makedirs(trainable_dir, exist_ok=True)
                    print(f"DEBUG: Created trainable segmentation state directory: {trainable_dir}")
                except Exception as e:
                    print(f"ERROR: Failed to create state directory: {e}")

                    # Try an alternative approach
                    try:
                        # Create each directory level separately
                        data_dir = os.path.join(project_dir, f"{project_name}_data")
                        if not os.path.exists(data_dir):
                            os.mkdir(data_dir)
                            print(f"DEBUG: Created data directory: {data_dir}")

                        state_dir = os.path.join(data_dir, "state")
                        if not os.path.exists(state_dir):
                            os.mkdir(state_dir)
                            print(f"DEBUG: Created state directory: {state_dir}")

                        trainable_dir = os.path.join(state_dir, "trainable_segmentation")
                        if not os.path.exists(trainable_dir):
                            os.mkdir(trainable_dir)
                            print(f"DEBUG: Created trainable_segmentation directory: {trainable_dir}")
                    except Exception as e2:
                        print(f"ERROR: Failed to create directories step by step: {e2}")

            # Initialize the label settings manager with the project
            if not hasattr(self, 'label_settings_manager'):
                from src.gui.handlers.label_settings_manager import LabelSettingsManager
                self.label_settings_manager = LabelSettingsManager()
                print("DEBUG: Created new label_settings_manager in set_project")

            if hasattr(self, 'label_settings_manager') and project:
                # Set the project for the label settings manager
                self.label_settings_manager.set_project(project)
                print(f"DEBUG: Set project for label_settings_manager")

                # Veapp rify that the settings file path is set
                if hasattr(self.label_settings_manager, 'settings_file_path'):
                    print(f"DEBUG: Label settings file path: {self.label_settings_manager.settings_file_path}")
                else:
                    print(f"ERROR: Label settings file path not set")

                # Reload label settings
                saved_label_names = self.label_settings_manager.get_label_names()
                saved_label_colors = self.label_settings_manager.get_label_colors()

                print(f"DEBUG: Loaded saved_label_names: {saved_label_names}")
                print(f"DEBUG: Loaded saved_label_colors: {saved_label_colors}")

                if saved_label_names and len(saved_label_names) > 0:
                    # Use saved label names
                    self.label_names = saved_label_names
                    print(f"DEBUG: Using saved label_names from project: {self.label_names}")

                    # Update the label selection combo box
                    if hasattr(self, 'label_selection_combo'):
                        self.update_label_color_indicator()

                if saved_label_colors and len(saved_label_colors) > 0:
                    # Convert saved colors to numpy array
                    max_label = max(saved_label_colors.keys())
                    self.mask_colors = np.zeros((max_label, 3), dtype=np.uint8)
                    for label, color in saved_label_colors.items():
                        if 1 <= label <= max_label:
                            self.mask_colors[label-1] = color
                    print(f"DEBUG: Using saved mask_colors from project")

                    # Update the display if we have an image loaded
                    if hasattr(self, 'trainable_image') and self.trainable_image is not None:
                        self.display_trainable_image()

            # Initialize the annotation path manager with the project
            if not hasattr(self, 'annotation_path_manager'):
                from src.gui.handlers.annotation_path_manager import AnnotationPathManager
                self.annotation_path_manager = AnnotationPathManager()
                print("DEBUG: Created new annotation_path_manager in set_project")

            if hasattr(self, 'annotation_path_manager') and project:
                # Load the annotation path mapping from the project state directory
                project_file = project.project_file
                project_dir = os.path.dirname(project_file)
                project_name = project.name
                mapping_file = os.path.join(project_dir, f"{project_name}_data", "state", "trainable_segmentation", "annotation_paths.json")
                if os.path.exists(mapping_file):
                    self.annotation_path_manager.load_from_file(mapping_file)
                    print(f"DEBUG: Loaded annotation path mapping from {mapping_file}")
        except Exception as e:
            print(f"ERROR: Exception in set_project: {e}")
            import traceback
            traceback.print_exc()

    def update_thumbnail_display(self):
        """This method is kept as a placeholder to maintain compatibility with existing code.
        The thumbnail display functionality has been removed."""
        # Thumbnail display functionality has been removed
        pass

    def load_image(self, file_path):
        """Loads and displays an image from the specified file path."""
        print(f"DEBUG: load_image called with file_path: {file_path}")

        # State saving is now manual
        # No automatic state saving before switching images

        # Load and display new image
        image = cv2.imread(file_path)
        if image is not None:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            self.trainable_image = image

            # Add to image paths if not already there
            if not hasattr(self, 'image_paths'):
                self.image_paths = []
            if file_path not in self.image_paths:
                self.image_paths.append(file_path)
                self.current_image_index = len(self.image_paths) - 1
            else:
                self.current_image_index = self.image_paths.index(file_path)

            # Add to multi-image handler if not already there
            try:
                existing_image = self.multi_image_handler.get_image(file_path)
                if existing_image is None:
                    print(f"DEBUG: Adding image to multi_image_handler: {file_path}")
                    self.multi_image_handler.add_image(file_path, image)
                else:
                    print(f"DEBUG: Image already exists in multi_image_handler: {file_path}")
            except Exception as e:
                print(f"DEBUG: Error checking if image exists in multi_image_handler: {e}")
                try:
                    self.multi_image_handler.add_image(file_path, image)
                except Exception as e2:
                    print(f"DEBUG: Error adding image to multi_image_handler: {e2}")

            # Clear current annotations and results before loading new ones
            self.training_labels = None
            self.result = None

            # Set current image path
            self.multi_image_handler.current_image_path = file_path
            self.current_image_path = file_path
            print(f"DEBUG: Set current_image_path to {file_path}")

            # Always try to load annotations from multi_image_handler first
            self.training_labels = self.multi_image_handler.get_annotations(file_path)
            print(f"DEBUG: Attempted to load annotations for {file_path} from multi_image_handler")

            # If no annotations found, initialize with empty labels
            if self.training_labels is None:
                print(f"DEBUG: No annotations found for {file_path}, initializing empty labels")
                self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                self.multi_image_handler.set_annotations(file_path, self.training_labels)
                # Initialize other attributes as new
                self.features = None
                self.classifier = None
                self.result = None
            else:
                print(f"DEBUG: Loaded existing annotations for {file_path} with shape {self.training_labels.shape}")

            # Set the image for MobileSAM
            if hasattr(self, 'sam_handler'):
                if self.sam_handler.set_image(self.trainable_image) is False:
                    QMessageBox.warning(self, "MobileSAM Error", "Failed to set image in MobileSAM predictor.")

            # Display the image
            self.display_trainable_image()

            # Update image info
            h, w, c = self.trainable_image.shape
            # Use 1-based indexing for display to match update_image_counter method
            display_index = self.current_image_index + 1
            total_images = len(self.image_paths) if hasattr(self, 'image_paths') else 0
            self.trainable_image_info_label.setText(f"Image: {os.path.basename(file_path)} ({display_index} of {total_images})\nSize: {w}x{h}\nChannels: {c}")

            # Synchronize gallery selection with current image
            if hasattr(self, 'trainable_gallery'):
                self.sync_gallery_selection_with_current_image()

            # Reset drawing state
            self.drawing_mode = False
            self.erasing_mode = False
            self.draw_button.setText("Draw Label")
            self.erase_button.setText("Erase Label")
            if hasattr(self, 'trainable_original_view'):
                self.trainable_original_view.drawing_enabled = False
                self.trainable_original_view.erasing_enabled = False

            return True
        else:
            QMessageBox.critical(self, "Error", f"Failed to load image at {file_path}. Check file integrity.")
            return False

    def load_image_at_index(self, index):
        """Loads and displays the image at the specified index."""
        if 0 <= index < len(self.image_paths):
            # Store the current image path before switching
            current_path = None
            if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
                current_path = self.multi_image_handler.current_image_path
                print(f"DEBUG: Saving state for current image: {current_path}")

            # Save current annotations, segmentation results, and other state if they exist
            if hasattr(self, 'trainable_image') and current_path:
                # Save annotations
                if self.training_labels is not None:
                    print(f"DEBUG: Saving annotations for {current_path} before switching")
                    self.multi_image_handler.set_annotations(current_path, self.training_labels.copy())

                # Save segmentation results
                if hasattr(self, 'result') and self.result is not None:
                    print(f"DEBUG: Saving segmentation result for {current_path} before switching")
                    self.multi_image_handler.set_segmentation_result(current_path, self.result.copy())

                # Save label names and colors to make them persistent across images
                if hasattr(self, 'label_names') and self.label_names:
                    print(f"DEBUG: Saving label_names to multi_image_handler before switching")
                    if not hasattr(self.multi_image_handler, 'label_names'):
                        self.multi_image_handler.label_names = {}
                    self.multi_image_handler.label_names = self.label_names.copy()

                # Save mask colors
                if hasattr(self, 'mask_colors') and self.mask_colors is not None:
                    print(f"DEBUG: Saving mask_colors to multi_image_handler before switching")
                    self.multi_image_handler.mask_colors = self.mask_colors.copy()

            # Update current index
            self.current_image_index = index
            file_path = self.image_paths[index]
            print(f"DEBUG: Switching to image at index {index}: {file_path}")

            # Load and display new image
            image = cv2.imread(file_path)
            if image is not None:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                self.trainable_image = image

                # Clear current results before loading new ones
                # Don't clear training_labels yet to avoid NoneType errors
                self.result = None

                # Update current image path after saving previous state
                self.multi_image_handler.current_image_path = file_path
                print(f"DEBUG: Switched current_image_path to {file_path}")

                # Update labels and display - ALWAYS get fresh annotations from multi_image_handler
                annotations = self.multi_image_handler.get_annotations(file_path)
                if annotations is not None:
                    print(f"DEBUG: Retrieved annotations for {file_path} from multi_image_handler with shape {annotations.shape}")
                    # Make a copy to ensure we're not modifying the original
                    self.training_labels = annotations.copy()

                    # Ensure the annotations are a numpy array of type uint8
                    if not isinstance(self.training_labels, np.ndarray):
                        print(f"DEBUG: Converting training_labels to numpy array")
                        self.training_labels = np.array(self.training_labels, dtype=np.uint8)
                    elif self.training_labels.dtype != np.uint8:
                        print(f"DEBUG: Converting training_labels dtype from {self.training_labels.dtype} to uint8")
                        self.training_labels = self.training_labels.astype(np.uint8)

                    # Verify dimensions match the image
                    if self.training_labels.shape != self.trainable_image.shape[:2]:
                        print(f"DEBUG: training_labels shape {self.training_labels.shape} doesn't match image shape {self.trainable_image.shape[:2]}")
                        try:
                            # Try to resize training_labels to match image dimensions
                            import cv2
                            resized_labels = cv2.resize(
                                self.training_labels,
                                (self.trainable_image.shape[1], self.trainable_image.shape[0]),
                                interpolation=cv2.INTER_NEAREST
                            )
                            self.training_labels = resized_labels
                            print(f"DEBUG: Resized training_labels to {self.training_labels.shape}")
                            # Save the resized annotations back to the handler
                            self.multi_image_handler.set_annotations(file_path, self.training_labels.copy())
                        except Exception as e:
                            print(f"DEBUG: Error resizing training_labels: {e}")
                            # Create new empty labels with correct dimensions
                            self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                            # Save these empty annotations to the handler
                            self.multi_image_handler.set_annotations(file_path, self.training_labels.copy())
                else:
                    print(f"DEBUG: No annotations found for {file_path} in multi_image_handler, initializing empty labels")
                    # Now it's safe to create empty annotations
                    self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                    # Save these empty annotations to the handler
                    self.multi_image_handler.set_annotations(file_path, self.training_labels.copy())

                # Also load label names and colors from multi_image_handler
                if hasattr(self.multi_image_handler, 'label_names') and self.multi_image_handler.label_names:
                    print(f"DEBUG: Loading label_names from multi_image_handler")
                    self.label_names = self.multi_image_handler.label_names.copy()

                # Load mask colors
                if hasattr(self.multi_image_handler, 'mask_colors') and self.multi_image_handler.mask_colors is not None:
                    print(f"DEBUG: Loading mask_colors from multi_image_handler")
                    self.mask_colors = self.multi_image_handler.mask_colors.copy()

                # Update the label selection combo box
                if hasattr(self, 'label_selection_combo'):
                    self.update_label_selection_combo()

                # At this point, self.training_labels should never be None
                # But add a safety check just in case
                if self.training_labels is None:
                    print(f"WARNING: training_labels is still None after retrieval attempt, creating empty labels")
                    self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                    self.multi_image_handler.set_annotations(file_path, self.training_labels.copy())

                print(f"DEBUG: Using annotations for {file_path} with shape {self.training_labels.shape}")

                # *CRITICAL*: Set the image for MobileSAM *before* any predictions
                if self.sam_handler.set_image(self.trainable_image) is False:
                    QMessageBox.warning(self, "MobileSAM Error", "Failed to set image in MobileSAM predictor.")
                    return  # Exit if image loading fails

                # Load segmentation result if available
                self.result = self.multi_image_handler.get_segmentation_result(file_path)
                if self.result is not None:
                    print(f"DEBUG: Loaded segmentation result for {file_path} with shape {self.result.shape}")
                    print(f"DEBUG: Unique values in result: {np.unique(self.result)}")

                    # Make a copy to ensure we're not modifying the original
                    self.result = self.result.copy()

                    # Ensure the result is saved back to the multi_image_handler
                    self.multi_image_handler.set_segmentation_result(file_path, self.result)
                    print(f"DEBUG: Ensured segmentation result is saved for {file_path}")

                    # Display the segmentation result
                    self.display_segmentation_result()

                    # Switch to the segmentation result tab if we have a result
                    self.trainable_tab_widget.setCurrentIndex(1)
                else:
                    print(f"DEBUG: No segmentation result found for {file_path}")
                    # Reset result to None
                    self.result = None
                    # Switch to the annotation tab
                    self.trainable_tab_widget.setCurrentIndex(0)

                # Display the image with annotations
                self.display_trainable_image()

                # Update image info
                h, w, c = self.trainable_image.shape
                # Use 1-based indexing for display to match update_image_counter method
                display_index = self.current_image_index + 1
                total_images = len(self.image_paths) if hasattr(self, 'image_paths') else 0
                self.trainable_image_info_label.setText(f"Image: {os.path.basename(file_path)} ({display_index} of {total_images})\nSize: {w}x{h}\nChannels: {c}")

                # Update image counter
                self.update_image_counter()

                # Update thumbnail display
                self.update_thumbnail_display()

                # Synchronize gallery selection with current image
                if hasattr(self, 'trainable_gallery'):
                    self.sync_gallery_selection_with_current_image()

                # Get classifier state from multi-image handler
                self.classifier, self.feature_params, label_mapping, mask_colors = self.multi_image_handler.get_classifier()
                if label_mapping is not None:
                    self.label_mapping = label_mapping

                # Get mask colors from multi-image handler
                if mask_colors is not None and hasattr(self, 'mask_colors'):
                    print(f"DEBUG: Retrieved mask_colors from multi_image_handler")
                    # Only update if the shape is compatible
                    if isinstance(mask_colors, np.ndarray) and mask_colors.shape[0] <= self.mask_colors.shape[0]:
                        self.mask_colors[:mask_colors.shape[0]] = mask_colors
                    elif isinstance(mask_colors, list) and len(mask_colors) <= self.mask_colors.shape[0]:
                        self.mask_colors[:len(mask_colors)] = np.array(mask_colors)
                    else:
                        # If shape is not compatible, create a new array with the right size
                        if isinstance(mask_colors, np.ndarray):
                            self.mask_colors = mask_colors.copy()
                        elif isinstance(mask_colors, list):
                            self.mask_colors = np.array(mask_colors)
                        print(f"DEBUG: Updated mask_colors with shape {self.mask_colors.shape}")

                # Get label names from multi-image handler if available
                if hasattr(self.multi_image_handler, 'label_names') and self.multi_image_handler.label_names:
                    print(f"DEBUG: Retrieved label_names from multi_image_handler: {self.multi_image_handler.label_names}")
                    self.label_names = self.multi_image_handler.label_names.copy()

                    # Make sure the max_label_index is updated
                    if self.label_names:
                        self.max_label_index = max(max(self.label_names.keys()), self.max_label_index)
                        print(f"DEBUG: Updated max_label_index to {self.max_label_index}")

                    # Update the label selection combo box
                    self.update_label_color_indicator()

                # Only set features to None if we don't have a result
                if self.result is None:
                    self.features = None

                # Reset drawing state
                self.drawing_mode = False
                self.erasing_mode = False
                self.draw_button.setText("Draw Label")
                self.erase_button.setText("Erase Label")
                if hasattr(self, 'trainable_original_view'):
                    self.trainable_original_view.drawing_enabled = False
                    self.trainable_original_view.erasing_enabled = False

    def toggle_sam_magic_wand(self):
        """Toggles the SAM magic wand tool state."""
        print("toggle_sam_magic_wand called")  # Debug print
        if self.sam_handler.predictor is None:  # Check if MobileSAM is initialized
            QMessageBox.warning(self, "MobileSAM Not Initialized", "MobileSAM failed to initialize. Please check the setup.")
            self.sam_magic_wand_button.setChecked(False)
            return

        # Deactivate drawing mode if active
        if self.drawing_mode:
            self.toggle_draw_mode()

        # Deactivate erasing mode if active
        if self.erasing_mode:
            self.toggle_erase_mode()

        # Deactivate point prompt if active
        if hasattr(self, 'sam_magic_wand_point_button') and self.sam_magic_wand_point_button.isChecked():
            self.sam_magic_wand_point_button.setChecked(False)
            self.sam_magic_wand_point_button.setStyleSheet(clear_button_styles())
            self.deactivate_sam_point_prompt()

        # Deactivate negative point prompt if active
        if hasattr(self, 'sam_magic_wand_neg_point_button') and self.sam_magic_wand_neg_point_button.isChecked():
            self.sam_magic_wand_neg_point_button.setChecked(False)
            self.sam_magic_wand_neg_point_button.setStyleSheet(clear_button_styles())
            self.deactivate_sam_neg_point_prompt()

        if self.sam_magic_wand_button.isChecked():
            # Apply active button style with background highlight
            active_style = """
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    font-weight: bold;
                    border: 1px solid #1976D2;
                    border-radius: 4px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #42A5F5;
                }
            """
            self.sam_magic_wand_button.setStyleSheet(active_style)

            # Show tooltip with instructions
            QToolTip.showText(self.sam_magic_wand_button.mapToGlobal(self.sam_magic_wand_button.rect().center()),
                             "Magic Wand active. Click and drag to create a bounding box.")
            self.activate_sam_magic_wand()

            # Update status message
            self.update_status_message("Magic Wand Tool: Active - Draw a box around the object")
        else:
            # Reset button style - use theme-aware style
            self.sam_magic_wand_button.setStyleSheet(clear_button_styles())
            self.deactivate_sam_magic_wand()

            # Update status message
            self.update_status_message("Magic Wand Tool: Inactive")

    def toggle_sam_point_prompt(self):
        """Toggles the SAM positive point prompt tool state."""
        print("toggle_sam_point_prompt called")  # Debug print
        if self.sam_handler.predictor is None:  # Check if MobileSAM is initialized
            QMessageBox.warning(self, "MobileSAM Not Initialized", "MobileSAM failed to initialize. Please check the setup.")
            self.sam_magic_wand_point_button.setChecked(False)
            return

        # Deactivate drawing mode if active
        if self.drawing_mode:
            self.toggle_draw_mode()

        # Deactivate erasing mode if active
        if self.erasing_mode:
            self.toggle_erase_mode()

        # Deactivate box prompt if active
        if self.sam_magic_wand_button.isChecked():
            self.sam_magic_wand_button.setChecked(False)
            self.sam_magic_wand_button.setStyleSheet(clear_button_styles())
            self.deactivate_sam_magic_wand()

        if self.sam_magic_wand_point_button.isChecked():
            # Apply active button style with background highlight
            active_style = """
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    font-weight: bold;
                    border: 1px solid #2E7D32;
                    border-radius: 4px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #66BB6A;
                }
            """
            self.sam_magic_wand_point_button.setStyleSheet(active_style)

            # Show tooltip with instructions
            QToolTip.showText(self.sam_magic_wand_point_button.mapToGlobal(self.sam_magic_wand_point_button.rect().center()),
                             "Positive point prompt active. Click to add foreground points.")
            self.activate_sam_point_prompt()

            # Enable negative point button for use in conjunction
            if hasattr(self, 'sam_magic_wand_neg_point_button'):
                self.sam_magic_wand_neg_point_button.setEnabled(True)
                self.sam_magic_wand_neg_point_button.setToolTip("Click to toggle negative point mode. Use with positive points.")

            # Update status message
            self.update_status_message("Point Tool: Active - Click on foreground objects")
        else:
            # Reset button style
            self.sam_magic_wand_point_button.setStyleSheet(clear_button_styles())
            self.deactivate_sam_point_prompt()

            # Disable negative point button when positive is deactivated
            if hasattr(self, 'sam_magic_wand_neg_point_button'):
                self.sam_magic_wand_neg_point_button.setChecked(False)
                self.sam_magic_wand_neg_point_button.setEnabled(False)
                self.sam_magic_wand_neg_point_button.setStyleSheet(clear_button_styles())

            # Update status message
            self.update_status_message("Point Tool: Inactive")

    def toggle_sam_neg_point_prompt(self):
        """Toggles the SAM negative point prompt tool state.
        This works in conjunction with the positive point prompt.
        """
        print("toggle_sam_neg_point_prompt called")  # Debug print

        # Only allow negative point prompt if positive point prompt is active
        if not hasattr(self, 'sam_magic_wand_point_button') or not self.sam_magic_wand_point_button.isChecked():
            self.sam_magic_wand_neg_point_button.setChecked(False)
            QToolTip.showText(self.sam_magic_wand_neg_point_button.mapToGlobal(self.sam_magic_wand_neg_point_button.rect().center()),
                             "Activate positive point tool first.")
            return

        if self.sam_handler.predictor is None:  # Check if MobileSAM is initialized
            QMessageBox.warning(self, "MobileSAM Not Initialized", "MobileSAM failed to initialize. Please check the setup.")
            self.sam_magic_wand_neg_point_button.setChecked(False)
            return

        # Toggle negative point mode (works alongside positive point mode)
        if self.sam_magic_wand_neg_point_button.isChecked():
            # Apply active button style with background highlight
            active_style = """
                QPushButton {
                    background-color: #F44336;
                    color: white;
                    font-weight: bold;
                    border: 1px solid #D32F2F;
                    border-radius: 4px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #EF5350;
                }
            """
            self.sam_magic_wand_neg_point_button.setStyleSheet(active_style)

            # Show tooltip with instructions
            QToolTip.showText(self.sam_magic_wand_neg_point_button.mapToGlobal(self.sam_magic_wand_neg_point_button.rect().center()),
                             "Negative point mode active. Click to add background points.")
            self.activate_sam_neg_point_prompt()

            # Update status message
            self.update_status_message("Negative Point Tool: Active - Click on background areas")
        else:
            # Reset button style but keep the tool active - use theme-aware style
            self.sam_magic_wand_neg_point_button.setStyleSheet(clear_button_styles())
            # Switch back to positive point mode
            self.activate_sam_point_prompt()
            QToolTip.showText(self.sam_magic_wand_point_button.mapToGlobal(self.sam_magic_wand_point_button.rect().center()),
                             "Positive point mode active. Click to add foreground points.")

            # Update status message
            self.update_status_message("Point Tool: Active - Click on foreground objects")

    def activate_sam_magic_wand(self):
        """Activates the SAM magic wand tool."""
        print("activate_sam_magic_wand called")  # Debug print
        if self.trainable_original_view:
            self.trainable_original_view.current_tool = "sam_magic_wand"
            self.sam_magic_wand_active = True
            self.trainable_original_view.setCursor(Qt.CrossCursor)

            # Update accept/reject buttons to show they're available
            if hasattr(self, 'accept_sam_button'):
                self.accept_sam_button.setStyleSheet(clear_button_styles())
                self.accept_sam_button.setEnabled(True)
            if hasattr(self, 'reject_sam_button'):
                self.reject_sam_button.setStyleSheet(clear_button_styles())
                self.reject_sam_button.setEnabled(True)

    def deactivate_sam_magic_wand(self):
        """Deactivates the SAM magic wand tool."""
        print("deactivate_sam_magic_wand called")  # Debug print
        if self.trainable_original_view:
            self.trainable_original_view.current_tool = None
            self.sam_magic_wand_active = False
            self.trainable_original_view.setCursor(Qt.ArrowCursor)

            # Clear any SAM-related temporary data
            self.sam_bbox = None
            self.drawing_sam_bbox = False
            self.temp_sam_prediction = None

            # Reset accept/reject buttons
            if hasattr(self, 'accept_sam_button'):
                self.accept_sam_button.setStyleSheet(clear_button_styles())
                self.accept_sam_button.setEnabled(False)
            if hasattr(self, 'reject_sam_button'):
                self.reject_sam_button.setStyleSheet(clear_button_styles())
                self.reject_sam_button.setEnabled(False)

    def activate_sam_point_prompt(self):
        """Activates the SAM positive point prompt tool."""
        print("activate_sam_point_prompt called")  # Debug print
        if self.trainable_original_view:
            self.trainable_original_view.current_tool = "sam_point_prompt"
            self.sam_magic_wand_active = True  # Reuse the same flag for both tools
            self.trainable_original_view.setCursor(Qt.CrossCursor)

            # Initialize point prompts list if it doesn't exist
            if not hasattr(self, 'point_prompts'):
                self.point_prompts = []

            # Set the current point type to positive (1)
            self.current_point_type = 1

            # Update accept/reject buttons to show they're available
            if hasattr(self, 'accept_sam_button'):
                self.accept_sam_button.setStyleSheet(clear_button_styles())
                self.accept_sam_button.setEnabled(True)
            if hasattr(self, 'reject_sam_button'):
                self.reject_sam_button.setStyleSheet(clear_button_styles())
                self.reject_sam_button.setEnabled(True)

    def deactivate_sam_point_prompt(self):
        """Deactivates the SAM point prompt tool."""
        print("deactivate_sam_point_prompt called")  # Debug print
        if self.trainable_original_view:
            self.trainable_original_view.current_tool = None
            self.sam_magic_wand_active = False
            self.trainable_original_view.setCursor(Qt.ArrowCursor)

            # Clear any SAM-related temporary data
            self.sam_bbox = None
            self.drawing_sam_bbox = False
            self.temp_sam_prediction = None

            # Clear point prompts
            if hasattr(self, 'point_prompts'):
                self.point_prompts = []

            # Reset accept/reject buttons
            if hasattr(self, 'accept_sam_button'):
                self.accept_sam_button.setStyleSheet(clear_button_styles())
                self.accept_sam_button.setEnabled(False)
            if hasattr(self, 'reject_sam_button'):
                self.reject_sam_button.setStyleSheet(clear_button_styles())
                self.reject_sam_button.setEnabled(False)

    def activate_sam_neg_point_prompt(self):
        """Activates the SAM negative point prompt tool.
        Works in conjunction with the positive point prompt.
        """
        print("activate_sam_neg_point_prompt called")  # Debug print
        if self.trainable_original_view:
            # Keep using the same tool, just change the point type
            self.trainable_original_view.current_tool = "sam_point_prompt"
            self.sam_magic_wand_active = True  # Reuse the same flag for all SAM tools
            self.trainable_original_view.setCursor(Qt.CrossCursor)

            # Initialize point prompts list if it doesn't exist
            if not hasattr(self, 'point_prompts'):
                self.point_prompts = []

            # Set the current point type to negative (0)
            self.current_point_type = 0

            # Update accept/reject buttons to show they're available
            if hasattr(self, 'accept_sam_button'):
                self.accept_sam_button.setStyleSheet(clear_button_styles())
                self.accept_sam_button.setEnabled(True)
            if hasattr(self, 'reject_sam_button'):
                self.reject_sam_button.setStyleSheet(clear_button_styles())
                self.reject_sam_button.setEnabled(True)

    def deactivate_sam_neg_point_prompt(self):
        """Deactivates the SAM negative point prompt tool."""
        print("deactivate_sam_neg_point_prompt called")  # Debug print
        if self.trainable_original_view:
            self.trainable_original_view.current_tool = None
            self.sam_magic_wand_active = False
            self.trainable_original_view.setCursor(Qt.ArrowCursor)

            # Clear any SAM-related temporary data
            self.sam_bbox = None
            self.drawing_sam_bbox = False
            self.temp_sam_prediction = None

            # Clear point prompts
            if hasattr(self, 'point_prompts'):
                self.point_prompts = []

            # Reset accept/reject buttons
            if hasattr(self, 'accept_sam_button'):
                self.accept_sam_button.setStyleSheet(clear_button_styles())
                self.accept_sam_button.setEnabled(False)
            if hasattr(self, 'reject_sam_button'):
                self.reject_sam_button.setStyleSheet(clear_button_styles())
                self.reject_sam_button.setEnabled(False)

    def update_brush_size(self, value):
        """Updates the brush size based on slider value."""
        print(f"DEBUG: update_brush_size called with value {value} for TRAINABLE slider")
        # Store the value in the instance variable
        self.brush_size = value

        # Update the label if it exists
        if hasattr(self, 'trainable_brush_size_label'):
            self.trainable_brush_size_label.setText(str(value))
            print(f"DEBUG: Updated trainable_brush_size_label text to {value}")
        else:
            print(f"DEBUG: trainable_brush_size_label not found in update_brush_size")

        # Force update of the preview if we're in drawing mode
        if hasattr(self, 'drawing_mode') and self.drawing_mode and hasattr(self, 'preview_labels') and self.preview_labels is not None:
            self.display_preview()

        print(f"DEBUG: Brush size updated to {value}, self.brush_size = {self.brush_size}")

    def predict_sam_mask(self):
        """Predicts mask using MobileSAM with the current bounding box or point."""
        # Import cv2 at the beginning of the method to ensure it's available throughout
        import cv2

        logger.debug("predict_sam_mask called")
        if self.sam_bbox is None:
            logger.debug("No sam_bbox available")
            return

        # Ensure SAM handler is using the correct image
        if hasattr(self, 'sam_handler') and hasattr(self, 'trainable_image'):
            success = self.sam_handler.set_image(self.trainable_image)
            logger.debug(f"Ensuring SAM handler is using the correct image in predict_sam_mask: {success}")

            # If we couldn't set the image, don't proceed
            if not success:
                logger.error("Failed to set image in SAM handler")
                return

            # Log the current image path and index for debugging
            if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
                logger.debug(f"Current image path: {self.multi_image_handler.current_image_path}")
            if hasattr(self, 'current_image_index'):
                logger.debug(f"Current image index: {self.current_image_index}")

        # Check if we're using point prompt or box prompt
        if hasattr(self, 'sam_magic_wand_point_button') and self.sam_magic_wand_point_button.isChecked():
            # For point prompt, sam_bbox contains just the x,y coordinates
            logger.debug(f"Using point prompt at {self.sam_bbox}")
            mask, _ = self.sam_handler.predict_mask(self.sam_bbox, input_type='point')  # Ignore score
        else:
            # For box prompt, sam_bbox contains x1,y1,x2,y2
            logger.debug(f"Using box prompt at {self.sam_bbox}")
            mask, _ = self.sam_handler.predict_mask(self.sam_bbox, input_type='box')  # Ignore score

        if mask is not None:
            # Keep the mask as a mask, but store it as temp_sam_prediction
            self.temp_sam_prediction = mask

            # Check if we need to resize the mask to match the training labels
            if hasattr(self, 'training_labels') and self.training_labels is not None:
                if self.temp_sam_prediction.shape != self.training_labels.shape:
                    logger.debug(f"Resizing SAM prediction from {self.temp_sam_prediction.shape} to {self.training_labels.shape}")
                    # We'll resize when accepting the prediction, not here

            # Enable accept/reject buttons
            if hasattr(self, 'accept_sam_button'):
                self.accept_sam_button.setStyleSheet(clear_button_styles())
                self.accept_sam_button.setEnabled(True)
            if hasattr(self, 'reject_sam_button'):
                self.reject_sam_button.setStyleSheet(clear_button_styles())
                self.reject_sam_button.setEnabled(True)

            # Show tooltip with instructions
            if hasattr(self, 'accept_sam_button'):
                QToolTip.showText(self.accept_sam_button.mapToGlobal(self.accept_sam_button.rect().center()),
                                 "Accept this mask to add it to annotations.")

            self.display_trainable_image()

    def predict_sam_mask_with_points(self):
        """Predicts mask using MobileSAM with multiple point prompts."""
        # Import cv2 at the beginning of the method to ensure it's available throughout
        import cv2

        logger.debug("predict_sam_mask_with_points called")
        if not hasattr(self, 'point_prompts') or not self.point_prompts:
            logger.debug("No point prompts available")
            return

        # Ensure SAM handler is using the correct image
        if hasattr(self, 'sam_handler') and hasattr(self, 'trainable_image'):
            success = self.sam_handler.set_image(self.trainable_image)
            logger.debug(f"Ensuring SAM handler is using the correct image in predict_sam_mask_with_points: {success}")

            # If we couldn't set the image, don't proceed
            if not success:
                logger.error("Failed to set image in SAM handler")
                return

            # Log the current image path and index for debugging
            if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
                logger.debug(f"Current image path: {self.multi_image_handler.current_image_path}")
            if hasattr(self, 'current_image_index'):
                logger.debug(f"Current image index: {self.current_image_index}")

        logger.debug(f"Using {len(self.point_prompts)} point prompts: {self.point_prompts}")
        mask, _ = self.sam_handler.predict_mask(self.point_prompts, input_type='point')  # Ignore score

        if mask is not None:
            # Keep the mask as a mask, but store it as temp_sam_prediction
            self.temp_sam_prediction = mask

            # Check if we need to resize the mask to match the training labels
            if hasattr(self, 'training_labels') and self.training_labels is not None:
                if self.temp_sam_prediction.shape != self.training_labels.shape:
                    logger.debug(f"Resizing SAM prediction from {self.temp_sam_prediction.shape} to {self.training_labels.shape}")
                    # We'll resize when accepting the prediction, not here

            # Enable accept/reject buttons
            if hasattr(self, 'accept_sam_button'):
                self.accept_sam_button.setStyleSheet(clear_button_styles())
                self.accept_sam_button.setEnabled(True)
            if hasattr(self, 'reject_sam_button'):
                self.reject_sam_button.setStyleSheet(clear_button_styles())
                self.reject_sam_button.setEnabled(True)

            # Show tooltip with instructions
            if hasattr(self, 'accept_sam_button'):
                QToolTip.showText(self.accept_sam_button.mapToGlobal(self.accept_sam_button.rect().center()),
                                 "Accept this mask to add it to annotations.")

            self.display_trainable_image()


    def save_annotation_state(self):
        """Saves the current annotation state to history for undo functionality."""
        if not hasattr(self, 'training_labels') or self.training_labels is None:
            return

        if not hasattr(self, 'multi_image_handler') or not self.multi_image_handler.current_image_path:
            return

        current_path = self.multi_image_handler.current_image_path

        # Initialize history for this image if it doesn't exist
        if current_path not in self.annotation_history:
            self.annotation_history[current_path] = []

        # Create a deep copy of the current annotations
        if hasattr(self, 'training_labels') and self.training_labels is not None:
            history_entry = self.training_labels.copy()

            # Add to history stack
            self.annotation_history[current_path].append(history_entry)

            # Limit history size
            if len(self.annotation_history[current_path]) > self.max_history_size:
                self.annotation_history[current_path].pop(0)

            logger.debug(f"Saved annotation state for {current_path}. History size: {len(self.annotation_history[current_path])}")

            # Enable undo button
            if hasattr(self, 'undo_last_button'):
                self.undo_last_button.setEnabled(True)

    def undo_last_annotation(self):
        """Undoes the last annotation action."""
        logger.debug("undo_last_annotation called")

        if not hasattr(self, 'multi_image_handler') or not self.multi_image_handler.current_image_path:
            logger.debug("No current image path, cannot undo")
            return

        current_path = self.multi_image_handler.current_image_path

        # Check if we have history for this image
        if current_path not in self.annotation_history or not self.annotation_history[current_path]:
            logger.debug(f"No annotation history for {current_path}")
            if hasattr(self, 'undo_last_button'):
                self.undo_last_button.setEnabled(False)
            return

        # Get the previous state
        previous_state = self.annotation_history[current_path].pop()

        # Update the current annotations
        self.training_labels = previous_state.copy()

        # Save to multi_image_handler
        self.multi_image_handler.set_annotations(current_path, self.training_labels)

        # Update display
        self.display_trainable_image()

        # Update the class percentages widget
        if hasattr(self, 'class_percentages_widget'):
            self.calculate_class_percentages(self.training_labels)

        # Disable undo button if no more history
        if not self.annotation_history[current_path] and hasattr(self, 'undo_last_button'):
            self.undo_last_button.setEnabled(False)

        # Show success message
        if hasattr(self, 'undo_last_button'):
            QToolTip.showText(self.undo_last_button.mapToGlobal(self.undo_last_button.rect().center()),
                             "Last annotation action undone.")

        logger.debug(f"Undid last annotation for {current_path}. Remaining history: {len(self.annotation_history[current_path])}")

    def accept_sam_prediction(self):
        """Accepts the current SAM prediction and adds it to training labels."""
        logger.debug("accept_sam_prediction called")
        if self.temp_sam_prediction is not None and self.training_labels is not None:
            # Save the current state to history before making changes
            self.save_annotation_state()

            # Check if dimensions match
            if self.temp_sam_prediction.shape != self.training_labels.shape:
                logger.debug(f"Dimension mismatch - temp_sam_prediction: {self.temp_sam_prediction.shape}, training_labels: {self.training_labels.shape}")
                # Resize the mask to match training_labels dimensions
                import cv2
                resized_mask = cv2.resize(
                    self.temp_sam_prediction.astype(np.uint8),
                    (self.training_labels.shape[1], self.training_labels.shape[0]),
                    interpolation=cv2.INTER_NEAREST
                )
                # Convert back to boolean
                resized_mask = resized_mask > 0
                # Use the resized mask
                self.training_labels[resized_mask] = self.current_label
            else:
                # Use the mask directly to update training labels
                self.training_labels[self.temp_sam_prediction] = self.current_label

            self.multi_image_handler.set_annotations(self.multi_image_handler.current_image_path, self.training_labels)

            # Clear temporary data
            self.temp_sam_prediction = None
            self.sam_bbox = None

            # Clear point prompts
            if hasattr(self, 'point_prompts'):
                self.point_prompts = []

            # Show success message
            QToolTip.showText(self.accept_sam_button.mapToGlobal(self.accept_sam_button.rect().center()),
                             "Mask accepted and added to annotations.")

            # Reset accept/reject buttons
            if hasattr(self, 'accept_sam_button'):
                self.accept_sam_button.setStyleSheet(clear_button_styles())
                self.accept_sam_button.setEnabled(False)
            if hasattr(self, 'reject_sam_button'):
                self.reject_sam_button.setStyleSheet(clear_button_styles())
                self.reject_sam_button.setEnabled(False)

            # Enable undo button
            if hasattr(self, 'undo_last_button'):
                self.undo_last_button.setEnabled(True)

            # Update the display
            self.display_trainable_image()

            # Update the class percentages widget with the new annotations
            if hasattr(self, 'class_percentages_widget') and hasattr(self, 'training_labels'):
                self.calculate_class_percentages(self.training_labels)

    def reject_sam_prediction(self):
        """Rejects the current SAM prediction and clears all point prompts."""
        logger.debug("reject_sam_prediction called")
        self.temp_sam_prediction = None
        self.sam_bbox = None

        # Clear point prompts
        if hasattr(self, 'point_prompts'):
            self.point_prompts = []

        # Show message
        QToolTip.showText(self.reject_sam_button.mapToGlobal(self.reject_sam_button.rect().center()),
                         "Mask rejected. Try again with different points or box.")

        # Reset accept/reject buttons
        if hasattr(self, 'accept_sam_button'):
            self.accept_sam_button.setStyleSheet(clear_button_styles())
            self.accept_sam_button.setEnabled(False)
        if hasattr(self, 'reject_sam_button'):
            self.reject_sam_button.setStyleSheet(clear_button_styles())
            self.reject_sam_button.setEnabled(False)

        self.display_trainable_image()

    def on_gallery_images_selected(self, images, filenames):
        """Handles image selection from gallery."""
        if not images or not filenames:
            return

        # Store the current image path before switching
        current_path = None
        if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
            current_path = self.multi_image_handler.current_image_path
            print(f"DEBUG: Saving state for current image in gallery selection: {current_path}")

        # Save current annotations and segmentation results if they exist
        if hasattr(self, 'trainable_image') and current_path:
            # Save annotations
            if hasattr(self, 'training_labels') and self.training_labels is not None:
                print(f"DEBUG: Saving annotations for {current_path} before gallery selection")
                self.multi_image_handler.set_annotations(current_path, self.training_labels.copy())

            # Save segmentation results
            if hasattr(self, 'result') and self.result is not None:
                print(f"DEBUG: Saving segmentation result for {current_path} before gallery selection")
                self.multi_image_handler.set_segmentation_result(current_path, self.result.copy())

        # Update current image (using first selected image)
        self.trainable_image = images[0]
        file_path = filenames[0]

        # Clear current annotations and results before loading new ones
        self.training_labels = None
        self.result = None

        # Update current image path after saving previous state
        self.multi_image_handler.current_image_path = file_path
        print(f"DEBUG: Switched to image from gallery: {file_path}, index: {self.trainable_gallery.file_paths.index(file_path) if hasattr(self, 'trainable_gallery') else 'unknown'}")

        # Try to get annotations from multi_image_handler first
        annotations = self.multi_image_handler.get_annotations(file_path)
        if annotations is not None:
            print(f"DEBUG: Retrieved annotations for {file_path} from multi_image_handler with shape {annotations.shape}")
            # Make a copy to ensure we're not modifying the original
            self.training_labels = annotations.copy()

            # Ensure the annotations are a numpy array of type uint8
            if not isinstance(self.training_labels, np.ndarray):
                print(f"DEBUG: Converting training_labels to numpy array")
                self.training_labels = np.array(self.training_labels, dtype=np.uint8)
            elif self.training_labels.dtype != np.uint8:
                print(f"DEBUG: Converting training_labels dtype from {self.training_labels.dtype} to uint8")
                self.training_labels = self.training_labels.astype(np.uint8)

            # Verify dimensions match the image
            if self.training_labels.shape != self.trainable_image.shape[:2]:
                print(f"DEBUG: training_labels shape {self.training_labels.shape} doesn't match image shape {self.trainable_image.shape[:2]}")
                try:
                    # Try to resize training_labels to match image dimensions
                    import cv2
                    resized_labels = cv2.resize(
                        self.training_labels,
                        (self.trainable_image.shape[1], self.trainable_image.shape[0]),
                        interpolation=cv2.INTER_NEAREST
                    )
                    self.training_labels = resized_labels
                    print(f"DEBUG: Resized training_labels to {self.training_labels.shape}")
                    # Save the resized annotations back to the handler
                    self.multi_image_handler.set_annotations(file_path, self.training_labels.copy())
                except Exception as e:
                    print(f"DEBUG: Error resizing training_labels: {e}")
                    # Create new empty labels with correct dimensions
                    self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                    # Save these empty annotations to the handler
                    self.multi_image_handler.set_annotations(file_path, self.training_labels.copy())
        else:
            print(f"DEBUG: No annotations found for {file_path} in multi_image_handler, initializing empty labels")
            # Now it's safe to create empty annotations
            self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
            # Save these empty annotations to the handler
            self.multi_image_handler.set_annotations(file_path, self.training_labels.copy())

        # Initialize other attributes as new
        self.features = None
        self.result = None

        # Always try to get the classifier from multi_image_handler
        self.classifier, self.feature_params, label_mapping, mask_colors = self.multi_image_handler.get_classifier()
        if label_mapping is not None:
            self.label_mapping = label_mapping

        # Get mask colors from multi-image handler
        if mask_colors is not None and hasattr(self, 'mask_colors'):
            # Only update if the shape is compatible
            if isinstance(mask_colors, np.ndarray) and mask_colors.shape[0] <= self.mask_colors.shape[0]:
                self.mask_colors[:mask_colors.shape[0]] = mask_colors
            elif isinstance(mask_colors, list) and len(mask_colors) <= self.mask_colors.shape[0]:
                self.mask_colors[:len(mask_colors)] = np.array(mask_colors)

        # Get label names from multi-image handler if available
        if hasattr(self.multi_image_handler, 'label_names') and self.multi_image_handler.label_names:
            print(f"DEBUG: Retrieved label_names from multi_image_handler in on_gallery_images_selected: {self.multi_image_handler.label_names}")
            self.label_names = self.multi_image_handler.label_names.copy()
            # Update the label selection combo box
            self.update_label_color_indicator()

        print(f"DEBUG: Retrieved classifier from multi_image_handler in on_gallery_images_selected: {self.classifier is not None}")

        # Try to get segmentation result from multi_image_handler
        self.result = self.multi_image_handler.get_segmentation_result(file_path)
        if self.result is not None:
            print(f"DEBUG: Retrieved segmentation result for {file_path} from multi_image_handler with shape {self.result.shape}")
            print(f"DEBUG: Unique values in result: {np.unique(self.result)}")

            # Make a copy to ensure we're not modifying the original
            self.result = self.result.copy()

            # Ensure the result is saved back to the multi_image_handler
            self.multi_image_handler.set_segmentation_result(file_path, self.result)
            print(f"DEBUG: Ensured segmentation result is saved for {file_path}")
        else:
            print(f"DEBUG: No segmentation result found for {file_path} in on_gallery_images_selected")
            self.result = None

        # Set the image for MobileSAM
        if hasattr(self, 'sam_handler'):
            if self.sam_handler.set_image(self.trainable_image) is False:
                QMessageBox.warning(self, "MobileSAM Error", "Failed to set image in MobileSAM predictor.")

        # Display the image
        self.display_trainable_image()

        # Display segmentation result if available
        if self.result is not None:
            print(f"DEBUG: Displaying segmentation result in on_gallery_images_selected")
            self.display_segmentation_result()
            # Switch to the segmentation result tab if we have a result
            if hasattr(self, 'trainable_tab_widget'):
                self.trainable_tab_widget.setCurrentIndex(1)
        else:
            # Clear result view if no segmentation results
            if hasattr(self, 'trainable_result_view'):
                self.trainable_result_view.clear()
                # Switch to the annotation tab
                if hasattr(self, 'trainable_tab_widget'):
                    self.trainable_tab_widget.setCurrentIndex(0)

        # Update image info
        h, w, c = self.trainable_image.shape
        self.trainable_image_info_label.setText(f"Image: {os.path.basename(file_path)}\nSize: {w}x{h}\nChannels: {c}")

        # Reset drawing state
        self.drawing_mode = False
        self.erasing_mode = False
        self.draw_button.setText("Draw Label")
        self.erase_button.setText("Erase Label")
        if hasattr(self, 'trainable_original_view'):
            self.trainable_original_view.drawing_enabled = False
            self.trainable_original_view.erasing_enabled = False

    def _overlay_mask(self, display_img, mask, colors):
        """Helper function to overlay a mask on an image."""
        # Import cv2 at the beginning to ensure it's available throughout the method
        import cv2

        logger.debug(f"_overlay_mask called with mask shape: {mask.shape}, unique values: {np.unique(mask)}")
        logger.debug(f"colors shape: {colors.shape if isinstance(colors, np.ndarray) else 'Not an array'}")

        # Check if mask dimensions match display_img dimensions
        if mask.shape != display_img.shape[:2]:
            logger.debug(f"Mask shape {mask.shape} doesn't match image shape {display_img.shape[:2]}")
            # Resize mask to match display_img dimensions
            try:
                # Import cv2 here to ensure it's available
                import cv2
                mask = cv2.resize(
                    mask.astype(np.uint8),
                    (display_img.shape[1], display_img.shape[0]),
                    interpolation=cv2.INTER_NEAREST
                )
                logger.debug(f"Resized mask to {mask.shape}")
            except Exception as e:
                logger.debug(f"Error resizing mask: {e}")
                # Return the original image if we can't resize
                return display_img

        # Create a colored mask image with the same dimensions as the display image
        mask_rgb = np.zeros_like(display_img)

        # Handle different color array formats
        if isinstance(colors, list):
            colors = np.array(colors)

        # Make sure we have enough colors for all labels
        max_label = np.max(mask)
        if max_label >= len(colors):
            logger.debug(f"Not enough colors for all labels. Max label: {max_label}, Colors: {len(colors)}")
            # Add more colors if needed
            import random
            # Convert colors to numpy array if it's not already
            if not isinstance(colors, np.ndarray):
                try:
                    colors = np.array(colors, dtype=np.int32)
                except Exception as e:
                    logger.debug(f"Error converting colors to numpy array: {e}")
                    # Create a new array with default colors
                    colors = np.array([[255, 0, 0], [0, 255, 0], [0, 0, 255]], dtype=np.int32)

            # Add more colors
            while len(colors) <= max_label:
                new_color = np.array([[random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)]], dtype=np.int32)
                colors = np.vstack([colors, new_color])

            # Update the mask_colors attribute to include the new colors
            if hasattr(self, 'mask_colors') and isinstance(self.mask_colors, np.ndarray):
                # Only update if the new colors array is larger
                if len(colors) > len(self.mask_colors):
                    print(f"DEBUG: Updating mask_colors from {len(self.mask_colors)} to {len(colors)} colors")
                    self.mask_colors = colors.copy()

        # Apply colors to each label
        for i in range(1, np.max(mask) + 1):  # Iterate through all labels
            label_mask = mask == i
            if np.any(label_mask):
                try:
                    # Get the color for this label
                    color = colors[i-1]  # Adjust index for color array

                    # Convert color values to integers if they're not already
                    if isinstance(color, (list, np.ndarray)):
                        try:
                            r = int(color[0])
                            g = int(color[1])
                            b = int(color[2])
                            mask_rgb[label_mask] = [r, g, b]
                        except (ValueError, TypeError) as e:
                            print(f"DEBUG: Error converting color values to integers: {e}")
                            # Use a default color if conversion fails
                            mask_rgb[label_mask] = [255, 0, 0]  # Red
                    else:
                        # If color is not a list or array, use a default color
                        print(f"DEBUG: Unexpected color format: {type(color)}")
                        mask_rgb[label_mask] = [255, 0, 0]  # Red
                except IndexError:
                    print(f"DEBUG: IndexError for label {i}, colors shape: {colors.shape if isinstance(colors, np.ndarray) else 'not an array'}")
                    # Use a default color if index is out of range
                    mask_rgb[label_mask] = [255, 0, 0]  # Red

        # For saving segmentation results, use a stronger alpha value to make colors more visible
        alpha = 0.7 if self.mask_overlay_alpha < 0.7 else self.mask_overlay_alpha

        # Create a fully colored version of the image (no transparency)
        colored_img = display_img.copy()
        mask_indices = mask > 0
        if np.any(mask_indices):
            # Apply semi-transparent overlay for display
            overlay = cv2.addWeighted(
                display_img[mask_indices].astype(np.float32),
                1 - alpha,
                mask_rgb[mask_indices].astype(np.float32),
                alpha,
                0
            ).astype(np.uint8)
            colored_img[mask_indices] = overlay

            # Apply the overlay to the display image
            display_img[mask_indices] = overlay

        return colored_img

    def display_trainable_image(self):
        """Displays the image with mask overlay in the trainable segmentation page."""
        # Import cv2 at the beginning of the method to ensure it's available throughout
        import cv2

        logger.debug("display_trainable_image called")
        if not hasattr(self, 'trainable_image') or self.trainable_image is None:
            logger.debug("No trainable_image available")
            return
        logger.debug(f"trainable_image shape: {self.trainable_image.shape}")

        # Ensure we have the latest annotations from multi_image_handler
        if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
            current_path = self.multi_image_handler.current_image_path
            annotations = self.multi_image_handler.get_annotations(current_path)
            if annotations is not None:
                logger.debug(f"Retrieved annotations for {current_path} from multi_image_handler in display_trainable_image")
                self.training_labels = annotations.copy()
            elif not hasattr(self, 'training_labels') or self.training_labels is None:
                logger.debug(f"No annotations found for {current_path}, initializing empty labels")
                self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                self.multi_image_handler.set_annotations(current_path, self.training_labels.copy())

            # Update image info display
            if hasattr(self, 'trainable_image_info_label'):
                h, w, c = self.trainable_image.shape
                # Use 1-based indexing for display
                display_index = self.current_image_index + 1 if hasattr(self, 'current_image_index') else 1
                total_images = len(self.multi_image_handler.get_all_images()) if hasattr(self.multi_image_handler, 'get_all_images') else 1

                # Get the filename from the path
                filename = os.path.basename(current_path)

                info_text = f"Image: {filename} ({display_index} of {total_images})\nSize: {w}x{h}\nChannels: {c}"
                self.trainable_image_info_label.setText(info_text)
                logger.debug(f"Updated image info in display_trainable_image: {info_text.replace(chr(10), ' | ')}")

            # Ensure SAM handler is using the correct image
            if hasattr(self, 'sam_handler'):
                success = self.sam_handler.set_image(self.trainable_image)
                logger.debug(f"Ensuring SAM handler is using the correct image in display_trainable_image: {success}")

        # Create a copy of the image for overlay
        display_img = self.trainable_image.copy()

        # Update class percentages for training labels
        if hasattr(self, 'training_labels') and self.training_labels is not None and hasattr(self, 'class_percentages_widget'):
            self.calculate_class_percentages(self.training_labels)

        # Draw SAM bounding box or points if active
        if self.sam_magic_wand_active:
            # Draw multiple point prompts if they exist
            if hasattr(self, 'point_prompts') and self.point_prompts:
                for point in self.point_prompts:
                    x, y, label = point
                    if label == 1:  # Foreground point (positive)
                        color = (46, 125, 50)  # Dark green (matches POSITIVE_POINT_BUTTON_STYLE)
                        text = "+"  # Plus sign for positive points
                    else:  # Background point (negative)
                        color = (198, 40, 40)  # Dark red (matches NEGATIVE_POINT_BUTTON_STYLE)
                        text = "-"  # Minus sign for negative points

                    # Draw a filled circle
                    cv2.circle(display_img, (int(x), int(y)), 5, color, -1)  # Filled circle

                    # Draw a larger circle around the point for better visibility
                    cv2.circle(display_img, (int(x), int(y)), 8, color, 2)  # Outlined circle

                    # Add a plus or minus sign
                    font = cv2.FONT_HERSHEY_SIMPLEX
                    text_size = cv2.getTextSize(text, font, 0.7, 2)[0]
                    text_x = int(x - text_size[0] / 2)
                    text_y = int(y + text_size[1] / 2)
                    cv2.putText(display_img, text, (text_x, text_y), font, 0.7, (255, 255, 255), 2)
            # Draw bounding box if it exists and we're using box prompt
            elif self.sam_bbox is not None:
                if hasattr(self, 'sam_magic_wand_point_button') and self.sam_magic_wand_point_button.isChecked():
                    # For single point prompt (backward compatibility), draw a circle
                    x, y = self.sam_bbox
                    cv2.circle(display_img, (int(x), int(y)), 5, (0, 255, 0), -1)  # Filled circle
                else:
                    # For box prompt, draw a rectangle
                    x1, y1, x2, y2 = self.sam_bbox
                    cv2.rectangle(display_img, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

        # Draw SAM prediction if available.  Draw as *mask overlay*, not polygon.
        if self.temp_sam_prediction is not None:
            # Check if dimensions match
            if self.temp_sam_prediction.shape != display_img.shape[:2]:
                print(f"DEBUG: Dimension mismatch for display - temp_sam_prediction: {self.temp_sam_prediction.shape}, display_img: {display_img.shape[:2]}")
                # Resize the mask to match display_img dimensions
                import cv2
                resized_mask = cv2.resize(
                    self.temp_sam_prediction.astype(np.uint8),
                    (display_img.shape[1], display_img.shape[0]),
                    interpolation=cv2.INTER_NEAREST
                )
                display_img = self._overlay_mask(display_img, resized_mask, [[0, 255, 0]])  # Green overlay
            else:
                display_img = self._overlay_mask(display_img, self.temp_sam_prediction.astype(np.uint8), [[0, 255, 0]])  # Green overlay


        # Overlay the training labels if they exist
        logger.debug(f"training_labels: {self.training_labels if hasattr(self, 'training_labels') else 'Not defined'}")
        if hasattr(self, 'training_labels') and self.training_labels is not None:
            logger.debug(f"training_labels shape: {self.training_labels.shape}")
            logger.debug(f"training_labels unique values: {np.unique(self.training_labels)}")
            logger.debug(f"mask_colors shape: {self.mask_colors.shape}")
            display_img = self._overlay_mask(display_img, self.training_labels, self.mask_colors)

        # Convert to QImage and display
        q_img = convert_cvimage_to_qimage(display_img, already_rgb=True)
        pixmap = QPixmap.fromImage(q_img)
        self.trainable_original_view.setPixmap(pixmap)

    def calculate_class_percentages(self, segmentation_result):
        """Calculates the percentage of each class in the segmentation result."""
        if segmentation_result is None:
            return {}

        total_pixels = segmentation_result.size
        unique_labels = np.unique(segmentation_result)
        percentages = {}
        label_to_color = {}

        # Make sure we have enough label names for all labels
        max_label = np.max(segmentation_result)

        # Check if label_names is a dictionary
        if isinstance(self.label_names, dict):
            # Get the highest label index in the dictionary
            highest_label = max(self.label_names.keys()) if self.label_names else 0

            # Add more label names if needed
            if max_label > highest_label:
                print(f"DEBUG: Not enough label names for all labels. Max label: {max_label}, Highest existing label: {highest_label}")
                for i in range(highest_label + 1, max_label + 1):
                    self.label_names[i] = f"Segment {i}"
                    print(f"DEBUG: Added new label name: Segment {i}")

                # Save the updated label names to the label settings manager
                if hasattr(self, 'label_settings_manager'):
                    self.label_settings_manager.update_label_names(self.label_names)
        else:
            # Legacy code for list-based label names (should not be reached anymore)
            if max_label >= len(self.label_names):
                print(f"DEBUG: Not enough label names for all labels. Max label: {max_label}, Names: {len(self.label_names)}")
                # Convert to dictionary first
                label_names_dict = {}
                for i, name in enumerate(self.label_names):
                    label_names_dict[i+1] = name  # 1-indexed labels
                self.label_names = label_names_dict

                # Add more label names if needed
                for i in range(len(self.label_names) + 1, max_label + 1):
                    self.label_names[i] = f"Segment {i}"
                    print(f"DEBUG: Added new label name: Segment {i}")

                # Save the updated label names to the label settings manager
                if hasattr(self, 'label_settings_manager'):
                    self.label_settings_manager.update_label_names(self.label_names)

        for label in unique_labels:
            if label == 0:  # Skip background
                continue
            mask = segmentation_result == label
            percentage = (np.sum(mask) / total_pixels) * 100

            # Get the label name, with fallback to "Segment X" if not found
            if isinstance(self.label_names, dict):
                # Dictionary-based label names
                if label in self.label_names:
                    label_name = self.label_names[label]
                    # Ensure consistent naming - if it's a default name, use "Segment X" format
                    if label_name.startswith("Label "):
                        label_name = f"Segment {label}"
                else:
                    label_name = f"Segment {label}"
            else:
                # Legacy code for list-based label names (should not be reached anymore)
                if label in self.label_names:
                    label_name = self.label_names[label]
                    # Ensure consistent naming - if it's a default name, use "Segment X" format
                    if label_name.startswith("Label "):
                        label_name = f"Segment {label}"
                else:
                    label_name = f"Segment {label}"

            percentages[label_name] = percentage

            # Create a mapping from label name to color
            if 0 <= label - 1 < len(self.mask_colors):
                label_to_color[label_name] = self.mask_colors[label - 1]
            else:
                # If we don't have a color for this label, use a default color
                import random
                label_to_color[label_name] = [random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)]

        # When updating the class percentages widget, pass the label_to_color mapping
        if hasattr(self, 'class_percentages_widget'):
            self.class_percentages_widget.update_percentages(percentages, label_to_color)

        return percentages

    def display_segmentation_result(self):
        """Displays the segmentation result."""
        # Import cv2 at the beginning to ensure it's available throughout the method
        import cv2

        print(f"DEBUG: display_segmentation_result called, current_image_index: {self.current_image_index if hasattr(self, 'current_image_index') else 'None'}")

        if not hasattr(self, 'result') or self.result is None:
            print("DEBUG: No segmentation result to display")
            # Clear the result view
            if hasattr(self, 'trainable_result_view'):
                self.trainable_result_view.clear()
            return

        print(f"DEBUG: Displaying segmentation result with shape: {self.result.shape}")
        print(f"DEBUG: Unique values in result: {np.unique(self.result)}")

        # Log the current image path
        if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
            print(f"DEBUG: Current image path: {self.multi_image_handler.current_image_path}")

            # Double-check if the result in multi_image_handler matches our current result
            stored_result = self.multi_image_handler.get_segmentation_result(self.multi_image_handler.current_image_path)
            if stored_result is not None:
                print(f"DEBUG: Stored result in multi_image_handler has shape: {stored_result.shape}")
                print(f"DEBUG: Unique values in stored result: {np.unique(stored_result)}")

                # If our current result is None but there's a stored result, use that
                if self.result is None:
                    print("DEBUG: Using stored result from multi_image_handler")
                    self.result = stored_result.copy()

        # Create a visualization of the segmentation result
        if hasattr(self, 'trainable_image') and self.trainable_image is not None:
            # Check if result dimensions match the image dimensions
            if self.result.shape[:2] != self.trainable_image.shape[:2]:
                print(f"DEBUG: Result dimensions {self.result.shape[:2]} don't match image dimensions {self.trainable_image.shape[:2]}. Resizing...")
                try:
                    # Resize result to match image dimensions
                    import cv2
                    resized_result = cv2.resize(
                        self.result,
                        (self.trainable_image.shape[1], self.trainable_image.shape[0]),
                        interpolation=cv2.INTER_NEAREST
                    )
                    self.result = resized_result
                    print(f"DEBUG: Resized result to {resized_result.shape}")

                    # Save the resized result to the multi_image_handler
                    if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
                        self.multi_image_handler.set_segmentation_result(self.multi_image_handler.current_image_path, self.result)
                        print(f"DEBUG: Saved resized result to multi_image_handler for {self.multi_image_handler.current_image_path}")
                except Exception as e:
                    print(f"DEBUG: Error resizing result: {e}")
                    # Create empty result matching the image size
                    self.result = None
                    print("DEBUG: Could not resize result, setting to None")
                    return

            # Create a colored mask for the segmentation result
            display_img = self.trainable_image.copy()
            try:
                display_img = self._overlay_mask(display_img, self.result, self.mask_colors)
                print(f"DEBUG: Created overlay for segmentation result")

                q_img = convert_cvimage_to_qimage(display_img, already_rgb=True)
                pixmap = QPixmap.fromImage(q_img)
                self.trainable_result_view.setPixmap(pixmap)
                print(f"DEBUG: Set pixmap for segmentation result")

                # Ensure the result is saved to the multi_image_handler
                if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
                    self.multi_image_handler.set_segmentation_result(self.multi_image_handler.current_image_path, self.result)
                    print(f"DEBUG: Ensured result is saved to multi_image_handler for {self.multi_image_handler.current_image_path}")
            except Exception as e:
                print(f"DEBUG: Error displaying segmentation result: {e}")
                import traceback
                traceback.print_exc()
                self.result = None

            # Update the class percentages widget if it exists
            if hasattr(self, 'class_percentages_widget') and hasattr(self, 'result') and self.result is not None:
                self.calculate_class_percentages(self.result)
                print(f"DEBUG: Updated class percentages for segmentation result")

    def display_feature_importance(self):
        """Displays the feature importance plot."""
        if not hasattr(self, 'classifier') or self.classifier is None:
            return

        # Create feature importance plot
        fig, ax = plt.subplots(1, 2, figsize=(9, 4))

        # Get feature importance from classifier
        l = len(self.classifier.feature_importances_)
        feature_importance = (
            self.classifier.feature_importances_[: l // 3],
            self.classifier.feature_importances_[l // 3: 2 * l // 3],
            self.classifier.feature_importances_[2 * l // 3:],
        )

        # Get sigma values
        sigma_min = self.sigma_min_spinbox.value()
        sigma_max = self.sigma_max_spinbox.value()
        # CRITICAL FIX: Calculate the number of sigmas based on *actual* feature count
        num_sigmas = self.features.shape[-1] // (3 * 3)  # Features per channel, per feature type (intensity, edge1, edge2)

        sigmas = np.logspace(
            np.log2(sigma_min),
            np.log2(sigma_max),
            num=num_sigmas, # Use the calculated number of sigmas
            base=2,
            endpoint=True,
        )


        # Plot intensity features
        for ch, color in enumerate(['r', 'g', 'b']):
            ax[0].plot(sigmas, feature_importance[ch][:num_sigmas], 'o', color=color) # Slice correctly
            ax[0].set_title("Intensity features")
            ax[0].set_xlabel("$\\sigma$")

        # Plot texture features
        for ch, color in enumerate(['r', 'g', 'b']):
            ax[1].plot(sigmas, feature_importance[ch][num_sigmas:2*num_sigmas], 'o', color=color)  #Slice correctly
            ax[1].plot(sigmas, feature_importance[ch][2*num_sigmas:3*num_sigmas], 's', color=color) #Slice correctly
            ax[1].set_title("Texture features")
            ax[1].set_xlabel("$\\sigma$")

        fig.tight_layout()

        # Save the figure to a temporary file and display it
        temp_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                 'temp_feature_importance.png')
        fig.savefig(temp_file)
        plt.close(fig)  # Close the figure to free memory

        # Load and display the image
        pixmap = QPixmap(temp_file)
        self.feature_importance_view.setPixmap(pixmap)

        # Clean up the temporary file
        try:
            os.remove(temp_file)
        except OSError:  # More specific exception handling
            pass
    def set_current_label(self, index):
        """Sets the current label for drawing."""
        self.current_label = index + 1  # Labels are 1-indexed

    def update_label_selection_combo(self):
        """Updates the label selection combo box with the current label colors and names."""
        if not hasattr(self, 'label_selection_combo'):
            print("DEBUG: label_selection_combo not found, cannot update")
            return

        print("DEBUG: Updating label selection combo box")
        self.update_label_color_indicator()

    def update_label_color_indicator(self):
        """Updates the color indicator in the label selection combo box."""
        if not hasattr(self, 'label_selection_combo'):
            return

        # Clear the combo box
        self.label_selection_combo.clear()

        # Check if label_names is a list or dictionary
        if isinstance(self.label_names, list):
            # Convert list to dictionary if needed
            print(f"DEBUG: Converting label_names from list to dictionary")
            label_names_dict = {}
            for i, name in enumerate(self.label_names):
                label_names_dict[i+1] = name  # 1-indexed labels
            self.label_names = label_names_dict

            # Also update the label settings manager
            if hasattr(self, 'label_settings_manager'):
                self.label_settings_manager.update_label_names(self.label_names)

        # Get sorted label indices (1-indexed)
        sorted_indices = sorted(self.label_names.keys())

        # Add items to combo box in order with colored icons
        for idx in sorted_indices:
            name = self.label_names[idx]

            # Get the color for this label
            color_idx = idx - 1  # Convert to 0-indexed for mask_colors
            if hasattr(self, 'mask_colors') and self.mask_colors is not None and color_idx < len(self.mask_colors):
                color = self.mask_colors[color_idx]

                # Create a colored square icon
                pixmap = QPixmap(16, 16)
                if isinstance(color, (list, tuple, np.ndarray)):
                    # Convert color values to integers if needed
                    try:
                        r = int(color[0])
                        g = int(color[1])
                        b = int(color[2])
                        pixmap.fill(QColor(r, g, b))
                    except (ValueError, TypeError, IndexError) as e:
                        print(f"DEBUG: Error creating color icon: {e}")
                        pixmap.fill(QColor(255, 0, 0))  # Default to red on error
                else:
                    print(f"DEBUG: Invalid color format: {color}")
                    pixmap.fill(QColor(255, 0, 0))  # Default to red

                # Add the item with the colored icon
                self.label_selection_combo.addItem(QIcon(pixmap), name)
                print(f"DEBUG: Added label {idx}: {name} with color {color}")
            else:
                # Add the item without an icon if no color is available
                self.label_selection_combo.addItem(name)
                print(f"DEBUG: Added label {idx}: {name} without color")

        # Set the current index to the current label if possible
        if hasattr(self, 'current_label'):
            # Find the index of the current label in the sorted indices
            for i, idx in enumerate(sorted_indices):
                if idx == self.current_label:
                    self.label_selection_combo.setCurrentIndex(i)
                    print(f"DEBUG: Set current label to index {i} (label {self.current_label})")
                    break

        print(f"DEBUG: Updated label selection combo box with {len(sorted_indices)} labels")

    def add_new_label(self):
        """Adds a new label using the label management dialog."""
        # Show the label management dialog
        self.show_label_management_dialog()

    def rename_label(self):
        """Renames the currently selected label using the label management dialog."""
        # Show the label management dialog
        self.show_label_management_dialog()

    def show_label_management_dialog(self):
        """Shows the label management dialog."""
        try:
            # Check if we already have a dialog reference and it's visible
            if hasattr(self, '_current_dialog') and self._current_dialog is not None:
                if self._current_dialog.isVisible():
                    print("DEBUG: Dialog already open, bringing to front")
                    self._current_dialog.raise_()
                    self._current_dialog.activateWindow()
                    return
                else:
                    # Dialog exists but is not visible, clean it up
                    self._current_dialog = None

            # Also check for any other dialog children
            for child in self.children():
                if isinstance(child, QDialog) and child.isVisible():
                    print("DEBUG: Another dialog is open, bringing to front")
                    child.raise_()
                    child.activateWindow()
                    return

            from src.gui.label_management_dialog import LabelManagementDialog

            # Convert mask_colors to a format suitable for the dialog
            if isinstance(self.mask_colors, np.ndarray):
                mask_colors_dict = {}
                for i, color in enumerate(self.mask_colors):
                    if isinstance(color, np.ndarray):
                        color = color.tolist()
                    mask_colors_dict[i+1] = color
            else:
                mask_colors_dict = self.mask_colors

            # Create the dialog
            dialog = LabelManagementDialog(self, self.label_names, mask_colors_dict)

            # Store the dialog as an instance variable to prevent garbage collection
            self._current_dialog = dialog

            # Connect the signal to update label data - use a direct connection to prevent duplicate signals
            dialog.label_data_changed.connect(self.update_label_data, Qt.DirectConnection)

            # Connect the signal to handle label removal
            dialog.label_removed.connect(self.handle_label_removed, Qt.DirectConnection)

            # Show the dialog
            print("DEBUG: Showing label management dialog")
            result = dialog.exec()

            if result:
                print("DEBUG: Label management dialog accepted")
                # The label data has already been updated via the signal
            else:
                print("DEBUG: Label management dialog cancelled")

            # Clean up the dialog reference
            self._current_dialog = None

        except ImportError:
            print("ERROR: Failed to import LabelManagementDialog")
            QMessageBox.critical(self, "Error", "Could not load the Label Management module.")
        except Exception as e:
            print(f"ERROR: Error showing Label Management dialog: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"An error occurred: {e}")

    def handle_label_removed(self, label_id):
        """Handle the removal of a label by removing all associated annotations.

        Args:
            label_id (int): The ID of the label being removed.
        """
        logger.info(f"Handling removal of label {label_id}")

        if not hasattr(self, 'multi_image_handler'):
            logger.warning("No multi_image_handler found, cannot remove annotations")
            return

        # Get all annotations from all images
        all_annotations = self.multi_image_handler.get_all_annotations()
        if not all_annotations:
            logger.warning("No annotations found in multi_image_handler")
            return

        # Track how many images were affected
        affected_images = 0

        # Iterate through all images and remove annotations with the specified label
        for image_path, annotations in all_annotations.items():
            if annotations is None:
                continue

            # Check if this image has any annotations with the specified label
            if np.any(annotations == label_id):
                logger.debug(f"Removing label {label_id} from annotations in {image_path}")

                # Create a copy to avoid modifying the original
                updated_annotations = annotations.copy()

                # Remove the label by setting all pixels with this label to 0 (background)
                updated_annotations[updated_annotations == label_id] = 0

                # Update the annotations in the multi_image_handler
                self.multi_image_handler.set_annotations(image_path, updated_annotations)

                affected_images += 1

        # If the current image has annotations with this label, update the display
        if hasattr(self, 'training_labels') and self.training_labels is not None:
            if np.any(self.training_labels == label_id):
                logger.debug(f"Removing label {label_id} from current image annotations")

                # Remove the label from the current image
                self.training_labels[self.training_labels == label_id] = 0

                # Update the display
                self.display_trainable_image()

                # Update the class percentages widget
                if hasattr(self, 'class_percentages_widget'):
                    self.calculate_class_percentages(self.training_labels)

        # Show a message about the number of affected images
        if affected_images > 0:
            QMessageBox.information(
                self,
                "Annotations Removed",
                f"Removed annotations with label {label_id} from {affected_images} image(s)."
            )
        else:
            logger.debug(f"No images had annotations with label {label_id}")

    def update_label_data(self, data):
        """Updates the label data from the label management dialog."""
        try:
            # Update label names
            if 'label_names' in data:
                self.label_names = data['label_names']
                print(f"DEBUG: Updated label names: {self.label_names}")

                # Update the label settings manager
                if hasattr(self, 'label_settings_manager'):
                    self.label_settings_manager.update_label_names(self.label_names)

            # Update mask colors
            if 'mask_colors' in data:
                # Convert to numpy array if needed
                if isinstance(data['mask_colors'], dict):
                    # Find the maximum label index
                    max_label = max(data['mask_colors'].keys()) if data['mask_colors'] else 0

                    # Create a new mask_colors array
                    new_mask_colors = np.zeros((max_label, 3), dtype=np.uint8)

                    # Fill in the colors
                    for label_idx, color in data['mask_colors'].items():
                        if isinstance(label_idx, str):
                            try:
                                label_idx = int(label_idx)
                            except ValueError:
                                continue

                        if 1 <= label_idx <= max_label:
                            new_mask_colors[label_idx-1] = color

                    self.mask_colors = new_mask_colors
                else:
                    self.mask_colors = np.array(data['mask_colors'], dtype=np.uint8)

                print(f"DEBUG: Updated mask colors with shape: {self.mask_colors.shape}")

                # Update the label settings manager
                if hasattr(self, 'label_settings_manager'):
                    self.label_settings_manager.update_label_colors(data['mask_colors'])

            # Update the label selection combo box
            self.update_label_color_indicator()

            # Refresh the display
            if hasattr(self, 'trainable_image') and self.trainable_image is not None:
                self.display_trainable_image()

                # Update the class percentages widget with the new colors
                if hasattr(self, 'class_percentages_widget') and hasattr(self, 'training_labels'):
                    self.calculate_class_percentages(self.training_labels)

                # Also update the segmentation result display if available
                if hasattr(self, 'result') and self.result is not None:
                    self.display_segmentation_result()

            # Update the multi_image_handler with the new label names and colors
            if hasattr(self, 'multi_image_handler'):
                if hasattr(self.multi_image_handler, 'label_names'):
                    self.multi_image_handler.label_names = self.label_names.copy()

                if hasattr(self.multi_image_handler, 'mask_colors'):
                    self.multi_image_handler.mask_colors = self.mask_colors.copy()

                print(f"DEBUG: Updated multi_image_handler with new label names and colors")

        except Exception as e:
            print(f"ERROR: Error updating label data: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"An error occurred while updating label data: {e}")

    def change_label_color(self):
        """Changes the color of the currently selected label."""
        # This method is kept for backward compatibility but is no longer used
        # Use the label management dialog instead
        self.show_label_management_dialog()

    def remove_label(self):
        """Removes the currently selected label using the label management dialog."""
        # Show the label management dialog
        self.show_label_management_dialog()

    def on_mouse_pressed(self, pos):
        """Handles mouse press events for drawing labels."""
        print("on_mouse_pressed called")
        if not hasattr(self, 'trainable_image') or self.trainable_image is None:
            return

        # Initialize training_labels if it's None
        if not hasattr(self, 'training_labels') or self.training_labels is None:
            if hasattr(self, 'trainable_image') and self.trainable_image is not None:
                print("Initializing empty training_labels")
                self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                if hasattr(self, 'multi_image_handler') and hasattr(self.multi_image_handler, 'current_image_path') and self.multi_image_handler.current_image_path:
                    self.multi_image_handler.set_annotations(self.multi_image_handler.current_image_path, self.training_labels)
            else:
                print("Cannot initialize training_labels: trainable_image is None")
                return

        if self.sam_magic_wand_active:
            # Initialize point_prompts list if it doesn't exist
            if not hasattr(self, 'point_prompts'):
                self.point_prompts = []

            # Check which SAM tool is active
            if self.trainable_original_view.current_tool == "sam_point_prompt":
                # Get the current point type (1 for positive, 0 for negative)
                point_type = getattr(self, 'current_point_type', 1)  # Default to positive if not set

                # Create the point with the appropriate type
                point = [pos.x(), pos.y(), point_type]  # x, y, label (1=foreground, 0=background)

                # Add to point prompts list
                self.point_prompts.append(point)

                # Log which type of point was added
                if point_type == 1:
                    print(f"Added positive point at ({pos.x()}, {pos.y()})")
                else:
                    print(f"Added negative point at ({pos.x()}, {pos.y()})")

                # If this is the first point, store it as sam_bbox for backward compatibility
                if len(self.point_prompts) == 1:
                    self.sam_bbox = [pos.x(), pos.y()]

                # Predict mask with all points
                self.predict_sam_mask_with_points()
                return
            else:
                # Start drawing SAM bounding box
                print("Starting SAM bounding box drawing")
                self.drawing_sam_bbox = True
                self.sam_bbox = [pos.x(), pos.y(), pos.x(), pos.y()]
                # Clear any existing point prompts
                if hasattr(self, 'point_prompts'):
                    self.point_prompts = []
                return

        self.last_draw_pos = pos  # Store initial position
        if self.drawing_mode or self.erasing_mode:
            # Make sure training_labels has the correct dimensions
            if hasattr(self, 'trainable_image') and self.trainable_image is not None:
                if not hasattr(self, 'training_labels') or self.training_labels is None:
                    print("DEBUG: Creating new training_labels in on_mouse_pressed")
                    self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                elif self.training_labels.shape != self.trainable_image.shape[:2]:
                    print(f"DEBUG: training_labels shape {self.training_labels.shape} doesn't match image shape {self.trainable_image.shape[:2]}")
                    print("DEBUG: Creating new training_labels with correct dimensions")
                    self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)

                # Now create preview_labels with correct dimensions
                self.preview_labels = self.training_labels.copy()
                print(f"DEBUG: Created preview_labels with shape {self.preview_labels.shape}")
            else:
                print("DEBUG: Cannot create preview_labels: trainable_image is None")
                return

        self.draw_label(pos)  # Initial draw (to preview_labels)


    def on_mouse_moved(self, pos):
        """Handles mouse move events for drawing labels."""
        print("on_mouse_moved called")
        if not hasattr(self, 'trainable_image') or self.trainable_image is None:
            return

        # Initialize training_labels if it's None
        if not hasattr(self, 'training_labels') or self.training_labels is None:
            if hasattr(self, 'trainable_image') and self.trainable_image is not None:
                print("Initializing empty training_labels in on_mouse_moved")
                self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                if hasattr(self, 'multi_image_handler') and hasattr(self.multi_image_handler, 'current_image_path') and self.multi_image_handler.current_image_path:
                    self.multi_image_handler.set_annotations(self.multi_image_handler.current_image_path, self.training_labels)
            else:
                print("Cannot initialize training_labels: trainable_image is None")
                return

        if self.sam_magic_wand_active and self.drawing_sam_bbox:
            # Update SAM bounding box
            print("Updating SAM bounding box")
            self.sam_bbox[2] = pos.x()
            self.sam_bbox[3] = pos.y()
            self.display_trainable_image()
            return

        if self.last_draw_pos is not None:  # Only draw if mouse is pressed
            self.draw_label(pos)


    def on_mouse_released(self, pos):
        """Handles mouse release events."""
        print("on_mouse_released called")
        if not hasattr(self, 'trainable_image') or self.trainable_image is None:
            return

        # Initialize training_labels if it's None
        if not hasattr(self, 'training_labels') or self.training_labels is None:
            if hasattr(self, 'trainable_image') and self.trainable_image is not None:
                print("Initializing empty training_labels in on_mouse_released")
                self.training_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                if hasattr(self, 'multi_image_handler') and hasattr(self.multi_image_handler, 'current_image_path') and self.multi_image_handler.current_image_path:
                    self.multi_image_handler.set_annotations(self.multi_image_handler.current_image_path, self.training_labels.copy())
            else:
                print("Cannot initialize training_labels: trainable_image is None")
                return

        if self.sam_magic_wand_active and self.drawing_sam_bbox:
            # Finish drawing SAM bounding box and predict mask
            print("Finishing SAM bounding box drawing")
            self.drawing_sam_bbox = False
            self.sam_bbox[2] = pos.x()
            self.sam_bbox[3] = pos.y()
            self.predict_sam_mask()
            return

        if self.last_draw_pos is not None:
            self.draw_label(pos)  # Final draw (to preview_labels)
            if self.preview_labels is not None:
                # Save the current state to history before making changes
                self.save_annotation_state()

                # Create a fresh copy of preview_labels to ensure we're not working with a reference
                self.training_labels = self.preview_labels.copy() # Copy preview back to training labels

                # Save annotations and state after each modification
                if hasattr(self, 'multi_image_handler') and hasattr(self.multi_image_handler, 'current_image_path') and self.multi_image_handler.current_image_path:
                    current_path = self.multi_image_handler.current_image_path
                    print(f"DEBUG: Saving annotations after drawing to {current_path}")
                    try:
                        # Make sure we're saving a valid copy
                        if self.training_labels is not None:
                            # Create a fresh copy to ensure we're not passing a reference
                            annotations_copy = self.training_labels.copy()

                            # Force the annotations to be a numpy array of type uint8
                            if not isinstance(annotations_copy, np.ndarray):
                                print(f"DEBUG: Converting annotations to numpy array")
                                annotations_copy = np.array(annotations_copy, dtype=np.uint8)
                            elif annotations_copy.dtype != np.uint8:
                                print(f"DEBUG: Converting annotations dtype from {annotations_copy.dtype} to uint8")
                                annotations_copy = annotations_copy.astype(np.uint8)

                            # Save to multi_image_handler
                            self.multi_image_handler.set_annotations(current_path, annotations_copy)
                            print(f"DEBUG: Successfully saved annotations after drawing")

                            # Also save label names and colors to make them persistent
                            if hasattr(self, 'label_names') and self.label_names:
                                if not hasattr(self.multi_image_handler, 'label_names'):
                                    self.multi_image_handler.label_names = {}
                                self.multi_image_handler.label_names = self.label_names.copy()
                                print(f"DEBUG: Saved label_names after drawing")

                            # Save mask colors
                            if hasattr(self, 'mask_colors') and self.mask_colors is not None:
                                self.multi_image_handler.mask_colors = self.mask_colors.copy()
                                print(f"DEBUG: Saved mask_colors after drawing")

                            # Enable the undo button since we now have a history state
                            if hasattr(self, 'undo_last_button'):
                                self.undo_last_button.setEnabled(True)
                        else:
                            print(f"WARNING: training_labels is None, cannot save annotations")
                    except Exception as e:
                        print(f"ERROR: Failed to save annotations after drawing: {e}")
                        import traceback
                        traceback.print_exc()

            self.last_draw_pos = None  # Reset last position
            self.preview_labels = None  # Reset preview

            self.display_trainable_image()  # Final display update

            # Update the class percentages widget with the new annotations
            if hasattr(self, 'class_percentages_widget') and hasattr(self, 'training_labels'):
                self.calculate_class_percentages(self.training_labels)



    def draw_label(self, pos):
        """Draws or erases labels, drawing on preview_labels."""
        if self.training_labels is None or self.last_draw_pos is None:
            return  # Ensure training_labels is initialized

        # Initialize preview_labels if it's None or has wrong dimensions
        if (self.preview_labels is None or
            (hasattr(self, 'trainable_image') and self.trainable_image is not None and
             self.preview_labels.shape != self.trainable_image.shape[:2])):

            if hasattr(self, 'training_labels') and self.training_labels is not None:
                if hasattr(self, 'trainable_image') and self.trainable_image is not None:
                    # Check if training_labels has the correct dimensions
                    if self.training_labels.shape != self.trainable_image.shape[:2]:
                        print(f"DEBUG: training_labels shape {self.training_labels.shape} doesn't match image shape {self.trainable_image.shape[:2]}")
                        try:
                            # Try to resize training_labels to match image dimensions
                            import cv2
                            resized_labels = cv2.resize(
                                self.training_labels,
                                (self.trainable_image.shape[1], self.trainable_image.shape[0]),
                                interpolation=cv2.INTER_NEAREST
                            )
                            self.preview_labels = resized_labels
                            print(f"DEBUG: Resized preview_labels to {self.preview_labels.shape}")
                        except Exception as e:
                            print(f"DEBUG: Error resizing training_labels for preview: {e}")
                            # Create new empty labels with correct dimensions
                            self.preview_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                            print(f"DEBUG: Created new empty preview_labels with shape {self.preview_labels.shape}")
                    else:
                        print(f"DEBUG: Initializing preview_labels from training_labels with shape {self.training_labels.shape}")
                        self.preview_labels = self.training_labels.copy()
                else:
                    print("Cannot initialize preview_labels: trainable_image is None")
                    return
            else:
                print("Cannot initialize preview_labels: training_labels is None")
                return

        x, y = int(pos.x()), int(pos.y())
        last_x, last_y = int(self.last_draw_pos.x()), int(self.last_draw_pos.y())

        if x < 0 or y < 0 or x >= self.training_labels.shape[1] or y >= self.training_labels.shape[0]:
            return
        if last_x < 0 or last_y < 0 or last_x >= self.training_labels.shape[1] or last_y >= self.training_labels.shape[0]:
            return

        # Get the current brush size from the slider if available
        current_brush_size = self.brush_size
        if hasattr(self, 'trainable_brush_size_slider'):
            current_brush_size = self.trainable_brush_size_slider.value()
            # Update the instance variable to keep it in sync
            self.brush_size = current_brush_size
            print(f"DEBUG: Using brush size from trainable_brush_size_slider: {current_brush_size}")
        else:
            print(f"DEBUG: Using default brush size: {current_brush_size}")

        # Use cv2.line for fast drawing between points (rubber-banding)
        # Import cv2 here to ensure it's available
        import cv2

        # Draw on self.preview_labels
        if self.drawing_mode:
            cv2.line(self.preview_labels, (last_x, last_y), (x, y), self.current_label, current_brush_size)
            print(f"DEBUG: Drawing line with brush size {current_brush_size}")
        elif self.erasing_mode:
            cv2.line(self.preview_labels, (last_x, last_y), (x, y), 0, current_brush_size)
            print(f"DEBUG: Erasing line with brush size {current_brush_size}")

        self.last_draw_pos = pos #Update last postion

        self.display_preview()  # Display the preview


    def display_preview(self):
        """Displays a preview of the drawing using the preview_labels."""
        if not hasattr(self, 'trainable_image') or self.trainable_image is None:
            return

        # Initialize preview_labels if it's None or has wrong dimensions
        if (self.preview_labels is None or
            self.preview_labels.shape != self.trainable_image.shape[:2]):

            if hasattr(self, 'training_labels') and self.training_labels is not None:
                # Check if training_labels has the correct dimensions
                if self.training_labels.shape != self.trainable_image.shape[:2]:
                    print(f"DEBUG: training_labels shape {self.training_labels.shape} doesn't match image shape {self.trainable_image.shape[:2]}")
                    try:
                        # Try to resize training_labels to match image dimensions
                        import cv2
                        resized_labels = cv2.resize(
                            self.training_labels,
                            (self.trainable_image.shape[1], self.trainable_image.shape[0]),
                            interpolation=cv2.INTER_NEAREST
                        )
                        self.preview_labels = resized_labels
                        print(f"DEBUG: Resized preview_labels to {self.preview_labels.shape}")
                    except Exception as e:
                        print(f"DEBUG: Error resizing training_labels for preview: {e}")
                        # Create new empty labels with correct dimensions
                        self.preview_labels = np.zeros(self.trainable_image.shape[:2], dtype=np.uint8)
                        print(f"DEBUG: Created new empty preview_labels with shape {self.preview_labels.shape}")
                else:
                    print(f"DEBUG: Initializing preview_labels from training_labels with shape {self.training_labels.shape}")
                    self.preview_labels = self.training_labels.copy()
            else:
                print("Cannot initialize preview_labels: training_labels is None")
                return

        try:
            display_img = self.trainable_image.copy()
            display_img = self._overlay_mask(display_img, self.preview_labels, self.mask_colors)
            q_img = convert_cvimage_to_qimage(display_img, already_rgb=True)
            pixmap = QPixmap.fromImage(q_img)
            self.trainable_original_view.setPixmap(pixmap)
        except Exception as e:
            print(f"DEBUG: Error in display_preview: {e}")
            import traceback
            traceback.print_exc()

    def toggle_draw_mode(self):
        """Toggles drawing mode for adding labels."""
        self.drawing_mode = not self.drawing_mode

        # Deactivate other tools if drawing mode is activated
        if self.drawing_mode:
            # Deactivate erasing mode if active
            self.erasing_mode = False
            self.erase_button.setText("Erase Label")
            self.erase_button.setStyleSheet(clear_button_styles())

            # Deactivate SAM tools if active
            if hasattr(self, 'sam_magic_wand_button') and self.sam_magic_wand_button.isChecked():
                self.sam_magic_wand_button.setChecked(False)
                self.deactivate_sam_magic_wand()

            if hasattr(self, 'sam_magic_wand_point_button') and self.sam_magic_wand_point_button.isChecked():
                self.sam_magic_wand_point_button.setChecked(False)
                self.deactivate_sam_point_prompt()

            if hasattr(self, 'sam_magic_wand_neg_point_button') and self.sam_magic_wand_neg_point_button.isChecked():
                self.sam_magic_wand_neg_point_button.setChecked(False)
                self.deactivate_sam_neg_point_prompt()

            # Update UI for active drawing mode
            self.draw_button.setText("Stop Drawing")

            # Apply active button style with background highlight
            active_style = """
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    font-weight: bold;
                    border: 1px solid #2E7D32;
                    border-radius: 4px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #66BB6A;
                }
            """
            self.draw_button.setStyleSheet(active_style)

            # Show tooltip with instructions
            QToolTip.showText(self.draw_button.mapToGlobal(self.draw_button.rect().center()),
                             "Drawing mode active. Click and drag to draw labels.")

            # Set cursor to crosshair
            if hasattr(self, 'trainable_original_view'):
                self.trainable_original_view.drawing_enabled = True
                self.trainable_original_view.erasing_enabled = False
                self.trainable_original_view.setCursor(Qt.CrossCursor)

            # Get the current brush size from the slider if available
            current_brush_size = self.brush_size
            if hasattr(self, 'trainable_brush_size_slider'):
                current_brush_size = self.trainable_brush_size_slider.value()
                # Update the instance variable to keep it in sync
                self.brush_size = current_brush_size

            # Update status message
            current_label_name = self.label_names.get(self.current_label, f"Label {self.current_label}")
            self.update_status_message(f"Drawing Mode: Active - Drawing '{current_label_name}' with brush size {current_brush_size}")
        else:
            # Deactivate drawing mode
            self.draw_button.setText("Draw Label")
            self.draw_button.setStyleSheet(clear_button_styles())

            if hasattr(self, 'trainable_original_view'):
                self.trainable_original_view.drawing_enabled = False
                self.trainable_original_view.setCursor(Qt.ArrowCursor)

            # Update status message
            self.update_status_message("Drawing Mode: Inactive")

    def toggle_erase_mode(self):
        """Toggles erasing mode for removing labels."""
        self.erasing_mode = not self.erasing_mode

        # Deactivate other tools if erasing mode is activated
        if self.erasing_mode:
            # Deactivate drawing mode if active
            self.drawing_mode = False
            self.draw_button.setText("Draw Label")
            self.draw_button.setStyleSheet(clear_button_styles())

            # Deactivate SAM tools if active
            if hasattr(self, 'sam_magic_wand_button') and self.sam_magic_wand_button.isChecked():
                self.sam_magic_wand_button.setChecked(False)
                self.deactivate_sam_magic_wand()

            if hasattr(self, 'sam_magic_wand_point_button') and self.sam_magic_wand_point_button.isChecked():
                self.sam_magic_wand_point_button.setChecked(False)
                self.deactivate_sam_point_prompt()

            if hasattr(self, 'sam_magic_wand_neg_point_button') and self.sam_magic_wand_neg_point_button.isChecked():
                self.sam_magic_wand_neg_point_button.setChecked(False)
                self.deactivate_sam_neg_point_prompt()

            # Update UI for active erasing mode
            self.erase_button.setText("Stop Erasing")

            # Apply active button style with background highlight
            active_style = """
                QPushButton {
                    background-color: #F44336;
                    color: white;
                    font-weight: bold;
                    border: 1px solid #D32F2F;
                    border-radius: 4px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #EF5350;
                }
            """
            self.erase_button.setStyleSheet(active_style)

            # Show tooltip with instructions
            QToolTip.showText(self.erase_button.mapToGlobal(self.erase_button.rect().center()),
                             "Erasing mode active. Click and drag to erase labels.")

            # Set cursor to crosshair
            if hasattr(self, 'trainable_original_view'):
                self.trainable_original_view.erasing_enabled = True
                self.trainable_original_view.drawing_enabled = False
                self.trainable_original_view.setCursor(Qt.CrossCursor)

            # Get the current brush size from the slider if available
            current_brush_size = self.brush_size
            if hasattr(self, 'trainable_brush_size_slider'):
                current_brush_size = self.trainable_brush_size_slider.value()
                # Update the instance variable to keep it in sync
                self.brush_size = current_brush_size

            # Update status message
            self.update_status_message(f"Erasing Mode: Active - Erasing with brush size {current_brush_size}")
        else:
            # Deactivate erasing mode
            self.erase_button.setText("Erase Label")
            self.erase_button.setStyleSheet(clear_button_styles())

            if hasattr(self, 'trainable_original_view'):
                self.trainable_original_view.erasing_enabled = False
                self.trainable_original_view.setCursor(Qt.ArrowCursor)

            # Update status message
            self.update_status_message("Erasing Mode: Inactive")

    def clear_all_labels(self):
        """Clears all training labels."""
        if self.training_labels is not None:
            # Ask for confirmation
            reply = QMessageBox.question(
                self,
                "Clear All Labels",
                "Are you sure you want to clear all labels for this image?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Save the current state to history before clearing
                self.save_annotation_state()

                # Apply clear button style temporarily for visual feedback
                if hasattr(self, 'clear_labels_button'):
                    original_style = self.clear_labels_button.styleSheet()
                    self.clear_labels_button.setStyleSheet(clear_button_styles())

                # Clear the labels
                self.training_labels[:] = 0  # Efficiently clear the array

                # Save cleared annotations
                if self.multi_image_handler.current_image_path:
                    self.multi_image_handler.set_annotations(self.multi_image_handler.current_image_path, self.training_labels)

                # Show confirmation message
                QToolTip.showText(self.clear_labels_button.mapToGlobal(self.clear_labels_button.rect().center()),
                                 "All labels cleared.")

                # Reset button style after a short delay
                if hasattr(self, 'clear_labels_button'):
                    QTimer.singleShot(1000, lambda: self.clear_labels_button.setStyleSheet(original_style))

                # Enable undo button
                if hasattr(self, 'undo_last_button'):
                    self.undo_last_button.setEnabled(True)

                self.display_trainable_image()

                # Update the class percentages widget with the cleared annotations
                if hasattr(self, 'class_percentages_widget') and hasattr(self, 'training_labels'):
                    self.calculate_class_percentages(self.training_labels)

    def train_classifier(self):
        """Trains the XGBoost classifier using the labeled pixels from all images."""
        if not hasattr(self, 'trainable_image') or self.trainable_image is None:
            QMessageBox.warning(self, "Warning", "Please upload an image first.")
            return

        # Save current annotations if they exist
        if self.training_labels is not None and self.multi_image_handler.current_image_path:
            self.multi_image_handler.set_annotations(self.multi_image_handler.current_image_path, self.training_labels)

        # Get all images and annotations
        all_images = self.multi_image_handler.get_all_images()
        all_annotations = self.multi_image_handler.get_all_annotations()

        if not all_images or not all_annotations:
            QMessageBox.warning(self, "Warning", "No images or annotations found.")
            return

        # Check if any annotations exist
        has_annotations = False
        for img_path, annotations in all_annotations.items():
            if np.max(annotations) > 0:
                has_annotations = True
                break

        if not has_annotations:
            QMessageBox.warning(self, "Warning", "Please add some training labels first.")
            return

        # Update progress
        self.trainable_progress.setValue(0)

        # Extract features
        sigma_min = self.sigma_min_spinbox.value()
        sigma_max = self.sigma_max_spinbox.value()

        # Store feature extraction parameters
        self.feature_params = {
            'intensity': self.intensity_checkbox.isChecked(),
            'edges': self.edges_checkbox.isChecked(),
            'texture': self.texture_checkbox.isChecked(),
            'sigma_min': sigma_min,
            'sigma_max': sigma_max,
            'channel_axis': -1  # Correct channel axis
        }

        # Create feature extraction function based on selected features
        features_func = partial(
            feature.multiscale_basic_features,
            **self.feature_params
        )

        # Prepare for collecting features and labels from all images
        all_features = []
        all_labels = []
        current_progress = 0

        # Extract features from all images and collect their annotations
        # First, identify which images have annotations
        annotated_images = []
        for img_path, annotations in all_annotations.items():
            if annotations is not None and np.max(annotations) > 0:
                annotated_images.append(img_path)

        # Log how many images have annotations
        print(f"Found {len(annotated_images)} images with annotations out of {len(all_images)} total images")

        # Check if we have any annotated images
        if not annotated_images:
            QMessageBox.warning(self, "Warning", "No images with annotations found. Please annotate at least one image.")
            return

        # Calculate progress step based on number of annotated images
        progress_step = 50 / len(annotated_images)

        # Only process images that have annotations
        for img_path in annotated_images:
            # Get the image and its annotations
            image = all_images.get(img_path)
            annotations = all_annotations.get(img_path)

            if image is None:
                continue

            # Extract features for this image
            image_features = features_func(image)

            # Get labeled pixels
            mask = annotations > 0
            # Add features and labels to our collection
            all_features.append(image_features[mask])
            # Subtract 1 from labels to make them zero-indexed for XGBoost
            all_labels.append(annotations[mask] - 1)

            # Log information about the annotations
            unique_labels = np.unique(annotations[mask])
            print(f"Image {os.path.basename(img_path)} has {np.sum(mask)} labeled pixels with classes: {unique_labels}")

            # Update progress
            current_progress += progress_step
            self.trainable_progress.setValue(int(current_progress))

        # If we have collected features and labels
        if all_features and all_labels:
            # Combine features and labels from all images
            combined_features = np.vstack(all_features)
            combined_labels = np.concatenate(all_labels)

            # Fix for non-consecutive labels: remap labels to consecutive integers starting from 0
            unique_labels = np.unique(combined_labels)
            print(f"DEBUG: Unique labels before remapping: {unique_labels}")

            # Create a mapping from original labels to consecutive integers
            label_mapping = {label: i for i, label in enumerate(unique_labels)}
            print(f"DEBUG: Label mapping: {label_mapping}")

            # Apply the mapping to combined_labels
            remapped_labels = np.zeros_like(combined_labels)
            for original, remapped in label_mapping.items():
                remapped_labels[combined_labels == original] = remapped

            # Replace combined_labels with remapped_labels
            combined_labels = remapped_labels
            print(f"DEBUG: Unique labels after remapping: {np.unique(combined_labels)}")

            # Store the label mapping for later use
            self.label_mapping = label_mapping

            # Create and train classifier
            self.classifier = xgb.XGBClassifier(
                n_estimators=self.n_estimators_spinbox.value(),
                n_jobs=-1,  # Use all available cores
                max_depth=self.max_depth_spinbox.value(),
                subsample=self.max_samples_spinbox.value(),  # XGBoost uses subsample instead of max_samples
                use_label_encoder=False,  # Avoid warning about label encoder
                eval_metric='mlogloss'  # For multi-class classification
            )

            # Fit the classifier with combined data from all images
            self.classifier.fit(combined_features, combined_labels)

            # Convert label_mapping keys from numpy types to Python native types
            label_mapping = None
            if hasattr(self, 'label_mapping') and self.label_mapping:
                # Create a new dictionary with int keys instead of numpy.uint8
                label_mapping = {}
                for k, v in self.label_mapping.items():
                    # Convert numpy types to Python native types
                    key = int(k) if hasattr(k, 'item') else k
                    value = int(v) if hasattr(v, 'item') else v
                    label_mapping[key] = value
                print(f"DEBUG: Original label_mapping before storing in multi_image_handler: {self.label_mapping}")
                print(f"DEBUG: Converted label_mapping for storing in multi_image_handler: {label_mapping}")

            # Convert mask_colors to list of lists if it's a numpy array
            mask_colors = None
            if hasattr(self, 'mask_colors'):
                if isinstance(self.mask_colors, np.ndarray):
                    mask_colors = self.mask_colors.copy()
                else:
                    mask_colors = self.mask_colors

            # Store classifier in multi-image handler
            self.multi_image_handler.set_classifier(
                self.classifier,
                self.feature_params,
                label_mapping,
                mask_colors
            )

            # Extract features for the current image for prediction
            self.features = features_func(self.trainable_image)
        else:
            QMessageBox.warning(self, "Warning", "No labeled pixels found in any image.")
            return

        self.trainable_progress.setValue(80)

        # Predict segmentation with probabilities
        probabilities = self.classifier.predict_proba(self.features.reshape(-1, self.features.shape[-1]))
        # Get the class with highest probability for each pixel
        # XGBoost returns remapped zero-indexed classes
        predicted_classes = np.argmax(probabilities, axis=1)

        # Store the label mapping in the classifier for future use
        if hasattr(self, 'label_mapping') and self.label_mapping:
            # Reverse the mapping to go from remapped labels back to original labels
            # Convert numpy types to Python native types
            reverse_mapping = {}
            for k, v in self.label_mapping.items():
                # Convert numpy types to Python native types
                key = int(k) if hasattr(k, 'item') else k
                value = int(v) if hasattr(v, 'item') else v
                reverse_mapping[value] = key

            print(f"DEBUG: Original label_mapping: {self.label_mapping}")
            print(f"DEBUG: Reverse label mapping: {reverse_mapping}")

            # Apply the reverse mapping to get original labels
            original_classes = np.zeros_like(predicted_classes)
            for remapped, original in reverse_mapping.items():
                original_classes[predicted_classes == remapped] = original + 1  # Add 1 to make 1-indexed

            self.result = original_classes.reshape(self.trainable_image.shape[:2])
        else:
            # If no mapping exists, just add 1 to make consistent with our 1-indexed labels
            self.result = predicted_classes.reshape(self.trainable_image.shape[:2]) + 1
        self.trainable_progress.setValue(100)

        # Display results
        self.display_segmentation_result()
        self.display_feature_importance()

        # Save the segmentation result to the multi-image handler
        if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
            self.multi_image_handler.set_segmentation_result(self.multi_image_handler.current_image_path, self.result)
            print(f"DEBUG: Saved segmentation result for {self.multi_image_handler.current_image_path} after training")

        # Switch to the segmentation result tab
        self.trainable_tab_widget.setCurrentIndex(1)

    def apply_to_new_image(self):
        """Applies the trained classifier to a new image."""
        # First check if we have a classifier in the instance
        if not hasattr(self, 'classifier') or self.classifier is None:
            # If not, try to get it from the multi_image_handler
            if hasattr(self, 'multi_image_handler'):
                print("DEBUG: Retrieving classifier from multi_image_handler in apply_to_new_image")
                classifier, feature_params, label_mapping, mask_colors = self.multi_image_handler.get_classifier()

                # Always update the classifier if it exists in the multi_image_handler
                if classifier is not None:
                    print("DEBUG: Found classifier in multi_image_handler, updating instance")
                    self.classifier = classifier

                    # Update feature parameters if they exist
                    if feature_params is not None:
                        print(f"DEBUG: Updating feature parameters: {feature_params}")
                        self.feature_params = feature_params
                    else:
                        print("DEBUG: No feature parameters found in multi_image_handler")
                        # Set default feature parameters if not found
                        self.feature_params = {
                            'intensity': True,
                            'edges': True,
                            'texture': True,
                            'sigma_min': 1,
                            'sigma_max': 16,
                            'num_sigma': 10
                        }
                        print(f"DEBUG: Using default feature parameters: {self.feature_params}")

                    # Update label mapping if it exists
                    if label_mapping is not None:
                        print(f"DEBUG: Updating label mapping: {label_mapping}")
                        self.label_mapping = label_mapping

                    # Update mask colors if they exist
                    if mask_colors is not None and hasattr(self, 'mask_colors'):
                        print("DEBUG: Updating mask colors")
                        # Only update if the shape is compatible
                        if isinstance(mask_colors, np.ndarray) and mask_colors.shape[0] <= self.mask_colors.shape[0]:
                            self.mask_colors[:mask_colors.shape[0]] = mask_colors
                        elif isinstance(mask_colors, list) and len(mask_colors) <= self.mask_colors.shape[0]:
                            self.mask_colors[:len(mask_colors)] = np.array(mask_colors)
                else:
                    print("DEBUG: No classifier found in multi_image_handler")

                print(f"DEBUG: Classifier status after retrieval: {self.classifier is not None}")

        # Check again after attempting to retrieve from multi_image_handler
        if not hasattr(self, 'classifier') or self.classifier is None:
            QMessageBox.warning(self, "Warning", "Please train a classifier first.")
            return

        if not self.feature_params: # Use the stored dictionary
            # Set default feature parameters if not found
            self.feature_params = {
                'intensity': True,
                'edges': True,
                'texture': True,
                'sigma_min': 1,
                'sigma_max': 16,
                'num_sigma': 10
            }
            print("DEBUG: Using default feature parameters for apply_to_new_image")

        # Open file dialog to select a new image
        file_path = self.open_image_dialog("Select New Image for Segmentation")
        if not file_path:
            return

        # Load the new image
        new_image = cv2.imread(file_path)
        if new_image is None:
            QMessageBox.critical(self, "Error", f"Failed to load image at {file_path}. Check file integrity.")
            return
        new_image = cv2.cvtColor(new_image, cv2.COLOR_BGR2RGB)

        # Update progress
        self.trainable_progress.setValue(0)

        # Extract features using the same parameters as the original training
                # Extract features using the same parameters as training
        features_func = partial(
            feature.multiscale_basic_features,
            **self.feature_params
        )

        # Extract features
        new_features = features_func(new_image)
        self.trainable_progress.setValue(50)

        # Predict segmentation with probabilities
        probabilities = self.classifier.predict_proba(new_features.reshape(-1, new_features.shape[-1]))
        # Get the class with highest probability for each pixel
        # XGBoost returns remapped zero-indexed classes
        predicted_classes = np.argmax(probabilities, axis=1)

        # Apply the reverse mapping if available
        if hasattr(self, 'label_mapping') and self.label_mapping:
            # Reverse the mapping to go from remapped labels back to original labels
            # Convert numpy types to Python native types
            reverse_mapping = {}
            for k, v in self.label_mapping.items():
                # Convert numpy types to Python native types
                key = int(k) if hasattr(k, 'item') else k
                value = int(v) if hasattr(v, 'item') else v
                reverse_mapping[value] = key

            print(f"DEBUG: Original label_mapping in apply_to_new_image: {self.label_mapping}")
            print(f"DEBUG: Reverse label mapping in apply_to_new_image: {reverse_mapping}")

            # Apply the reverse mapping to get original labels
            original_classes = np.zeros_like(predicted_classes)
            for remapped, original in reverse_mapping.items():
                original_classes[predicted_classes == remapped] = original + 1  # Add 1 to make 1-indexed

            new_result = original_classes.reshape(new_image.shape[:2])
        else:
            # If no mapping exists, just add 1 to make consistent with our 1-indexed labels
            new_result = predicted_classes.reshape(new_image.shape[:2]) + 1

        self.trainable_progress.setValue(100)

        # Update the current image and result.  Crucially, *don't* reset the classifier.
        self.trainable_image = new_image
        self.training_labels = np.zeros(new_image.shape[:2], dtype=np.uint8) # Reset training labels
        self.features = new_features  # Keep the new features
        self.result = new_result

        # Display results
        self.display_trainable_image()  # Show the new image
        self.display_segmentation_result()  # And the segmentation

        # Save the segmentation result to the multi-image handler
        if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
            self.multi_image_handler.set_segmentation_result(self.multi_image_handler.current_image_path, self.result)
            print(f"DEBUG: Saved segmentation result for {self.multi_image_handler.current_image_path} after applying to new image")

        # Update image info
        h, w, c = new_image.shape
        self.trainable_image_info_label.setText(f"Image: {os.path.basename(file_path)}\nSize: {w}x{h}\nChannels: {c}")

        # Switch to the segmentation result tab
        self.trainable_tab_widget.setCurrentIndex(1)

    def apply_to_current_image(self):
        """Applies the trained classifier to the current image."""
        # First check if we have a classifier in the instance
        if not hasattr(self, 'classifier') or self.classifier is None:
            # If not, try to get it from the multi_image_handler
            if hasattr(self, 'multi_image_handler'):
                print("DEBUG: Retrieving classifier from multi_image_handler in apply_to_current_image")
                classifier, feature_params, label_mapping, mask_colors = self.multi_image_handler.get_classifier()

                # Always update the classifier if it exists in the multi_image_handler
                if classifier is not None:
                    print("DEBUG: Found classifier in multi_image_handler, updating instance")
                    self.classifier = classifier

                    # Update feature parameters if they exist
                    if feature_params is not None:
                        print(f"DEBUG: Updating feature parameters: {feature_params}")
                        self.feature_params = feature_params
                    else:
                        print("DEBUG: No feature parameters found in multi_image_handler")
                        # Set default feature parameters if not found
                        self.feature_params = {
                            'intensity': True,
                            'edges': True,
                            'texture': True,
                            'sigma_min': 1,
                            'sigma_max': 16,
                            'num_sigma': 10
                        }
                        print(f"DEBUG: Using default feature parameters: {self.feature_params}")

                    # Update label mapping if it exists
                    if label_mapping is not None:
                        print(f"DEBUG: Updating label mapping: {label_mapping}")
                        self.label_mapping = label_mapping

                    # Update mask colors if they exist
                    if mask_colors is not None and hasattr(self, 'mask_colors'):
                        print("DEBUG: Updating mask colors")
                        # Only update if the shape is compatible
                        if isinstance(mask_colors, np.ndarray) and mask_colors.shape[0] <= self.mask_colors.shape[0]:
                            self.mask_colors[:mask_colors.shape[0]] = mask_colors
                        elif isinstance(mask_colors, list) and len(mask_colors) <= self.mask_colors.shape[0]:
                            self.mask_colors[:len(mask_colors)] = np.array(mask_colors)
                else:
                    print("DEBUG: No classifier found in multi_image_handler")

                print(f"DEBUG: Classifier status after retrieval: {self.classifier is not None}")

        # Check again after attempting to retrieve from multi_image_handler
        if not hasattr(self, 'classifier') or self.classifier is None:
            QMessageBox.warning(self, "Warning", "Please train a classifier first.")
            return

        if not hasattr(self, 'trainable_image') or self.trainable_image is None:
            QMessageBox.warning(self, "Warning", "No image loaded.")
            return

        if not self.feature_params:
            # Set default feature parameters if not found
            self.feature_params = {
                'intensity': True,
                'edges': True,
                'texture': True,
                'sigma_min': 1,
                'sigma_max': 16,
                'num_sigma': 10
            }
            print("DEBUG: Using default feature parameters for apply_to_current_image")

        # Update progress
        self.trainable_progress.setValue(0)

        # Extract features using the same parameters as training
        features_func = partial(
            feature.multiscale_basic_features,
            **self.feature_params
        )

        # Extract features
        new_features = features_func(self.trainable_image)
        self.trainable_progress.setValue(50)

        # Predict segmentation with probabilities
        probabilities = self.classifier.predict_proba(new_features.reshape(-1, new_features.shape[-1]))
        # Get the class with highest probability for each pixel
        # XGBoost returns remapped zero-indexed classes
        predicted_classes = np.argmax(probabilities, axis=1)

        # Apply the reverse mapping if available
        if hasattr(self, 'label_mapping') and self.label_mapping:
            # Reverse the mapping to go from remapped labels back to original labels
            # Convert numpy types to Python native types
            reverse_mapping = {}
            for k, v in self.label_mapping.items():
                # Convert numpy types to Python native types
                key = int(k) if hasattr(k, 'item') else k
                value = int(v) if hasattr(v, 'item') else v
                reverse_mapping[value] = key

            print(f"DEBUG: Original label_mapping in apply_to_current_image: {self.label_mapping}")
            print(f"DEBUG: Reverse label mapping in apply_to_current_image: {reverse_mapping}")

            # Apply the reverse mapping to get original labels
            original_classes = np.zeros_like(predicted_classes)
            for remapped, original in reverse_mapping.items():
                original_classes[predicted_classes == remapped] = original + 1  # Add 1 to make 1-indexed

            new_result = original_classes.reshape(self.trainable_image.shape[:2])
        else:
            # If no mapping exists, just add 1 to make consistent with our 1-indexed labels
            new_result = predicted_classes.reshape(self.trainable_image.shape[:2]) + 1

        self.trainable_progress.setValue(100)

        # Update features and result
        self.features = new_features
        self.result = new_result

        # Display results
        self.display_segmentation_result()

        # Save the segmentation result to the multi-image handler
        if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
            self.multi_image_handler.set_segmentation_result(self.multi_image_handler.current_image_path, self.result)
            print(f"DEBUG: Saved segmentation result for {self.multi_image_handler.current_image_path} after applying to current image")

        # Switch to the segmentation result tab
        self.trainable_tab_widget.setCurrentIndex(1)

    def save_classifier(self):
        """Saves the trained classifier to a file."""
        if self.classifier is None:  # Simplified check
            QMessageBox.warning(self, "Warning", "Please train a classifier first.")
            return

        # Get the trainable segmentation state directory as the default save location
        # Use the same directory as annotations
        default_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "state")
        if hasattr(self, 'project') and self.project:
            # Use the project's data directory structure
            project_file = self.project.project_file
            project_dir = os.path.dirname(project_file)
            project_name = self.project.name
            default_dir = os.path.join(project_dir, f"{project_name}_data", "state", "trainable_segmentation")
        os.makedirs(default_dir, exist_ok=True)

        # Create a default filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"trainable_classifier_{timestamp}.pkl"
        default_path = os.path.join(default_dir, default_filename)

        # Open file dialog to select save location with default path
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Classifier",
            default_path,
            "Pickle Files (*.pkl)"
        )
        if not file_path:
            return

        # Ensure the file has .pkl extension
        if not file_path.endswith('.pkl'):
            file_path += '.pkl'

        # Convert label_mapping keys from numpy types to Python native types
        label_mapping = None
        if hasattr(self, 'label_mapping') and self.label_mapping:
            # Create a new dictionary with int keys instead of numpy.uint8
            label_mapping = {}
            for k, v in self.label_mapping.items():
                # Convert numpy types to Python native types
                key = int(k) if hasattr(k, 'item') else k
                value = int(v) if hasattr(v, 'item') else v
                label_mapping[key] = value
            print(f"DEBUG: Original label_mapping: {self.label_mapping}")
            print(f"DEBUG: Converted label_mapping for saving: {label_mapping}")

        # Convert mask_colors to list of lists if it's a numpy array
        mask_colors = None
        if hasattr(self, 'mask_colors'):
            if isinstance(self.mask_colors, np.ndarray):
                mask_colors = self.mask_colors.tolist()
            else:
                mask_colors = self.mask_colors

        # Save the classifier, feature parameters, label mapping, and mask colors
        save_data = {
            'classifier': self.classifier,
            'feature_params': self.feature_params,
            'label_mapping': label_mapping,
            'mask_colors': mask_colors
        }
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(save_data, f)
            QMessageBox.information(self, "Success", f"Classifier saved to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save classifier: {e}")


    def infer_feature_params_from_classifier(self, classifier):
        """Attempt to infer feature parameters from a classifier based on its input dimension."""
        try:
            # Get the number of features the classifier expects
            if hasattr(classifier, 'feature_importances_'):
                n_features = len(classifier.feature_importances_)
            elif hasattr(classifier, 'get_booster') and hasattr(classifier.get_booster(), 'feature_names'):
                n_features = len(classifier.get_booster().feature_names)
            else:
                # Try to infer from the model structure
                print("DEBUG: Could not directly determine feature count, trying to infer...")
                # Common feature counts and their corresponding parameters
                feature_counts = {
                    15: {'intensity': True, 'edges': True, 'texture': False, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 5},
                    30: {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 5},
                    45: {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 10},
                    50: {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 10},
                    60: {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 15}
                }

                # Default to 45 features if we can't determine
                return feature_counts.get(45, {
                    'intensity': True,
                    'edges': True,
                    'texture': True,
                    'sigma_min': 1,
                    'sigma_max': 16,
                    'num_sigma': 10
                })

            print(f"DEBUG: Detected classifier with {n_features} features")

            # Map feature count to parameters
            if n_features == 15:
                return {'intensity': True, 'edges': True, 'texture': False, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 5}
            elif n_features == 30:
                return {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 5}
            elif n_features == 45:
                return {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 10}
            elif n_features == 50:
                return {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 10}
            elif n_features == 60:
                return {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 15}
            else:
                print(f"DEBUG: Unknown feature count: {n_features}, using default parameters")
                return {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 10}
        except Exception as e:
            print(f"DEBUG: Error inferring feature parameters: {e}")
            return {'intensity': True, 'edges': True, 'texture': True, 'sigma_min': 1, 'sigma_max': 16, 'num_sigma': 10}

    def save_segmentation_results(self):
        """Saves the current segmentation results as PNG image and class distribution as CSV."""
        # Import cv2 at the beginning to ensure it's available throughout the method
        import cv2

        if not hasattr(self, 'result') or self.result is None:
            QMessageBox.warning(self, "Warning", "No segmentation results to save. Please train a classifier and apply it first.")
            return

        if not hasattr(self, 'trainable_image') or self.trainable_image is None:
            QMessageBox.warning(self, "Warning", "No image loaded. Cannot save segmentation results.")
            return

        # Get the trainable segmentation state directory as the default save location
        default_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "state")
        if hasattr(self, 'project') and self.project:
            # Use the project's data directory structure
            project_file = self.project.project_file
            project_dir = os.path.dirname(project_file)
            project_name = self.project.name
            default_dir = os.path.join(project_dir, f"{project_name}_data", "state", "trainable_segmentation")
        os.makedirs(default_dir, exist_ok=True)

        # Create a default filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        image_name = "unknown"
        if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
            image_name = os.path.splitext(os.path.basename(self.multi_image_handler.current_image_path))[0]

        # Create a folder to save both PNG and CSV files
        save_folder = QFileDialog.getExistingDirectory(
            self,
            "Select Directory to Save Segmentation Results",
            default_dir
        )

        if not save_folder:
            return

        # Create filenames for PNG and CSV
        png_filename = f"{image_name}_segmentation_{timestamp}.png"
        csv_filename = f"{image_name}_class_distribution_{timestamp}.csv"

        png_path = os.path.join(save_folder, png_filename)
        csv_path = os.path.join(save_folder, csv_filename)

        # 1. Save the segmented image as PNG
        try:
            # Create a visualization of the segmentation result with colors
            from skimage import segmentation

            # Create a colored visualization of the segmentation
            display_img = self.trainable_image.copy()
            display_img = self._overlay_mask(display_img, self.result, self.mask_colors)

            # Convert to uint8 for saving
            display_img = display_img.astype(np.uint8)

            # Create a second image with boundaries for reference
            boundaries_img = segmentation.mark_boundaries(
                self.trainable_image, self.result, mode='thick', color=(1,1,1)
            )
            boundaries_img = (boundaries_img * 255).astype(np.uint8)

            # Save the colored segmentation image
            cv2.imwrite(png_path, cv2.cvtColor(display_img, cv2.COLOR_RGB2BGR))

            # Also save the boundaries image with a different filename
            boundaries_path = os.path.join(save_folder, f"{image_name}_boundaries_{timestamp}.png")
            cv2.imwrite(boundaries_path, cv2.cvtColor(boundaries_img, cv2.COLOR_RGB2BGR))
            print(f"DEBUG: Segmentation image saved to {png_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save segmentation image: {e}")
            print(f"DEBUG: Error saving segmentation image: {e}")
            return

        # 2. Save the class distribution as CSV
        try:
            # Calculate class percentages
            percentages = self.calculate_class_percentages(self.result)

            # Create a CSV file with class distribution
            import csv
            with open(csv_path, 'w', newline='') as csvfile:
                csv_writer = csv.writer(csvfile)
                # Write header
                csv_writer.writerow(['Segment Name', 'Percentage'])

                # Write data rows
                for segment_name, percentage in percentages.items():
                    csv_writer.writerow([segment_name, f"{percentage:.2f}"])

            print(f"DEBUG: Class distribution saved to {csv_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save class distribution: {e}")
            print(f"DEBUG: Error saving class distribution: {e}")
            return

        boundaries_filename = f"{image_name}_boundaries_{timestamp}.png"
        QMessageBox.information(
            self,
            "Success",
            f"Segmentation results saved successfully:\n\n" +
            f"Colored Segmentation: {png_filename}\n" +
            f"Boundaries Image: {boundaries_filename}\n" +
            f"Class Distribution: {csv_filename}"
        )

    def load_classifier(self):
        """Loads a classifier from a file."""
        # Get the trainable segmentation state directory as the default load location
        default_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "state")
        if hasattr(self, 'project') and self.project:
            # Use the project's data directory structure
            project_file = self.project.project_file
            project_dir = os.path.dirname(project_file)
            project_name = self.project.name
            default_dir = os.path.join(project_dir, f"{project_name}_data", "state", "trainable_segmentation")

        # Open file dialog to select classifier file
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Load Classifier",
            default_dir,
            "Pickle Files (*.pkl)"
        )
        if not file_path:
            return

        # Load the classifier
        try:
            with open(file_path, 'rb') as f:
                try:
                    # Try to load the classifier normally
                    save_data = pickle.load(f)

                    # Check if save_data is a dictionary
                    if isinstance(save_data, dict):
                        self.classifier = save_data.get('classifier')
                        if 'feature_params' in save_data and save_data['feature_params']:
                            self.feature_params = save_data.get('feature_params')
                            print(f"DEBUG: Loaded feature parameters from file: {self.feature_params}")
                        else:
                            # Infer feature parameters from the classifier
                            self.feature_params = self.infer_feature_params_from_classifier(self.classifier)
                            print(f"DEBUG: Inferred feature parameters: {self.feature_params}")
                            QMessageBox.information(self, "Information", "Inferred feature parameters from classifier.")

                        # Load label mapping if available
                        if 'label_mapping' in save_data and save_data['label_mapping']:
                            self.label_mapping = save_data.get('label_mapping')
                            print(f"DEBUG: Loaded label mapping from file: {self.label_mapping}")

                        # Load mask colors if available
                        if 'mask_colors' in save_data and save_data['mask_colors']:
                            # Convert list to numpy array
                            loaded_colors = np.array(save_data.get('mask_colors'))
                            # Only update if the shape is compatible
                            if hasattr(self, 'mask_colors') and loaded_colors.shape[0] <= self.mask_colors.shape[0]:
                                # Copy the colors to preserve the original colors
                                self.mask_colors[:loaded_colors.shape[0]] = loaded_colors
                                print(f"DEBUG: Loaded mask colors from file: {loaded_colors}")
                    else:
                        # If save_data is not a dictionary, assume it's just the classifier
                        self.classifier = save_data
                        # Infer feature parameters from the classifier
                        self.feature_params = self.infer_feature_params_from_classifier(self.classifier)
                        print(f"DEBUG: Inferred feature parameters: {self.feature_params}")
                        QMessageBox.information(self, "Information", "Inferred feature parameters from classifier.")

                    # Verify the classifier is valid
                    if not hasattr(self.classifier, 'predict_proba'):
                        raise ValueError("The loaded object does not appear to be a valid classifier.")

                except Exception as inner_e:
                    # If normal loading fails, try to create a new classifier and copy parameters
                    print(f"DEBUG: Error during normal loading: {inner_e}")
                    print("DEBUG: Attempting to recreate classifier from scratch...")

                    # Rewind the file and try to load it again
                    f.seek(0)
                    old_classifier = pickle.load(f)

                    # Create a new classifier with the same parameters
                    new_classifier = xgb.XGBClassifier(
                        n_estimators=100,  # Default values
                        max_depth=3,
                        subsample=1.0,
                        use_label_encoder=False,
                        eval_metric='mlogloss'
                    )

                    # Try to copy the model data
                    if hasattr(old_classifier, 'get_booster'):
                        # For newer XGBoost versions
                        model_data = old_classifier.get_booster().save_raw()
                        new_classifier._Booster = xgb.Booster()
                        new_classifier._Booster.load_model(model_data)
                    elif hasattr(old_classifier, '_Booster'):
                        # For older XGBoost versions
                        new_classifier._Booster = old_classifier._Booster
                    else:
                        raise ValueError("Could not extract model data from the classifier.")

                    # Set the classifier and feature params
                    self.classifier = new_classifier
                    if isinstance(old_classifier, dict) and 'feature_params' in old_classifier:
                        self.feature_params = old_classifier['feature_params']
                        print(f"DEBUG: Loaded feature parameters from dictionary: {self.feature_params}")
                    else:
                        # Infer feature parameters from the classifier
                        self.feature_params = self.infer_feature_params_from_classifier(self.classifier)
                        print(f"DEBUG: Inferred feature parameters: {self.feature_params}")
                        QMessageBox.information(self, "Information", "Inferred feature parameters from classifier.")

            QMessageBox.information(self, "Success", f"Classifier loaded from {file_path}")

            # If we have an image and features, apply the classifier
            if self.trainable_image is not None:
                try:
                    # Extract features using the inferred parameters
                    features_func = partial(
                        feature.multiscale_basic_features,
                        **self.feature_params
                    )

                    # Extract features
                    new_features = features_func(self.trainable_image)
                    self.features = new_features  # Update features with the correct parameters

                    # Apply classifier
                    probabilities = self.classifier.predict_proba(new_features.reshape(-1, new_features.shape[-1]))
                    predicted_classes = np.argmax(probabilities, axis=1)

                    # Apply the reverse mapping if available
                    if hasattr(self, 'label_mapping') and self.label_mapping:
                        # Reverse the mapping to go from remapped labels back to original labels
                        reverse_mapping = {v: k for k, v in self.label_mapping.items()}

                        # Apply the reverse mapping to get original labels
                        original_classes = np.zeros_like(predicted_classes)
                        for remapped, original in reverse_mapping.items():
                            original_classes[predicted_classes == remapped] = original + 1  # Add 1 to make 1-indexed

                        self.result = original_classes.reshape(self.trainable_image.shape[:2])
                    else:
                        # If no mapping exists, just add 1 to make consistent with our 1-indexed labels
                        self.result = predicted_classes.reshape(self.trainable_image.shape[:2]) + 1
                    self.display_segmentation_result()
                    self.display_feature_importance()
                    self.trainable_tab_widget.setCurrentIndex(1)
                except Exception as apply_e:
                    QMessageBox.warning(self, "Warning", f"Classifier loaded but could not be applied to the current image: {apply_e}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load classifier: {e}")

    # Annotation loading/saving functionality has been removed

    def _has_existing_annotations(self):
        """Check if there are existing annotations for the current image.

        Returns:
            bool: True if annotations exist, False otherwise.
        """
        # Check if we have a current image path
        if not hasattr(self, 'multi_image_handler') or not self.multi_image_handler.current_image_path:
            return False

        current_path = self.multi_image_handler.current_image_path

        # Get annotations for the current image
        annotations = self.multi_image_handler.get_annotations(current_path)

        # Check if annotations exist and contain any non-zero values
        if annotations is not None and np.any(annotations > 0):
            logger.debug(f"Found existing annotations for {current_path}")
            return True

        return False

    def reset_to_defaults(self):
        """Reset label names and colors to default values."""
        logger.debug("Resetting labels to defaults")

        # Reset label names to defaults
        self.label_names = {}
        for i, name in enumerate(DEFAULT_LABEL_NAMES):
            self.label_names[i+1] = name  # 1-indexed labels

        # Reset mask colors to defaults
        self.mask_colors = DEFAULT_MASK_COLORS.copy()

        # Update the UI
        if hasattr(self, 'update_label_color_indicator'):
            self.update_label_color_indicator()

        # Update the class percentages widget if it exists
        if hasattr(self, 'class_percentages_widget') and hasattr(self, 'training_labels'):
            self.calculate_class_percentages(self.training_labels)

        # Save the default settings
        if hasattr(self, 'label_settings_manager'):
            self.label_settings_manager.update_label_names(self.label_names)
            self.label_settings_manager.update_label_colors(self.mask_colors)

        logger.debug("Labels reset to defaults")

    def _confirm_reset_labels(self):
        """Show a confirmation dialog asking if labels should be reset before importing.

        Returns:
            bool: True if user confirms reset, False otherwise.
        """
        result = QMessageBox.question(
            self,
            "Existing Annotations Detected",
            "Existing annotations detected. Would you like to reset labels to defaults before importing new annotations? "
            "This is recommended to avoid naming conflicts and ensure consistency.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes  # Yes is the default
        )

        if result == QMessageBox.Yes:
            logger.debug("User confirmed reset labels to defaults")
            # Reset labels to defaults
            self.reset_to_defaults()
            return True

        return False

    def load_exported_annotations(self, file_path=None, show_messages=True):
        """Load annotations from an exported NPZ file from the unsupervised segmentation page.

        Args:
            file_path (str, optional): Path to the NPZ file. If None, a file dialog will be shown.
            show_messages (bool, optional): Whether to show message boxes. Defaults to True.

        Returns:
            bool: True if annotations were loaded successfully, False otherwise.
        """
        # If no file path provided, show file dialog
        if file_path is None:
            file_path = self.open_file_dialog("Load Exported Annotations", "NumPy Files (*.npz);;All Files (*)")
            if not file_path:
                return False

        # Ensure the file exists
        if not os.path.exists(file_path):
            if show_messages:
                QMessageBox.critical(self, "Error", f"File not found: {file_path}")
            return False

        # Check if there are existing annotations and ask for confirmation to reset labels
        if self._has_existing_annotations():
            self._confirm_reset_labels()

        try:
            # Load the NPZ file
            data = np.load(file_path, allow_pickle=True)
            print(f"DEBUG: Loaded NPZ file with keys: {list(data.keys())}")

            # Check if annotations are present
            if 'annotations' not in data:
                if show_messages:
                    QMessageBox.critical(self, "Error", "Invalid annotation file. No annotations found.")
                return False

            # Load annotations
            annotations = data['annotations']

            # Check if annotations are valid
            if annotations is None or (isinstance(annotations, np.ndarray) and annotations.size == 0):
                if show_messages:
                    QMessageBox.critical(self, "Error", "Empty annotations in file.")
                return False

            # Ensure annotations are a numpy array
            if not isinstance(annotations, np.ndarray):
                print(f"WARNING: Annotations are not a numpy array. Converting.")
                annotations = np.array(annotations, dtype=np.uint8)

            # Get unique labels in the annotations
            unique_labels = np.unique(annotations)
            unique_labels = unique_labels[unique_labels > 0]  # Exclude background (0)
            print(f"DEBUG: Found {len(unique_labels)} unique labels in annotations: {unique_labels}")

            # Initialize segment names and colors
            segment_names = {}
            segment_colors = {}

            # Load segment names if available
            if 'segment_names' in data:
                # Direct dictionary format
                segment_names_data = data['segment_names']
                if isinstance(segment_names_data, np.ndarray):
                    # Try to convert numpy array to dictionary
                    try:
                        segment_names = segment_names_data.item()
                    except:
                        print(f"WARNING: Could not convert segment_names to dictionary")
                else:
                    segment_names = segment_names_data
                print(f"DEBUG: Loaded segment names: {segment_names}")
            elif 'segment_names_json' in data:
                # JSON string format
                try:
                    segment_names_json = data['segment_names_json']
                    if isinstance(segment_names_json, np.ndarray) and segment_names_json.size == 1:
                        segment_names_json = segment_names_json.item()
                    segment_names = json.loads(segment_names_json)
                    print(f"DEBUG: Loaded segment names from JSON: {segment_names}")
                except Exception as e:
                    print(f"WARNING: Could not parse segment_names_json: {e}")

            # Load segment colors if available
            if 'segment_colors' in data:
                # Direct dictionary format
                segment_colors_data = data['segment_colors']
                if isinstance(segment_colors_data, np.ndarray):
                    # Try to convert numpy array to dictionary
                    try:
                        segment_colors = segment_colors_data.item()
                    except:
                        print(f"WARNING: Could not convert segment_colors to dictionary")
                else:
                    segment_colors = segment_colors_data
                print(f"DEBUG: Loaded segment colors: {segment_colors}")
            elif 'segment_colors_json' in data:
                # JSON string format
                try:
                    segment_colors_json = data['segment_colors_json']
                    if isinstance(segment_colors_json, np.ndarray) and segment_colors_json.size == 1:
                        segment_colors_json = segment_colors_json.item()
                    segment_colors = json.loads(segment_colors_json)
                    print(f"DEBUG: Loaded segment colors from JSON: {segment_colors}")
                except Exception as e:
                    print(f"WARNING: Could not parse segment_colors_json: {e}")

            # Convert string keys to integers if needed
            if segment_names:
                segment_names = {int(k) if isinstance(k, str) else k: v for k, v in segment_names.items()}
            if segment_colors:
                segment_colors = {int(k) if isinstance(k, str) else k: v for k, v in segment_colors.items()}

            # Update label names and colors
            if segment_names:
                # Merge with existing label names
                if not hasattr(self, 'label_names') or not self.label_names:
                    self.label_names = {}

                # Special labels that need to be preserved
                special_labels = ["porosity", "glauconite", "quartz"]

                # First, check if any of the imported segment names are special labels
                special_label_mapping = {}
                for label_id, name in segment_names.items():
                    if name.lower() in special_labels:
                        special_label_mapping[name.lower()] = label_id
                        print(f"DEBUG: Found special label '{name}' with ID {label_id}")

                # Check if any of the existing labels are special labels
                existing_special_labels = {}
                for existing_id, existing_name in self.label_names.items():
                    if existing_name.lower() in special_labels:
                        existing_special_labels[existing_name.lower()] = existing_id
                        print(f"DEBUG: Found existing special label '{existing_name}' with ID {existing_id}")

                # Add new segment names
                for label_id, name in segment_names.items():
                    # Check if this is a special label
                    if name.lower() in special_labels:
                        # If this special label already exists, use the existing ID
                        if name.lower() in existing_special_labels:
                            existing_id = existing_special_labels[name.lower()]
                            # Update the color for this special label (handled in the color update section)
                            print(f"DEBUG: Special label '{name}' already exists with ID {existing_id}")
                            continue
                        else:
                            # This is a new special label, add it
                            self.label_names[label_id] = name
                            print(f"DEBUG: Added new special label '{name}' with ID {label_id}")
                    else:
                        # Regular label - check if this name already exists
                        name_exists = False
                        for existing_id, existing_name in self.label_names.items():
                            if existing_name.lower() == name.lower():
                                name_exists = True
                                break

                        if not name_exists:
                            # Find the next available index if needed
                            if label_id in self.label_names:
                                next_id = max(self.label_names.keys()) + 1 if self.label_names else 1
                                self.label_names[next_id] = name
                            else:
                                self.label_names[label_id] = name

                # Update the label settings manager
                if hasattr(self, 'label_settings_manager'):
                    self.label_settings_manager.update_label_names(self.label_names)

                print(f"DEBUG: Updated label names: {self.label_names}")

            # Update colors
            if segment_colors:
                # Convert to numpy array if needed
                if not hasattr(self, 'mask_colors') or self.mask_colors is None:
                    # Create a new mask_colors array
                    max_label = max(segment_colors.keys()) if segment_colors else 0
                    self.mask_colors = np.zeros((max_label, 3), dtype=np.uint8)

                # Ensure mask_colors is large enough
                max_label = max(segment_colors.keys()) if segment_colors else 0
                if isinstance(self.mask_colors, np.ndarray) and self.mask_colors.shape[0] < max_label:
                    # Extend the array
                    extended_colors = np.zeros((max_label, 3), dtype=np.uint8)
                    extended_colors[:self.mask_colors.shape[0]] = self.mask_colors
                    self.mask_colors = extended_colors

                # Special labels that need to be preserved
                special_labels = ["porosity", "glauconite", "quartz"]

                # Create a mapping of special label names to their IDs and colors
                special_label_ids = {}
                for label_id, name in self.label_names.items():
                    if name.lower() in special_labels:
                        special_label_ids[name.lower()] = label_id

                # Create a mapping of imported label IDs to special label IDs
                imported_to_special_mapping = {}
                for label_id, name in segment_names.items() if segment_names else {}:
                    if name.lower() in special_labels and name.lower() in special_label_ids:
                        imported_to_special_mapping[label_id] = special_label_ids[name.lower()]
                        print(f"DEBUG: Mapping imported label ID {label_id} to special label ID {special_label_ids[name.lower()]} for '{name}'")

                # Update colors
                for label_id, color in segment_colors.items():
                    # Check if this is a special label that needs to be mapped
                    if label_id in imported_to_special_mapping:
                        special_id = imported_to_special_mapping[label_id]
                        if isinstance(self.mask_colors, np.ndarray) and 0 <= special_id-1 < self.mask_colors.shape[0]:
                            self.mask_colors[special_id-1] = color
                            print(f"DEBUG: Updated color for special label ID {special_id} with color from imported label ID {label_id}")
                    else:
                        # Regular label, update color normally
                        if isinstance(self.mask_colors, np.ndarray) and 0 <= label_id-1 < self.mask_colors.shape[0]:
                            self.mask_colors[label_id-1] = color

                # Update the label settings manager
                if hasattr(self, 'label_settings_manager'):
                    self.label_settings_manager.update_label_colors(self.mask_colors)

                print(f"DEBUG: Updated mask colors with shape: {self.mask_colors.shape if isinstance(self.mask_colors, np.ndarray) else 'dict'}")

            # Set the annotations for the current image
            if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
                current_path = self.multi_image_handler.current_image_path

                # Save annotations to multi_image_handler
                self.multi_image_handler.set_annotations(current_path, annotations)

                # Update the current training labels
                self.training_labels = annotations.copy()

                print(f"DEBUG: Set annotations for current image: {current_path}")

            # Update the UI
            self.update_label_color_indicator()
            self.display_trainable_image()

            # Update the class percentages widget with the new colors
            if hasattr(self, 'class_percentages_widget') and hasattr(self, 'training_labels'):
                self.calculate_class_percentages(self.training_labels)

            # Show success message
            if show_messages:
                QMessageBox.information(self, "Success", f"Loaded annotations with {len(unique_labels)} segments.")

            return True

        except Exception as e:
            import traceback
            traceback.print_exc()
            if show_messages:
                QMessageBox.critical(self, "Error", f"Failed to load annotations: {e}")
            return False

    def export_segmentation(self):
        """Exports the segmentation result as an image."""
        if self.result is None:  # Simplified check
            QMessageBox.warning(self, "Warning", "No segmentation result to export.")
            return

        # Open file dialog to select save location
        file_path, _ = self.save_file_dialog("Export Segmentation", "PNG Files (*.png);;JPEG Files (*.jpg)")
        if not file_path:
            return

        # Create a visualization of the segmentation result
        if hasattr(self, 'trainable_image') and self.trainable_image is not None:
             # Display segmentation boundaries on original image
            boundaries_img = segmentation.mark_boundaries(
                self.trainable_image, self.result, mode='thick', color=(1,1,1)) #Consistent label indexing
            boundaries_img = (boundaries_img * 255).astype(np.uint8)

            # Save the image
            try:
                cv2.imwrite(file_path, cv2.cvtColor(boundaries_img, cv2.COLOR_RGB2BGR))
                QMessageBox.information(self, "Success", f"Segmentation exported to {file_path}")
            except Exception as e:
                QMessageBox.critical(self,"Error", f"Failed to export image: {e}")

    def load_all_annotations(self):
        """Loads all annotations from a previously saved JSON metadata file.

        This method:
        1. Opens a file dialog to select a JSON metadata file
        2. Loads all annotations from the associated subdirectory
        3. Updates the MultiImageTrainableHandler with all loaded annotations
        4. Handles label names and colors according to specified rules
        5. Updates the UI to display the loaded annotations
        """
        # Check if we have any images loaded
        if not hasattr(self, 'multi_image_handler') or not self.multi_image_handler.images:
            QMessageBox.warning(self, "Warning", "No images loaded. Please load images first.")
            return

        # Check if there are existing annotations and ask for confirmation to reset labels
        if self._has_existing_annotations():
            self._confirm_reset_labels()

        # Get the default directory
        default_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "..", "new_project_data", "state", "trainable_segmentation")
        if hasattr(self, 'project') and self.project:
            # Use the project's data directory structure
            project_file = self.project.project_file
            project_dir = os.path.dirname(project_file)
            project_name = self.project.name
            default_dir = os.path.join(project_dir, f"{project_name}_data", "state", "trainable_segmentation")

        # Show file dialog to select the JSON metadata file
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Load All Annotations",
            default_dir,
            "JSON Files (*.json)"
        )

        if not file_path:
            return

        # Check if the file exists
        if not os.path.exists(file_path):
            QMessageBox.critical(self, "Error", f"File not found: {file_path}")
            return

        # Load the JSON metadata file
        try:
            with open(file_path, 'r') as f:
                metadata = json.load(f)

            # Check if the metadata has the expected structure
            if not isinstance(metadata, dict) or "images" not in metadata:
                QMessageBox.critical(self, "Error", "Invalid metadata file format. Missing 'images' key.")
                return

            # Get the directory containing the annotation files
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            annotations_dir = os.path.join(os.path.dirname(file_path), base_name)

            # Check if the annotations directory exists
            if not os.path.exists(annotations_dir) or not os.path.isdir(annotations_dir):
                QMessageBox.critical(self, "Error", f"Annotations directory not found: {annotations_dir}")
                return

            # Process label names and colors from metadata
            if "label_names" in metadata and metadata["label_names"]:
                # Convert string keys back to integers
                loaded_label_names = {}
                for k, v in metadata["label_names"].items():
                    try:
                        # Convert string key to integer
                        key = int(k)
                        loaded_label_names[key] = v
                    except ValueError:
                        print(f"WARNING: Could not convert label key '{k}' to integer. Skipping.")

                # Special labels that need to be preserved
                special_labels = ["porosity", "glauconite", "quartz"]

                # First, check if any of the loaded label names are special labels
                special_label_mapping = {}
                for label_id, name in loaded_label_names.items():
                    if name.lower() in special_labels:
                        special_label_mapping[name.lower()] = label_id
                        print(f"DEBUG: Found special label '{name}' with ID {label_id} in loaded annotations")

                # Check if any of the existing labels are special labels
                existing_special_labels = {}
                if hasattr(self, 'label_names') and self.label_names:
                    for existing_id, existing_name in self.label_names.items():
                        if existing_name.lower() in special_labels:
                            existing_special_labels[existing_name.lower()] = existing_id
                            print(f"DEBUG: Found existing special label '{existing_name}' with ID {existing_id}")

                # Merge with existing label names according to the rules
                if hasattr(self, 'label_names') and self.label_names:
                    # For each loaded label name
                    for label_id, label_name in loaded_label_names.items():
                        # Check if this is a special label
                        if label_name.lower() in special_labels:
                            # If this special label already exists, use the existing ID
                            if label_name.lower() in existing_special_labels:
                                existing_id = existing_special_labels[label_name.lower()]
                                # Update the color for this special label (handled in the color update section)
                                print(f"DEBUG: Special label '{label_name}' already exists with ID {existing_id}")
                                continue
                            else:
                                # This is a new special label, add it
                                self.label_names[label_id] = label_name
                                print(f"DEBUG: Added new special label '{label_name}' with ID {label_id}")
                        else:
                            # Regular label - check if this name already exists
                            exists = False
                            for _, existing_name in self.label_names.items():
                                if existing_name == label_name:
                                    exists = True
                                    break

                            # If it doesn't exist, add it
                            if not exists:
                                # Find the next available index
                                next_idx = max(self.label_names.keys()) + 1 if self.label_names else 1
                                self.label_names[next_idx] = label_name
                else:
                    # No existing label names, use the loaded ones
                    self.label_names = loaded_label_names

                # Update the label settings manager
                if hasattr(self, 'label_settings_manager'):
                    self.label_settings_manager.update_label_names(self.label_names)

                print(f"DEBUG: Updated label names: {self.label_names}")

            # Process mask colors from metadata
            if "mask_colors" in metadata and metadata["mask_colors"]:
                loaded_mask_colors = np.array(metadata["mask_colors"], dtype=np.uint8)

                # Special labels that need to be preserved
                special_labels = ["porosity", "glauconite", "quartz"]

                # Create a mapping of special label names to their IDs
                special_label_ids = {}
                for label_id, name in self.label_names.items():
                    if name.lower() in special_labels:
                        special_label_ids[name.lower()] = label_id
                        print(f"DEBUG: Found special label '{name}' with ID {label_id}")

                # Create a mapping of loaded label IDs to special label IDs
                loaded_to_special_mapping = {}
                for label_id, name in loaded_label_names.items():
                    if name.lower() in special_labels and name.lower() in special_label_ids:
                        loaded_to_special_mapping[label_id] = special_label_ids[name.lower()]
                        print(f"DEBUG: Mapping loaded label ID {label_id} to special label ID {special_label_ids[name.lower()]} for '{name}'")

                # Ensure we have enough colors for all labels
                max_label = max(self.label_names.keys()) if self.label_names else 0
                if loaded_mask_colors.shape[0] < max_label:
                    # Extend the loaded colors array
                    extended_colors = np.zeros((max_label, 3), dtype=np.uint8)
                    extended_colors[:loaded_mask_colors.shape[0]] = loaded_mask_colors

                    # Fill in missing colors from DEFAULT_MASK_COLORS
                    for i in range(loaded_mask_colors.shape[0], max_label):
                        if i < len(DEFAULT_MASK_COLORS):
                            extended_colors[i] = DEFAULT_MASK_COLORS[i]
                        else:
                            # Generate a random color if we run out of default colors
                            extended_colors[i] = np.random.randint(0, 255, 3)

                    self.mask_colors = extended_colors
                else:
                    self.mask_colors = loaded_mask_colors

                # Update colors for special labels
                for loaded_id, special_id in loaded_to_special_mapping.items():
                    if 1 <= loaded_id <= loaded_mask_colors.shape[0] and 1 <= special_id <= max_label:
                        # Get the color from the loaded colors
                        color = loaded_mask_colors[loaded_id-1]
                        # Update the color for the special label
                        self.mask_colors[special_id-1] = color
                        print(f"DEBUG: Updated color for special label ID {special_id} with color from loaded label ID {loaded_id}")

                # Update the label settings manager
                if hasattr(self, 'label_settings_manager'):
                    self.label_settings_manager.update_label_colors(self.mask_colors)

                print(f"DEBUG: Updated mask colors with shape: {self.mask_colors.shape}")

            # Load all annotations from the metadata
            success_count = 0
            error_count = 0
            error_messages = []
            skipped_count = 0

            # Create a progress dialog
            progress = QProgressDialog("Loading annotations...", "Cancel", 0, len(metadata["images"]), self)
            progress.setWindowTitle("Loading Annotations")
            progress.setWindowModality(Qt.WindowModal)
            progress.show()

            # Process each image in the metadata
            for i, image_info in enumerate(metadata["images"]):
                # Update progress
                progress.setValue(i)
                if progress.wasCanceled():
                    break

                try:
                    # Get the image path and annotation file
                    image_path = image_info.get("image_path")
                    annotation_file = image_info.get("annotation_file")

                    if not image_path or not annotation_file:
                        print(f"WARNING: Missing image_path or annotation_file in metadata entry: {image_info}")
                        skipped_count += 1
                        continue

                    # Check if the image exists in our collection
                    if image_path not in self.multi_image_handler.images:
                        print(f"WARNING: Image path {image_path} not found in current collection. Skipping.")
                        skipped_count += 1
                        continue

                    # Construct the full path to the annotation file
                    annotation_path = os.path.join(annotations_dir, annotation_file)

                    # Check if the annotation file exists
                    if not os.path.exists(annotation_path):
                        print(f"WARNING: Annotation file not found: {annotation_path}")
                        skipped_count += 1
                        continue

                    # Load the annotation file
                    try:
                        npz_data = np.load(annotation_path, allow_pickle=True)

                        # Check if the file has the expected structure
                        if 'annotations' not in npz_data:
                            print(f"WARNING: Missing 'annotations' in {annotation_path}")
                            skipped_count += 1
                            continue

                        # Get the annotations
                        annotations = npz_data['annotations']

                        # Check if annotations are valid
                        if annotations is None or (isinstance(annotations, np.ndarray) and annotations.size == 0):
                            print(f"WARNING: Empty annotations in {annotation_path}")
                            skipped_count += 1
                            continue

                        # Ensure annotations are a numpy array
                        if not isinstance(annotations, np.ndarray):
                            print(f"WARNING: Annotations in {annotation_path} are not a numpy array. Converting.")
                            annotations = np.array(annotations, dtype=np.uint8)

                        # Check if annotations dimensions match the image dimensions
                        image = self.multi_image_handler.images.get(image_path)
                        if image is not None and annotations.shape[:2] != image.shape[:2]:
                            print(f"WARNING: Annotations dimensions {annotations.shape[:2]} don't match image dimensions {image.shape[:2]}. Resizing.")
                            try:
                                # Resize annotations to match image dimensions
                                import cv2
                                resized_annotations = cv2.resize(
                                    annotations,
                                    (image.shape[1], image.shape[0]),
                                    interpolation=cv2.INTER_NEAREST
                                )
                                annotations = resized_annotations
                            except Exception as e:
                                print(f"ERROR: Failed to resize annotations: {e}")
                                error_count += 1
                                error_messages.append(f"Failed to resize annotations for {image_path}: {e}")
                                continue

                        # Save annotations to multi_image_handler
                        self.multi_image_handler.set_annotations(image_path, annotations)

                        # Load segmentation result if available
                        if 'segmentation_result' in npz_data:
                            segmentation_result = npz_data['segmentation_result']

                            # Check if segmentation result is valid
                            if segmentation_result is not None and isinstance(segmentation_result, np.ndarray) and segmentation_result.size > 0:
                                # Check if dimensions match the image dimensions
                                if image is not None and segmentation_result.shape[:2] != image.shape[:2]:
                                    print(f"WARNING: Segmentation result dimensions {segmentation_result.shape[:2]} don't match image dimensions {image.shape[:2]}. Resizing.")
                                    try:
                                        # Resize segmentation result to match image dimensions
                                        import cv2
                                        resized_result = cv2.resize(
                                            segmentation_result,
                                            (image.shape[1], image.shape[0]),
                                            interpolation=cv2.INTER_NEAREST
                                        )
                                        segmentation_result = resized_result
                                    except Exception as e:
                                        print(f"ERROR: Failed to resize segmentation result: {e}")
                                        # Continue anyway, we'll just skip the segmentation result

                                # Save segmentation result to multi_image_handler
                                self.multi_image_handler.set_segmentation_result(image_path, segmentation_result)
                                print(f"DEBUG: Loaded segmentation result for {image_path}")

                        success_count += 1
                        print(f"DEBUG: Loaded annotations for {image_path}")
                    except Exception as e:
                        error_count += 1
                        error_message = f"Error loading annotations from {annotation_path}: {str(e)}"
                        error_messages.append(error_message)
                        print(f"ERROR: {error_message}")
                except Exception as e:
                    error_count += 1
                    error_message = f"Error processing metadata entry: {str(e)}"
                    error_messages.append(error_message)
                    print(f"ERROR: {error_message}")

            # Close the progress dialog
            progress.setValue(len(metadata["images"]))

            # Update the UI
            if hasattr(self, 'update_label_color_indicator'):
                self.update_label_color_indicator()
                print(f"DEBUG: Updated label selection combo box")

            # If we're currently viewing an image, reload its annotations
            if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
                current_path = self.multi_image_handler.current_image_path

                # Get annotations for the current image
                annotations = self.multi_image_handler.get_annotations(current_path)
                if annotations is not None:
                    self.training_labels = annotations.copy()
                    print(f"DEBUG: Loaded annotations for current image: {current_path}")

                # Get segmentation result for the current image
                segmentation_result = self.multi_image_handler.get_segmentation_result(current_path)
                if segmentation_result is not None:
                    self.result = segmentation_result.copy()
                    print(f"DEBUG: Loaded segmentation result for current image: {current_path}")

                # Update the display
                self.display_trainable_image()

                # Display segmentation result if available
                if self.result is not None:
                    self.display_segmentation_result()
                    # Switch to the segmentation result tab if we have a result
                    if hasattr(self, 'trainable_tab_widget'):
                        self.trainable_tab_widget.setCurrentIndex(1)

            # Show success message
            if error_count > 0:
                QMessageBox.warning(
                    self,
                    "Load All Annotations",
                    f"Loaded annotations for {success_count} images with {error_count} errors.\n\n"
                    f"Skipped {skipped_count} images.\n\n"
                    f"Errors:\n{chr(10).join(error_messages[:5])}"
                    f"{chr(10)}..." if len(error_messages) > 5 else ""
                )
            else:
                QMessageBox.information(
                    self,
                    "Load All Annotations",
                    f"Successfully loaded annotations for {success_count} images.\n\n"
                    f"Skipped {skipped_count} images."
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to load annotations: {str(e)}"
            )
            print(f"ERROR: Failed to load annotations: {e}")
            import traceback
            traceback.print_exc()

    def save_all_annotations(self):
        """Saves all annotations from all images in a single operation.

        Creates:
        1. A JSON file with metadata and references
        2. A subdirectory with individual annotation files
        """
        # Check if we have any images loaded
        if not hasattr(self, 'multi_image_handler') or not self.multi_image_handler.images:
            QMessageBox.warning(self, "Warning", "No images loaded. Nothing to save.")
            return

        # Get the default directory
        default_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "..", "new_project_data", "state", "trainable_segmentation")
        if hasattr(self, 'project') and self.project:
            # Use the project's data directory structure
            project_file = self.project.project_file
            project_dir = os.path.dirname(project_file)
            project_name = self.project.name
            default_dir = os.path.join(project_dir, f"{project_name}_data", "state", "trainable_segmentation")

        # Create the directory if it doesn't exist
        os.makedirs(default_dir, exist_ok=True)

        # Create a default filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"all_annotations_{timestamp}.json"
        default_path = os.path.join(default_dir, default_filename)

        # Show file dialog to select save location
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save All Annotations",
            default_path,
            "JSON Files (*.json)"
        )

        if not file_path:
            return

        # Create the subdirectory with the same name as the JSON file (without extension)
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        save_dir = os.path.join(os.path.dirname(file_path), base_name)
        os.makedirs(save_dir, exist_ok=True)

        # Save the current state before saving all annotations
        if hasattr(self, 'multi_image_handler') and self.multi_image_handler.current_image_path:
            current_path = self.multi_image_handler.current_image_path
            # Save annotations
            if hasattr(self, 'training_labels') and self.training_labels is not None:
                print(f"DEBUG: Saving current annotations for {current_path} before saving all")
                self.multi_image_handler.set_annotations(current_path, self.training_labels.copy())
            # Save segmentation results
            if hasattr(self, 'result') and self.result is not None:
                print(f"DEBUG: Saving current segmentation result for {current_path} before saving all")
                self.multi_image_handler.set_segmentation_result(current_path, self.result.copy())

        # Prepare the metadata
        metadata = {
            "timestamp": timestamp,
            "images": [],
            "label_names": {},
            "mask_colors": []
        }

        # Add label names if available
        if hasattr(self, 'label_names') and self.label_names:
            # Convert label_names to a serializable format
            label_names_dict = {}
            for k, v in self.label_names.items():
                label_names_dict[str(k)] = v
            metadata["label_names"] = label_names_dict

        # Add mask colors if available
        if hasattr(self, 'mask_colors') and self.mask_colors is not None:
            if isinstance(self.mask_colors, np.ndarray):
                metadata["mask_colors"] = self.mask_colors.tolist()
            else:
                metadata["mask_colors"] = self.mask_colors

        # Get all annotations and segmentation results from the multi_image_handler
        all_annotations = self.multi_image_handler.get_all_annotations()
        all_segmentation_results = self.multi_image_handler.get_all_segmentation_results()

        # Save each annotation to a separate file
        success_count = 0
        skipped_count = 0
        error_count = 0
        error_messages = []

        for image_path, annotations in all_annotations.items():
            try:
                # Skip if annotations is None
                if annotations is None:
                    print(f"DEBUG: Skipping None annotations for {image_path}")
                    skipped_count += 1
                    continue

                # Check if annotations are empty (all zeros or all background)
                if isinstance(annotations, np.ndarray):
                    # Check if all values are 0 (background)
                    if np.all(annotations == 0):
                        print(f"DEBUG: Skipping empty annotations (all zeros) for {image_path}")
                        skipped_count += 1
                        continue

                    # Check if there are any non-zero values (actual annotations)
                    if np.count_nonzero(annotations) == 0:
                        print(f"DEBUG: Skipping empty annotations (no non-zero values) for {image_path}")
                        skipped_count += 1
                        continue

                    # Check if annotations contain any actual labels (values > 0)
                    unique_values = np.unique(annotations)
                    if len(unique_values) <= 1 and unique_values[0] == 0:
                        print(f"DEBUG: Skipping annotations with only background for {image_path}")
                        skipped_count += 1
                        continue

                    print(f"DEBUG: Found valid annotations for {image_path} with unique values: {unique_values}")
                else:
                    # If annotations are not a numpy array, check if they're empty
                    if not annotations:
                        print(f"DEBUG: Skipping empty annotations for {image_path}")
                        skipped_count += 1
                        continue

                # Create a filename for this annotation
                image_filename = os.path.splitext(os.path.basename(image_path))[0]
                annotation_filename = f"{image_filename}_annotation.npz"
                annotation_path = os.path.join(save_dir, annotation_filename)

                # Get the segmentation result if available
                segmentation_result = all_segmentation_results.get(image_path)

                # Check if we should include this image based on segmentation results
                # If there are no annotations but there is a segmentation result, we should still save it
                if np.count_nonzero(annotations) == 0 and segmentation_result is None:
                    print(f"DEBUG: Skipping image with no annotations and no segmentation result: {image_path}")
                    skipped_count += 1
                    continue

                # Prepare the data to save
                save_data = {
                    'annotations': annotations,
                    'image_path': image_path,
                    'format': "trainable_segmentation"
                }

                # Add segmentation result if available
                if segmentation_result is not None:
                    save_data['segmentation_result'] = segmentation_result

                # Save the annotation file
                np.savez_compressed(annotation_path, **save_data)

                # Add to metadata
                metadata["images"].append({
                    "image_path": image_path,
                    "annotation_file": annotation_filename,
                    "has_segmentation_result": segmentation_result is not None
                })

                success_count += 1
                print(f"DEBUG: Saved annotation for {image_path} to {annotation_path}")
            except Exception as e:
                error_count += 1
                error_message = f"Error saving annotation for {image_path}: {str(e)}"
                error_messages.append(error_message)
                print(f"ERROR: {error_message}")

        # Save the metadata to the JSON file
        try:
            # Only save metadata if we have at least one image with annotations
            if success_count > 0:
                with open(file_path, 'w') as f:
                    json.dump(metadata, f, indent=2)

                # Show success message
                if error_count > 0:
                    QMessageBox.warning(
                        self,
                        "Save All Annotations",
                        f"Saved annotations for {success_count} images with {error_count} errors.\n\n"
                        f"Skipped {skipped_count} images with no annotations.\n\n"
                        f"Metadata saved to:\n{file_path}\n\n"
                        f"Annotation files saved to:\n{save_dir}\n\n"
                        f"Errors:\n{chr(10).join(error_messages[:5])}"
                        f"{chr(10)}..." if len(error_messages) > 5 else ""
                    )
                else:
                    QMessageBox.information(
                        self,
                        "Save All Annotations",
                        f"Successfully saved annotations for {success_count} images.\n\n"
                        f"Skipped {skipped_count} images with no annotations.\n\n"
                        f"Metadata saved to:\n{file_path}\n\n"
                        f"Annotation files saved to:\n{save_dir}"
                    )
            else:
                # No annotations were saved
                QMessageBox.information(
                    self,
                    "Save All Annotations",
                    f"No annotations were saved. All {skipped_count} images had no annotations."
                )
                # Clean up the empty directory since we didn't save anything
                try:
                    os.rmdir(save_dir)
                    print(f"DEBUG: Removed empty directory: {save_dir}")
                except:
                    pass
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to save metadata file: {str(e)}"
            )
            print(f"ERROR: Failed to save metadata file: {e}")

    def export_features(self):
        """Exports the extracted features as a numpy array."""
        if self.features is None:  # Simplified check
            QMessageBox.warning(self, "Warning", "No features to export.")
            return

        # Open file dialog to select save location
        file_path, _ = self.save_file_dialog("Export Features", "NumPy Files (*.npy)")
        if not file_path:
            return

        # Ensure the file has .npy extension
        if not file_path.endswith('.npy'):
            file_path += '.npy'

        # Save the features
        try:
            np.save(file_path, self.features)
            QMessageBox.information(self, "Success", f"Features exported to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save features: {e}")

    def export_classifier(self):
        """Exports the trained classifier (same as save_classifier)."""
        self.save_classifier()

    def export_annotations(self):
        """Annotation loading/saving functionality has been removed."""
        return False


    def import_annotations(self):
        """Annotation loading/saving functionality has been removed."""
        return False

    def save_state(self):
        """
        This method is now disabled in favor of manual save/load.
        Use save_annotations instead.
        """
        return False

    def load_state(self, show_message=True):  # pylint: disable=unused-argument
        """
        This method is now disabled in favor of manual save/load.
        Use load_annotations instead.
        """
        return False

    def get_current_timestamp(self):
        """Returns the current timestamp as a string."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def save_annotations_to_file(self):
        """Annotation loading/saving functionality has been removed."""
        return False

    def load_annotations_from_file(self):
        """Annotation loading/saving functionality has been removed."""
        return False

    # Theme handling
    def apply_theme(self, theme_name=None, style_params=None):  # pylint: disable=unused-argument
        """Apply theme to the trainable segmentation page.

        Args:
            theme_name (str, optional): The name of the theme to apply. Defaults to None.
            style_params (dict, optional): Style parameters for the theme. Defaults to None.
        """
        try:
            # Update tab styling for the trainable tab widget
            if hasattr(self, 'trainable_tab_widget'):
                # Remove any hardcoded styling
                self.trainable_tab_widget.setStyleSheet("")

                # If we have a base_ui instance with update_tab_styling method, use it
                if hasattr(self, 'update_tab_styling'):
                    self.update_tab_styling()
        except Exception:
            import traceback
            traceback.print_exc()

    # Keep these methods for backward compatibility
    def save_annotations(self):
        """Alias for save_annotations_to_file for backward compatibility."""
        return self.save_annotations_to_file()

    def load_annotations(self):
        """Alias for load_annotations_from_file for backward compatibility."""
        return self.load_annotations_from_file()