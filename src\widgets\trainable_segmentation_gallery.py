# src/widgets/trainable_segmentation_gallery.py

from PySide6.QtWidgets import QPushButton, QHBoxLayout, QWidget
# QIcon is used in the parent class
from PySide6.QtCore import Signal
import logging

from src.widgets.page_image_gallery import PageImageGallery

# Configure logger
logger = logging.getLogger(__name__)

class TrainableSegmentationGallery(PageImageGallery):
    """Image gallery specifically for the Trainable Segmentation Page."""

    # Additional signals specific to trainable segmentation page
    training_requested = Signal(int)  # Signal to request training on an image

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_training_controls()

    def clear_images(self):
        """Removes all images and thumbnails from the gallery."""
        # Call the parent class's clear method to handle the actual clearing
        self.clear()
        logger.debug("TrainableSegmentationGallery: All images cleared.")

    def reinitialize(self):
        """Reinitializes the gallery if it's in an invalid state."""
        logger.debug("Reinitializing TrainableSegmentationGallery")
        # Check if the container layout is valid
        if not self._ensure_valid_layout():
            # If not, recreate the UI
            self.setup_ui()
            # Recreate the training controls
            self.setup_training_controls()
            return True
        return False

    def setup_training_controls(self):
        """Adds training-specific controls to the gallery."""
        # Create a container for additional controls
        controls_container = QWidget()
        controls_layout = QHBoxLayout(controls_container)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        controls_layout.setSpacing(5)

        # Add a button to train on the selected image
        train_button = QPushButton("Train Selected")
        train_button.setToolTip("Train on the selected image")
        train_button.clicked.connect(self._on_train_clicked)
        controls_layout.addWidget(train_button)

        # Add the controls container to the layout
        self.layout.addWidget(controls_container)

    def _on_train_clicked(self):
        """Handler for train button clicks."""
        if self.selected_index >= 0:
            self.training_requested.emit(self.selected_index)

    def get_all_images(self):
        """Returns all images in the gallery."""
        return self.images.copy()

    def get_all_file_paths(self):
        """Returns all file paths in the gallery."""
        return self.file_paths.copy()
