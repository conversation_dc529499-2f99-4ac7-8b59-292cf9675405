"""
A simple QComboBox subclass that ensures the dropdown appears when clicked.
"""

from PySide6.QtWidgets import QComboBox
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QMouseEvent
import logging

logger = logging.getLogger(__name__)

class ClickableComboBox(QComboBox):
    """
    A simple QComboBox subclass that ensures the dropdown appears when clicked.
    This class overrides mousePressEvent to ensure the dropdown is shown.
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        # Set strong focus policy to ensure it receives focus and mouse events
        self.setFocusPolicy(Qt.StrongFocus)
        
    def mousePressEvent(self, event):
        """Override to ensure the dropdown is shown when clicked."""
        if event.button() == Qt.LeftButton:
            logger.debug("ClickableComboBox: mousePressEvent detected, showing dropdown")
            self.showPopup()
        
        # Call the parent implementation to ensure normal behavior
        super().mousePressEvent(event)
