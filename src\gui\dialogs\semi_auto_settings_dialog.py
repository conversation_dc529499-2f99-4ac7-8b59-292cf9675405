# src/gui/dialogs/semi_auto_settings_dialog.py

from PySide6.QtWidgets import (QD<PERSON>og, Q<PERSON>oxLayout, QHBoxLayout, QLabel, QPushButton,
                              QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QGroupBox,
                              QFormLayout, QDialogButtonBox)
from PySide6.QtCore import Qt

from src.utils.point_classifier import PointClassifier

class SemiAutoSettingsDialog(QDialog):
    """Dialog for configuring semi-automatic point classification settings."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Semi-Auto Classification Settings")
        self.setMinimumWidth(400)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Feature Extraction Settings
        feature_group = QGroupBox("Feature Extraction")
        feature_layout = QFormLayout(feature_group)
        
        self.patch_size_spin = QSpinBox()
        self.patch_size_spin.setRange(5, 51)
        self.patch_size_spin.setSingleStep(2)  # Only odd values
        self.patch_size_spin.setValue(15)
        self.patch_size_spin.valueChanged.connect(self._ensure_odd_patch_size)
        feature_layout.addRow("Patch Size:", self.patch_size_spin)
        
        self.use_color_check = QCheckBox("Use Color Features")
        self.use_color_check.setChecked(True)
        feature_layout.addRow("", self.use_color_check)
        
        self.use_texture_check = QCheckBox("Use Texture Features")
        self.use_texture_check.setChecked(True)
        feature_layout.addRow("", self.use_texture_check)
        
        layout.addWidget(feature_group)
        
        # Classifier Settings
        classifier_group = QGroupBox("Classifier")
        classifier_layout = QFormLayout(classifier_group)
        
        self.classifier_combo = QComboBox()
        for key, name in PointClassifier.get_classifier_types().items():
            self.classifier_combo.addItem(name, key)
        self.classifier_combo.currentIndexChanged.connect(self._update_classifier_options)
        classifier_layout.addRow("Classifier Type:", self.classifier_combo)
        
        # KNN Settings
        self.knn_settings = QGroupBox("KNN Settings")
        knn_layout = QFormLayout(self.knn_settings)
        
        self.n_neighbors_spin = QSpinBox()
        self.n_neighbors_spin.setRange(1, 20)
        self.n_neighbors_spin.setValue(5)
        knn_layout.addRow("Number of Neighbors:", self.n_neighbors_spin)
        
        self.weights_combo = QComboBox()
        self.weights_combo.addItems(["uniform", "distance"])
        self.weights_combo.setCurrentText("distance")
        knn_layout.addRow("Weight Function:", self.weights_combo)
        
        # SVM Settings
        self.svm_settings = QGroupBox("SVM Settings")
        svm_layout = QFormLayout(self.svm_settings)
        
        self.c_spin = QDoubleSpinBox()
        self.c_spin.setRange(0.1, 100.0)
        self.c_spin.setSingleStep(0.1)
        self.c_spin.setValue(1.0)
        svm_layout.addRow("C (Regularization):", self.c_spin)
        
        self.kernel_combo = QComboBox()
        self.kernel_combo.addItems(["linear", "poly", "rbf", "sigmoid"])
        self.kernel_combo.setCurrentText("rbf")
        svm_layout.addRow("Kernel:", self.kernel_combo)
        
        self.gamma_combo = QComboBox()
        self.gamma_combo.addItems(["scale", "auto"])
        self.gamma_combo.setCurrentText("scale")
        svm_layout.addRow("Gamma:", self.gamma_combo)
        
        # Random Forest Settings
        self.rf_settings = QGroupBox("Random Forest Settings")
        rf_layout = QFormLayout(self.rf_settings)
        
        self.n_estimators_spin = QSpinBox()
        self.n_estimators_spin.setRange(10, 500)
        self.n_estimators_spin.setSingleStep(10)
        self.n_estimators_spin.setValue(100)
        rf_layout.addRow("Number of Trees:", self.n_estimators_spin)
        
        self.max_depth_spin = QSpinBox()
        self.max_depth_spin.setRange(1, 50)
        self.max_depth_spin.setValue(10)
        self.max_depth_spin.setSpecialValueText("None")  # Display "None" for value 1
        rf_layout.addRow("Max Depth:", self.max_depth_spin)
        
        # Add all classifier settings to layout
        classifier_layout.addRow("", self.knn_settings)
        classifier_layout.addRow("", self.svm_settings)
        classifier_layout.addRow("", self.rf_settings)
        
        layout.addWidget(classifier_group)
        
        # Auto-classification Settings
        auto_group = QGroupBox("Auto-classification")
        auto_layout = QFormLayout(auto_group)
        
        self.min_confidence_spin = QDoubleSpinBox()
        self.min_confidence_spin.setRange(0.0, 1.0)
        self.min_confidence_spin.setSingleStep(0.05)
        self.min_confidence_spin.setValue(0.7)
        self.min_confidence_spin.setDecimals(2)
        auto_layout.addRow("Minimum Confidence:", self.min_confidence_spin)
        
        self.visual_distinction_check = QCheckBox("Visually Distinguish Auto-classified Points")
        self.visual_distinction_check.setChecked(True)
        auto_layout.addRow("", self.visual_distinction_check)
        
        layout.addWidget(auto_group)
        
        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        # Initialize UI state
        self._update_classifier_options()
        
    def _ensure_odd_patch_size(self, value):
        """Ensure patch size is always odd."""
        if value % 2 == 0:
            self.patch_size_spin.setValue(value + 1)
            
    def _update_classifier_options(self):
        """Show/hide classifier-specific settings based on selection."""
        classifier_key = self.classifier_combo.currentData()
        
        self.knn_settings.setVisible(classifier_key == 'knn')
        self.svm_settings.setVisible(classifier_key == 'svm')
        self.rf_settings.setVisible(classifier_key == 'rf')
        
    def get_settings(self):
        """Get the current settings as a dictionary."""
        classifier_key = self.classifier_combo.currentData()
        
        settings = {
            'feature_extraction': {
                'patch_size': self.patch_size_spin.value(),
                'use_color': self.use_color_check.isChecked(),
                'use_texture': self.use_texture_check.isChecked()
            },
            'classifier': {
                'type': classifier_key,
                'min_confidence': self.min_confidence_spin.value(),
                'visual_distinction': self.visual_distinction_check.isChecked()
            }
        }
        
        # Add classifier-specific settings
        if classifier_key == 'knn':
            settings['classifier']['n_neighbors'] = self.n_neighbors_spin.value()
            settings['classifier']['weights'] = self.weights_combo.currentText()
        elif classifier_key == 'svm':
            settings['classifier']['C'] = self.c_spin.value()
            settings['classifier']['kernel'] = self.kernel_combo.currentText()
            settings['classifier']['gamma'] = self.gamma_combo.currentText()
        elif classifier_key == 'rf':
            settings['classifier']['n_estimators'] = self.n_estimators_spin.value()
            max_depth = self.max_depth_spin.value()
            settings['classifier']['max_depth'] = None if max_depth == 1 else max_depth
            
        return settings
