# src/gui/detectron2_model_trainer_dialog.py
import os
import logging
import json
import torch
import numpy as np
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                              QGroupBox, QFormLayout, QSpinBox, QDoubleSpinBox,
                              QComboBox, QCheckBox, QFileDialog, QMessageBox,
                              QTabWidget, QWidget, QProgressBar, QLineEdit, QTextEdit)
from PySide6.QtCore import Qt, Signal, Slot, QThread, QTimer

logger = logging.getLogger(__name__)

class TrainingWorker(QThread):
    """Worker thread for training Detectron2 models."""

    # Signals
    progress_updated = Signal(int)
    status_updated = Signal(str)
    training_completed = Signal(bool, str)  # success, message

    def __init__(self, config, dataset_dir, output_dir):
        super().__init__()
        self.config = config
        self.dataset_dir = dataset_dir
        self.output_dir = output_dir
        self.is_running = True

    def run(self):
        """Run the training process."""
        try:
            # Import required modules
            import os
            import sys

            # Import detectron2 here to avoid import errors if not installed
            try:
                from detectron2.config import get_cfg
                from detectron2.engine import DefaultTrainer
                from detectron2.data import MetadataCatalog, DatasetCatalog
                from detectron2.data.datasets import register_coco_instances
                from detectron2.utils.logger import setup_logger
            except ImportError:
                # Try to import from local path
                detectron2_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'detectron2')
                if os.path.exists(detectron2_path):
                    sys.path.append(os.path.dirname(detectron2_path))
                    from detectron2.config import get_cfg
                    from detectron2.engine import DefaultTrainer
                    from detectron2.data import MetadataCatalog, DatasetCatalog
                    from detectron2.data.datasets import register_coco_instances
                    from detectron2.utils.logger import setup_logger
                else:
                    raise ImportError("Detectron2 not found in local path")

            # Set up detectron2 logger
            setup_logger()

            # Register the dataset
            train_json = os.path.join(self.dataset_dir, "train", "annotations.json")
            train_images = os.path.join(self.dataset_dir, "train", "images")
            val_json = os.path.join(self.dataset_dir, "val", "annotations.json")
            val_images = os.path.join(self.dataset_dir, "val", "images")

            # Check if annotation files exist
            if not os.path.exists(train_json):
                self.training_completed.emit(False, f"Training annotation file not found: {train_json}")
                return

            if not os.path.exists(val_json):
                self.training_completed.emit(False, f"Validation annotation file not found: {val_json}")
                return

            # Validate COCO JSON files
            try:
                with open(train_json, 'r') as f:
                    train_data = json.load(f)

                # Check for required fields
                required_fields = ['images', 'annotations', 'categories']
                missing_fields = [field for field in required_fields if field not in train_data]
                if missing_fields:
                    self.training_completed.emit(False, f"Training annotation file missing required fields: {missing_fields}")
                    return

                # Check if annotations have required fields
                for ann in train_data['annotations']:
                    if 'bbox' not in ann:
                        self.training_completed.emit(False, "Some annotations are missing 'bbox' field. Please regenerate the dataset.")
                        return

                self.status_updated.emit(f"Found {len(train_data['images'])} training images and {len(train_data['annotations'])} annotations")
            except Exception as e:
                self.training_completed.emit(False, f"Error validating training annotations: {e}")
                return

            try:
                with open(val_json, 'r') as f:
                    val_data = json.load(f)

                # Check for required fields
                required_fields = ['images', 'annotations', 'categories']
                missing_fields = [field for field in required_fields if field not in val_data]
                if missing_fields:
                    self.training_completed.emit(False, f"Validation annotation file missing required fields: {missing_fields}")
                    return

                self.status_updated.emit(f"Found {len(val_data['images'])} validation images and {len(val_data['annotations'])} annotations")
            except Exception as e:
                self.training_completed.emit(False, f"Error validating validation annotations: {e}")
                return

            # Register datasets
            try:
                register_coco_instances("visionlab_ai_train", {}, train_json, train_images)
                register_coco_instances("visionlab_ai_val", {}, val_json, val_images)
                self.status_updated.emit("Successfully registered datasets")
            except Exception as e:
                self.training_completed.emit(False, f"Error registering datasets: {e}")
                return

            # Create config
            cfg = get_cfg()

            # Set the model architecture based on config
            try:
                from detectron2.model_zoo import model_zoo
            except ImportError:
                # Try to import from local path if not already imported
                import sys
                import os
                detectron2_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'detectron2')
                if os.path.exists(detectron2_path):
                    sys.path.append(os.path.dirname(detectron2_path))
                    from detectron2.model_zoo import model_zoo
                else:
                    raise ImportError("Detectron2 model_zoo not found in local path")

            if self.config["model_type"] == "Faster R-CNN":
                # Load Faster R-CNN config
                cfg.merge_from_file(model_zoo.get_config_file("COCO-Detection/faster_rcnn_R_50_FPN_3x.yaml"))
                cfg.MODEL.WEIGHTS = model_zoo.get_checkpoint_url("COCO-Detection/faster_rcnn_R_50_FPN_3x.yaml")
            elif self.config["model_type"] == "Mask R-CNN":
                # Load Mask R-CNN config
                cfg.merge_from_file(model_zoo.get_config_file("COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml"))
                cfg.MODEL.WEIGHTS = model_zoo.get_checkpoint_url("COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml")

            # Set dataset and training parameters
            cfg.DATASETS.TRAIN = ("visionlab_ai_train",)
            cfg.DATASETS.TEST = ("visionlab_ai_val",)

            # Set training hyperparameters
            cfg.DATALOADER.NUM_WORKERS = self.config["num_workers"]
            cfg.SOLVER.IMS_PER_BATCH = self.config["batch_size"]
            cfg.SOLVER.BASE_LR = self.config["learning_rate"]
            cfg.SOLVER.MAX_ITER = self.config["max_iterations"]
            cfg.SOLVER.STEPS = []  # No learning rate decay

            # Set the number of classes
            metadata = MetadataCatalog.get("visionlab_ai_train")
            with open(train_json, 'r') as f:
                coco_data = json.load(f)
            num_classes = len(coco_data["categories"])
            cfg.MODEL.ROI_HEADS.NUM_CLASSES = num_classes

            # Set output directory
            os.makedirs(self.output_dir, exist_ok=True)
            cfg.OUTPUT_DIR = self.output_dir

            # Create trainer
            trainer = DefaultTrainer(cfg)
            trainer.resume_or_load(resume=False)

            # Start training
            self.status_updated.emit("Training started...")

            # Monitor training progress
            max_iter = cfg.SOLVER.MAX_ITER
            checkpoint_period = cfg.SOLVER.CHECKPOINT_PERIOD

            # Start training in a separate process
            trainer_process = trainer.train()

            # Emit completion signal
            self.training_completed.emit(True, f"Training completed successfully. Model saved to {self.output_dir}")

        except Exception as e:
            logger.error(f"Error during training: {str(e)}")
            self.training_completed.emit(False, f"Error during training: {str(e)}")

    def stop(self):
        """Stop the training process."""
        self.is_running = False


class InferenceWorker(QThread):
    """Worker thread for running inference with Detectron2 models."""

    # Signals
    inference_completed = Signal(bool, str, object)  # success, message, results

    def __init__(self, model_path, image_path, threshold, model_type):
        super().__init__()
        self.model_path = model_path
        self.image_path = image_path
        self.threshold = threshold
        self.model_type = model_type

    def run(self):
        """Run the inference process."""
        try:
            # Import required modules
            import os
            import sys
            import cv2

            # Import detectron2 here to avoid import errors if not installed
            try:
                from detectron2.config import get_cfg
                from detectron2.engine import DefaultPredictor
                from detectron2.utils.visualizer import Visualizer
                from detectron2.data import MetadataCatalog
            except ImportError:
                # Try to import from local path
                detectron2_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'detectron2')
                if os.path.exists(detectron2_path):
                    sys.path.append(os.path.dirname(detectron2_path))
                    from detectron2.config import get_cfg
                    from detectron2.engine import DefaultPredictor
                    from detectron2.utils.visualizer import Visualizer
                    from detectron2.data import MetadataCatalog
                else:
                    raise ImportError("Detectron2 not found in local path")

            # Load image
            image = cv2.imread(self.image_path)
            if image is None:
                self.inference_completed.emit(False, f"Failed to load image: {self.image_path}", None)
                return

            # Create config
            cfg = get_cfg()

            # Set the model architecture based on model type
            try:
                from detectron2.model_zoo import model_zoo
            except ImportError:
                # Try to import from local path if not already imported
                # Note: os and sys are already imported at the beginning of the method
                detectron2_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'detectron2')
                if os.path.exists(detectron2_path):
                    sys.path.append(os.path.dirname(detectron2_path))
                    from detectron2.model_zoo import model_zoo
                else:
                    raise ImportError("Detectron2 model_zoo not found in local path")

            if self.model_type == "Faster R-CNN":
                cfg.merge_from_file(model_zoo.get_config_file("COCO-Detection/faster_rcnn_R_50_FPN_3x.yaml"))
            elif self.model_type == "Mask R-CNN":
                cfg.merge_from_file(model_zoo.get_config_file("COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml"))

            # Load model weights
            cfg.MODEL.WEIGHTS = self.model_path
            cfg.MODEL.ROI_HEADS.SCORE_THRESH_TEST = self.threshold

            # Create predictor
            predictor = DefaultPredictor(cfg)

            # Run inference
            outputs = predictor(image)

            # Process results
            if self.model_type == "Faster R-CNN":
                # Get bounding boxes
                instances = outputs["instances"].to("cpu")
                boxes = instances.pred_boxes.tensor.numpy()
                scores = instances.scores.numpy()
                classes = instances.pred_classes.numpy()

                # Create visualization
                metadata = MetadataCatalog.get(cfg.DATASETS.TRAIN[0])
                visualizer = Visualizer(image[:, :, ::-1], metadata=metadata, scale=1.0)
                vis_output = visualizer.draw_instance_predictions(instances)
                result_image = vis_output.get_image()[:, :, ::-1]

                # Create results dictionary
                results = {
                    "boxes": boxes,
                    "scores": scores,
                    "classes": classes,
                    "visualization": result_image
                }

            elif self.model_type == "Mask R-CNN":
                # Get instance segmentation results
                instances = outputs["instances"].to("cpu")
                boxes = instances.pred_boxes.tensor.numpy()
                scores = instances.scores.numpy()
                classes = instances.pred_classes.numpy()
                masks = instances.pred_masks.numpy()

                # Create visualization
                metadata = MetadataCatalog.get(cfg.DATASETS.TRAIN[0])
                visualizer = Visualizer(image[:, :, ::-1], metadata=metadata, scale=1.0)
                vis_output = visualizer.draw_instance_predictions(instances)
                result_image = vis_output.get_image()[:, :, ::-1]

                # Create results dictionary
                results = {
                    "boxes": boxes,
                    "scores": scores,
                    "classes": classes,
                    "masks": masks,
                    "visualization": result_image
                }

            # Emit completion signal
            self.inference_completed.emit(True, "Inference completed successfully", results)

        except Exception as e:
            logger.error(f"Error during inference: {str(e)}")
            self.inference_completed.emit(False, f"Error during inference: {str(e)}", None)


class Detectron2ModelTrainerDialog(QDialog):
    """Dialog for training and running inference with Detectron2 models."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Detectron2 Model Trainer")
        self.setMinimumWidth(700)
        self.setMinimumHeight(600)

        # Initialize variables
        self.dataset_dir = ""
        self.output_dir = ""
        self.model_path = ""
        self.training_worker = None
        self.inference_worker = None

        # Set up the UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)

        # Create tabs
        self.tab_widget = QTabWidget()

        # Training tab
        self.training_tab = QWidget()
        self.setup_training_tab()
        self.tab_widget.addTab(self.training_tab, "Training")

        # Inference tab
        self.inference_tab = QWidget()
        self.setup_inference_tab()
        self.tab_widget.addTab(self.inference_tab, "Inference")

        main_layout.addWidget(self.tab_widget)

        # Close button
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        main_layout.addWidget(close_button, alignment=Qt.AlignRight)

    def setup_training_tab(self):
        """Set up the training tab."""
        layout = QVBoxLayout(self.training_tab)

        # Dataset selection
        dataset_group = QGroupBox("Dataset")
        dataset_layout = QFormLayout(dataset_group)

        dataset_path_layout = QHBoxLayout()
        self.dataset_path_edit = QLineEdit()
        self.dataset_path_edit.setReadOnly(True)
        self.dataset_path_button = QPushButton("Browse...")
        self.dataset_path_button.clicked.connect(self.browse_dataset_dir)
        dataset_path_layout.addWidget(self.dataset_path_edit)
        dataset_path_layout.addWidget(self.dataset_path_button)
        dataset_layout.addRow("Dataset Directory:", dataset_path_layout)

        layout.addWidget(dataset_group)

        # Model configuration
        model_group = QGroupBox("Model Configuration")
        model_layout = QFormLayout(model_group)

        # Model type
        self.model_type_combo = QComboBox()
        self.model_type_combo.addItems(["Faster R-CNN", "Mask R-CNN"])
        model_layout.addRow("Model Type:", self.model_type_combo)

        # Batch size
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 16)
        self.batch_size_spin.setValue(2)
        model_layout.addRow("Batch Size:", self.batch_size_spin)

        # Learning rate
        self.learning_rate_spin = QDoubleSpinBox()
        self.learning_rate_spin.setRange(0.0001, 0.1)
        self.learning_rate_spin.setValue(0.001)
        self.learning_rate_spin.setSingleStep(0.0001)
        self.learning_rate_spin.setDecimals(6)
        model_layout.addRow("Learning Rate:", self.learning_rate_spin)

        # Max iterations
        self.max_iterations_spin = QSpinBox()
        self.max_iterations_spin.setRange(100, 100000)
        self.max_iterations_spin.setValue(1000)
        self.max_iterations_spin.setSingleStep(100)
        model_layout.addRow("Max Iterations:", self.max_iterations_spin)

        # Number of workers
        self.num_workers_spin = QSpinBox()
        self.num_workers_spin.setRange(0, 8)
        self.num_workers_spin.setValue(2)
        model_layout.addRow("Num Workers:", self.num_workers_spin)

        layout.addWidget(model_group)

        # Output directory
        output_group = QGroupBox("Output")
        output_layout = QFormLayout(output_group)

        output_dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setReadOnly(True)
        self.output_dir_button = QPushButton("Browse...")
        self.output_dir_button.clicked.connect(self.browse_output_dir)
        output_dir_layout.addWidget(self.output_dir_edit)
        output_dir_layout.addWidget(self.output_dir_button)
        output_layout.addRow("Output Directory:", output_dir_layout)

        layout.addWidget(output_group)

        # Progress
        progress_group = QGroupBox("Training Progress")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("Ready")
        progress_layout.addWidget(self.status_label)

        layout.addWidget(progress_group)

        # Training button
        self.train_button = QPushButton("Start Training")
        self.train_button.clicked.connect(self.start_training)
        layout.addWidget(self.train_button)

    def setup_inference_tab(self):
        """Set up the inference tab."""
        layout = QVBoxLayout(self.inference_tab)

        # Model selection
        model_group = QGroupBox("Model")
        model_layout = QFormLayout(model_group)

        model_path_layout = QHBoxLayout()
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setReadOnly(True)
        self.model_path_button = QPushButton("Browse...")
        self.model_path_button.clicked.connect(self.browse_model_path)
        model_path_layout.addWidget(self.model_path_edit)
        model_path_layout.addWidget(self.model_path_button)
        model_layout.addRow("Model Path:", model_path_layout)

        # Model type for inference
        self.inference_model_type_combo = QComboBox()
        self.inference_model_type_combo.addItems(["Faster R-CNN", "Mask R-CNN"])
        model_layout.addRow("Model Type:", self.inference_model_type_combo)

        # Confidence threshold
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.1, 1.0)
        self.threshold_spin.setValue(0.5)
        self.threshold_spin.setSingleStep(0.05)
        model_layout.addRow("Confidence Threshold:", self.threshold_spin)

        layout.addWidget(model_group)

        # Image selection
        image_group = QGroupBox("Image")
        image_layout = QFormLayout(image_group)

        image_path_layout = QHBoxLayout()
        self.image_path_edit = QLineEdit()
        self.image_path_edit.setReadOnly(True)
        self.image_path_button = QPushButton("Browse...")
        self.image_path_button.clicked.connect(self.browse_image_path)
        image_path_layout.addWidget(self.image_path_edit)
        image_path_layout.addWidget(self.image_path_button)
        image_layout.addRow("Image Path:", image_path_layout)

        layout.addWidget(image_group)

        # Results
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)

        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        results_layout.addWidget(self.results_text)

        layout.addWidget(results_group)

        # Run inference button
        self.run_button = QPushButton("Run Inference")
        self.run_button.clicked.connect(self.run_inference)
        layout.addWidget(self.run_button)

    def browse_dataset_dir(self):
        """Browse for dataset directory."""
        dataset_dir = QFileDialog.getExistingDirectory(
            self, "Select Dataset Directory", os.path.expanduser("~")
        )

        if dataset_dir:
            self.dataset_dir = dataset_dir
            self.dataset_path_edit.setText(dataset_dir)

    def browse_output_dir(self):
        """Browse for output directory."""
        output_dir = QFileDialog.getExistingDirectory(
            self, "Select Output Directory", os.path.expanduser("~")
        )

        if output_dir:
            self.output_dir = output_dir
            self.output_dir_edit.setText(output_dir)

    def browse_model_path(self):
        """Browse for model file."""
        model_path, _ = QFileDialog.getOpenFileName(
            self, "Select Model File", os.path.expanduser("~"),
            "Model Files (*.pth);;All Files (*)"
        )

        if model_path:
            self.model_path = model_path
            self.model_path_edit.setText(model_path)

    def browse_image_path(self):
        """Browse for image file."""
        image_path, _ = QFileDialog.getOpenFileName(
            self, "Select Image File", os.path.expanduser("~"),
            "Image Files (*.jpg *.jpeg *.png);;All Files (*)"
        )

        if image_path:
            self.image_path = image_path
            self.image_path_edit.setText(image_path)

    def start_training(self):
        """Start the training process."""
        # Validate inputs
        if not self.dataset_dir:
            QMessageBox.warning(self, "Warning", "Please select a dataset directory")
            return

        if not self.output_dir:
            QMessageBox.warning(self, "Warning", "Please select an output directory")
            return

        # Check if dataset directory has the required structure
        train_dir = os.path.join(self.dataset_dir, "train")
        val_dir = os.path.join(self.dataset_dir, "val")

        if not os.path.exists(train_dir) or not os.path.exists(val_dir):
            QMessageBox.warning(self, "Warning",
                               "Dataset directory must contain 'train' and 'val' subdirectories")
            return

        # Get training configuration
        config = {
            "model_type": self.model_type_combo.currentText(),
            "batch_size": self.batch_size_spin.value(),
            "learning_rate": self.learning_rate_spin.value(),
            "max_iterations": self.max_iterations_spin.value(),
            "num_workers": self.num_workers_spin.value()
        }

        # Create and start the training worker
        self.training_worker = TrainingWorker(config, self.dataset_dir, self.output_dir)
        self.training_worker.progress_updated.connect(self.update_progress)
        self.training_worker.status_updated.connect(self.update_status)
        self.training_worker.training_completed.connect(self.on_training_completed)

        # Update UI
        self.train_button.setEnabled(False)
        self.progress_bar.setValue(0)
        self.status_label.setText("Initializing training...")

        # Start training
        self.training_worker.start()

        # Set up a timer to simulate progress updates
        self.progress_timer = QTimer(self)
        self.progress_timer.timeout.connect(self.simulate_progress)
        self.progress_timer.start(1000)  # Update every second

    def simulate_progress(self):
        """Simulate progress updates during training."""
        current_progress = self.progress_bar.value()
        if current_progress < 99:
            self.progress_bar.setValue(current_progress + 1)

    def update_progress(self, progress):
        """Update the progress bar."""
        self.progress_bar.setValue(progress)

    def update_status(self, status):
        """Update the status label."""
        self.status_label.setText(status)

    def on_training_completed(self, success, message):
        """Handle training completion."""
        # Stop the progress timer
        if hasattr(self, 'progress_timer'):
            self.progress_timer.stop()

        # Update UI
        self.train_button.setEnabled(True)

        if success:
            self.progress_bar.setValue(100)
            QMessageBox.information(self, "Success", message)
        else:
            QMessageBox.critical(self, "Error", message)

    def run_inference(self):
        """Run inference on an image."""
        # Validate inputs
        if not self.model_path:
            QMessageBox.warning(self, "Warning", "Please select a model file")
            return

        if not self.image_path:
            QMessageBox.warning(self, "Warning", "Please select an image file")
            return

        # Get inference configuration
        model_type = self.inference_model_type_combo.currentText()
        threshold = self.threshold_spin.value()

        # Create and start the inference worker
        self.inference_worker = InferenceWorker(self.model_path, self.image_path, threshold, model_type)
        self.inference_worker.inference_completed.connect(self.on_inference_completed)

        # Update UI
        self.run_button.setEnabled(False)
        self.results_text.clear()
        self.results_text.append("Running inference...")

        # Start inference
        self.inference_worker.start()

    def on_inference_completed(self, success, message, results):
        """Handle inference completion."""
        # Update UI
        self.run_button.setEnabled(True)

        if success:
            # Display results
            self.results_text.clear()
            self.results_text.append(message + "\n")

            if results:
                # Display detection results
                num_detections = len(results["scores"])
                self.results_text.append(f"Found {num_detections} objects\n")

                for i in range(num_detections):
                    score = results["scores"][i]
                    class_id = results["classes"][i]
                    box = results["boxes"][i]

                    self.results_text.append(f"Detection {i+1}:")
                    self.results_text.append(f"  Class: {class_id}")
                    self.results_text.append(f"  Score: {score:.4f}")
                    self.results_text.append(f"  Box: [{box[0]:.1f}, {box[1]:.1f}, {box[2]:.1f}, {box[3]:.1f}]")

                    if "masks" in results:
                        mask = results["masks"][i]
                        mask_pixels = np.sum(mask)
                        self.results_text.append(f"  Mask size: {mask_pixels} pixels")

                    self.results_text.append("")

                # Save visualization
                try:
                    import os
                    import cv2
                    from PySide6.QtGui import QPixmap, QImage
                    from PySide6.QtWidgets import QLabel

                    # Save the visualization image
                    vis_path = os.path.join(os.path.dirname(self.image_path),
                                           f"detection_{os.path.basename(self.image_path)}")
                    cv2.imwrite(vis_path, results["visualization"])
                    self.results_text.append(f"Visualization saved to: {vis_path}")

                    # Convert OpenCV image to QPixmap
                    height, width = results["visualization"].shape[:2]
                    bytes_per_line = 3 * width
                    q_img = QImage(results["visualization"].data, width, height,
                                  bytes_per_line, QImage.Format_RGB888)
                    pixmap = QPixmap.fromImage(q_img)

                    # Show in a separate window
                    self.result_window = QLabel()
                    self.result_window.setPixmap(pixmap)
                    self.result_window.setWindowTitle("Detection Result")
                    self.result_window.show()

                except Exception as e:
                    self.results_text.append(f"Error displaying visualization: {str(e)}")
        else:
            self.results_text.clear()
            self.results_text.append(f"Error: {message}")

    def closeEvent(self, event):
        """Handle dialog close event."""
        # Stop any running workers
        if self.training_worker and self.training_worker.isRunning():
            self.training_worker.stop()
            self.training_worker.wait()

        if self.inference_worker and self.inference_worker.isRunning():
            self.inference_worker.terminate()
            self.inference_worker.wait()

        event.accept()
