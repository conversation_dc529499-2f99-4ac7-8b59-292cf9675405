"""
Improved Class Selector Widget for quick class selection in point counting.
This version stays within the application window and can be moved around.
"""

from PySide6.QtWidgets import (QWidget, QHBoxLayout, QPushButton, QLabel,
                              QFrame, QVBoxLayout, QToolButton, QSizePolicy)
from PySide6.QtCore import Qt, Signal, QSize, QPoint
from PySide6.QtGui import QColor, QIcon, QCursor, QMouseEvent

class ClassButton(QPushButton):
    """A custom button for class selection."""

    def __init__(self, class_name, color, index, parent=None):
        super().__init__(parent)
        self.class_name = class_name
        self.color = color
        self.index = index
        self.setup_ui()

    def setup_ui(self):
        """Set up the button UI."""
        # Set button text to class name
        self.setText(self.class_name)

        # Set button color
        r, g, b = self.color.red(), self.color.green(), self.color.blue()

        # Calculate text color based on background brightness
        brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255
        text_color = "white" if brightness < 0.5 else "black"

        # Create slightly darker and lighter versions for effects
        darker_r, darker_g, darker_b = max(r-30, 0), max(g-30, 0), max(b-30, 0)
        lighter_r, lighter_g, lighter_b = min(r+30, 255), min(g+30, 255), min(b+30, 255)

        # Set button style with improved visual effects (removed unsupported box-shadow)
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: rgb({r}, {g}, {b});
                color: {text_color};
                border: 1px solid rgba(0, 0, 0, 120);
                border-radius: 6px;
                padding: 6px 8px;
                font-weight: bold;
                font-size: 11pt;
            }}
            QPushButton:hover {{
                background-color: rgb({lighter_r}, {lighter_g}, {lighter_b});
                border: 1px solid rgba(255, 255, 255, 120);
            }}
            QPushButton:pressed {{
                background-color: rgb({darker_r}, {darker_g}, {darker_b});
                border: 1px solid rgba(0, 0, 0, 180);
                padding: 7px 7px 5px 9px; /* Shift content to simulate press */
            }}
        """)

        # Set fixed size with improved dimensions
        self.setFixedSize(90, 36)

        # Set tooltip
        self.setToolTip(f"Select {self.class_name} (Shortcut: {self.index + 1})")

class ImprovedClassSelector(QWidget):
    """An improved floating widget for quick class selection that stays within the application."""

    class_selected = Signal(int)  # Signal emitted when a class is selected

    def __init__(self, parent=None):
        super().__init__(parent)
        self.classes = []  # List of (class_name, color) tuples
        self.dragging = False
        self.drag_start_position = None
        self.setup_ui()

    def setup_ui(self):
        """Set up the UI components."""
        # Set window flags for floating behavior within the application
        self.setWindowFlags(Qt.Widget)  # Regular widget, not a separate window

        # Main layout
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        self.main_layout.setSpacing(5)

        # Title bar with drag handle
        title_layout = QHBoxLayout()

        # Drag handle with modern styling (removed unsupported cursor property)
        self.drag_handle = QLabel("Classes")
        self.drag_handle.setStyleSheet("""
            color: #e0e0e0;
            font-weight: bold;
            font-size: 11pt;
            padding: 4px 10px;
            background-color: rgba(50, 50, 60, 0.7);
            border-radius: 6px;
            border-left: 3px solid #4a90e2;
        """)
        self.drag_handle.setCursor(Qt.OpenHandCursor)
        self.drag_handle.setToolTip("Click and drag to move")
        title_layout.addWidget(self.drag_handle)

        title_layout.addStretch()

        # Close button with subtle styling
        self.close_button = QToolButton()
        self.close_button.setText("✕")
        self.close_button.setFixedSize(24, 24)
        self.close_button.setStyleSheet("""
            QToolButton {
                background-color: transparent;
                color: #c0c0c0;
                border-radius: 4px;
                font-weight: normal;
                font-size: 14px;
            }
            QToolButton:hover {
                background-color: rgba(80, 80, 90, 0.5);
                color: white;
            }
            QToolButton:pressed {
                background-color: rgba(60, 60, 70, 0.7);
                color: #e0e0e0;
            }
        """)
        self.close_button.clicked.connect(self.hide)
        title_layout.addWidget(self.close_button)

        self.main_layout.addLayout(title_layout)

        # Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("background-color: #555555;")
        self.main_layout.addWidget(separator)

        # Container for class buttons
        self.buttons_container = QWidget()
        self.buttons_layout = QHBoxLayout(self.buttons_container)
        self.buttons_layout.setContentsMargins(0, 0, 0, 0)
        self.buttons_layout.setSpacing(5)

        self.main_layout.addWidget(self.buttons_container)

        # Set widget style with improved visual appearance (removed unsupported box-shadow)
        self.setStyleSheet("""
            ImprovedClassSelector {
                background-color: rgba(40, 40, 40, 230);
                border-radius: 12px;
                border: 1px solid rgba(80, 80, 80, 180);
            }
        """)

        # Set size policy
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Minimum)

        # Enable mouse tracking for drag and drop
        self.setMouseTracking(True)

    def update_classes(self, classes):
        """Update the class buttons.

        Args:
            classes: List of (class_name, color) tuples
        """
        self.classes = classes

        # Clear existing buttons
        while self.buttons_layout.count():
            item = self.buttons_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

        # Add new buttons
        for i, (class_name, color) in enumerate(classes):
            button = ClassButton(class_name, color, i)
            button.clicked.connect(lambda checked=False, idx=i: self.class_selected.emit(idx))
            self.buttons_layout.addWidget(button)

        # Adjust size based on number of buttons
        self.adjustSize()

    def mousePressEvent(self, event):
        """Handle mouse press events for dragging."""
        if event.button() == Qt.LeftButton and self.drag_handle.geometry().contains(event.pos()):
            self.dragging = True
            self.drag_start_position = event.pos()
            self.drag_handle.setCursor(Qt.ClosedHandCursor)
            event.accept()
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move events for dragging."""
        if self.dragging and (event.buttons() & Qt.LeftButton):
            # Calculate the new position
            delta = event.pos() - self.drag_start_position
            new_pos = self.pos() + delta

            # Ensure the widget stays within the parent widget
            if self.parent():
                parent_rect = self.parent().rect()
                new_pos.setX(max(0, min(new_pos.x(), parent_rect.width() - self.width())))
                new_pos.setY(max(0, min(new_pos.y(), parent_rect.height() - self.height())))

            # Move the widget
            self.move(new_pos)
            event.accept()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """Handle mouse release events for dragging."""
        if event.button() == Qt.LeftButton and self.dragging:
            self.dragging = False
            self.drag_handle.setCursor(Qt.OpenHandCursor)
            event.accept()
        else:
            super().mouseReleaseEvent(event)

    def sizeHint(self):
        """Suggest a size for the widget."""
        return QSize(len(self.classes) * 85 + 20, 80)
