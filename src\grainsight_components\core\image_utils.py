# core/image_utils.py

import logging
import cv2
import numpy as np
import torch
from PIL import Image, ImageDraw, ImageFont # Keep PIL imports if returning PIL image
from typing import List, Tuple, Union, Optional

logger = logging.getLogger(__name__)

def create_segmented_visualization(
    base_image: Image.Image,
    annotations: Union[List[torch.Tensor], torch.Tensor],
    contour_thickness: int = 1,
    contour_color: Tuple[int, int, int] = (255, 255, 0) # Yellow contours default
    ) -> Optional[Image.Image]:
    # Try to free up GPU memory before processing
    if torch.cuda.is_available():
        try:
            torch.cuda.empty_cache()
            logger.info("Cleared CUDA cache before visualization")
        except Exception as e:
            logger.warning(f"Failed to clear CUDA cache: {e}")
    """
    Creates a visual representation with contours drawn on the base image.

    Args:
        base_image (PIL.Image): The original base image.
        annotations (Union[List[torch.Tensor], torch.Tensor]):
            List or Tensor of mask tensors (binary, H, W, uint8 or bool).
            Expected to be on CPU or GPU, will be moved to CPU.
        contour_thickness (int): Thickness of the contour lines.
        contour_color (tuple): RGB tuple for the contour color (e.g., (255, 255, 0)).

    Returns:
        Optional[PIL.Image.Image]: The base image with contours drawn on top (RGBA format).
                                   Returns None if base_image is None or annotations are empty/invalid.
                                   Returns the original image (converted to RGBA) if no valid contours found.
    """
    if base_image is None:
         logger.error("Base image is None, cannot create visualization.")
         return None

    try:
        # Start with the original image converted to RGBA for compositing
        vis_image_rgba = base_image.copy().convert("RGBA")
    except Exception as e:
         logger.error(f"Failed to convert base image to RGBA: {e}", exc_info=True)
         return None

    # Determine number of annotations
    if isinstance(annotations, torch.Tensor):
        num_annotations = annotations.shape[0]
    elif isinstance(annotations, list):
        num_annotations = len(annotations)
    else:
        num_annotations = 0

    if num_annotations == 0:
        logger.info("No annotations provided, returning base image (RGBA).")
        return vis_image_rgba # Return copy in RGBA format

    # Create a transparent overlay to draw contours onto
    overlay = Image.new('RGBA', vis_image_rgba.size, (0, 0, 0, 0)) # Fully transparent
    draw = ImageDraw.Draw(overlay)

    logger.info(f"Creating contour visualization for {num_annotations} annotations.")

    # Define contour color with full alpha
    contour_color_rgba = contour_color + (255,) # Ensure full opacity for contours
    contours_drawn = 0

    for i in range(num_annotations):
        try:
            # Get mask tensor
            if isinstance(annotations, torch.Tensor):
                mask_tensor = annotations[i]
            else:
                mask_tensor = annotations[i]

            if mask_tensor is None: continue # Skip if None

            # --- Ensure mask is numpy uint8 on CPU ---
            if not isinstance(mask_tensor, torch.Tensor) or mask_tensor.ndim != 2:
                 logger.warning(f"Skipping visualization for invalid annotation {i} (not 2D tensor).")
                 continue

            try:
                # Handle boolean or float tensors
                if mask_tensor.dtype == torch.bool:
                    try:
                        # Try direct conversion first
                        mask_np = mask_tensor.cpu().numpy().astype(np.uint8)
                    except RuntimeError as e:
                        # Handle CUDA out-of-memory errors
                        if "CUDA error: out of memory" in str(e):
                            logger.warning(f"CUDA out of memory when converting tensor {i} for visualization. Using fallback method.")
                            # Try memory-efficient conversion
                            if mask_tensor.is_cuda:
                                try:
                                    # Create empty numpy array of the same shape
                                    mask_np = np.zeros(mask_tensor.shape, dtype=np.uint8)

                                    # Process in rows to reduce memory usage
                                    chunk_size = 10  # Process 10 rows at a time
                                    height = mask_tensor.shape[0]

                                    for row in range(0, height, chunk_size):
                                        end_row = min(row + chunk_size, height)
                                        # Process a small chunk at a time
                                        chunk = mask_tensor[row:end_row].cpu().numpy()
                                        mask_np[row:end_row] = chunk.astype(np.uint8)

                                    logger.info(f"Successfully converted tensor {i} using chunked approach for visualization.")
                                except Exception as chunk_error:
                                    logger.error(f"Chunked conversion failed for tensor {i}: {chunk_error}")
                                    continue
                            else:
                                # If not on CUDA but still OOM, try with clone and detach
                                try:
                                    detached_tensor = mask_tensor.clone().detach()
                                    mask_np = detached_tensor.numpy().astype(np.uint8)
                                    del detached_tensor  # Free up memory
                                except Exception as detach_error:
                                    logger.error(f"Detach method failed for tensor {i}: {detach_error}")
                                    continue
                        else:
                            # If not a CUDA memory error, re-raise
                            raise
                elif mask_tensor.dtype.is_floating_point:
                    try:
                        # Try direct conversion first
                        mask_np = (mask_tensor.cpu().numpy() > 0.5).astype(np.uint8)
                    except RuntimeError as e:
                        # Handle CUDA out-of-memory errors
                        if "CUDA error: out of memory" in str(e):
                            logger.warning(f"CUDA out of memory when converting float tensor {i} for visualization. Using fallback method.")
                            # Try memory-efficient conversion
                            if mask_tensor.is_cuda:
                                try:
                                    # Create empty numpy array of the same shape
                                    mask_np = np.zeros(mask_tensor.shape, dtype=np.uint8)

                                    # Process in rows to reduce memory usage
                                    chunk_size = 10  # Process 10 rows at a time
                                    height = mask_tensor.shape[0]

                                    for row in range(0, height, chunk_size):
                                        end_row = min(row + chunk_size, height)
                                        # Process a small chunk at a time
                                        chunk = mask_tensor[row:end_row].cpu().numpy()
                                        mask_np[row:end_row] = (chunk > 0.5).astype(np.uint8)

                                    logger.info(f"Successfully converted float tensor {i} using chunked approach for visualization.")
                                except Exception as chunk_error:
                                    logger.error(f"Chunked conversion failed for float tensor {i}: {chunk_error}")
                                    continue
                            else:
                                # If not on CUDA but still OOM, try with clone and detach
                                try:
                                    detached_tensor = mask_tensor.clone().detach()
                                    mask_np = (detached_tensor.numpy() > 0.5).astype(np.uint8)
                                    del detached_tensor  # Free up memory
                                except Exception as detach_error:
                                    logger.error(f"Detach method failed for float tensor {i}: {detach_error}")
                                    continue
                        else:
                            # If not a CUDA memory error, re-raise
                            raise
                elif mask_tensor.dtype == torch.uint8:
                    try:
                        # Try direct conversion first
                        mask_np = mask_tensor.cpu().numpy()
                    except RuntimeError as e:
                        # Handle CUDA out-of-memory errors
                        if "CUDA error: out of memory" in str(e):
                            logger.warning(f"CUDA out of memory when converting uint8 tensor {i} for visualization. Using fallback method.")
                            # Try memory-efficient conversion
                            if mask_tensor.is_cuda:
                                try:
                                    # Create empty numpy array of the same shape
                                    mask_np = np.zeros(mask_tensor.shape, dtype=np.uint8)

                                    # Process in rows to reduce memory usage
                                    chunk_size = 10  # Process 10 rows at a time
                                    height = mask_tensor.shape[0]

                                    for row in range(0, height, chunk_size):
                                        end_row = min(row + chunk_size, height)
                                        # Process a small chunk at a time
                                        chunk = mask_tensor[row:end_row].cpu().numpy()
                                        mask_np[row:end_row] = chunk

                                    logger.info(f"Successfully converted uint8 tensor {i} using chunked approach for visualization.")
                                except Exception as chunk_error:
                                    logger.error(f"Chunked conversion failed for uint8 tensor {i}: {chunk_error}")
                                    continue
                            else:
                                # If not on CUDA but still OOM, try with clone and detach
                                try:
                                    detached_tensor = mask_tensor.clone().detach()
                                    mask_np = detached_tensor.numpy()
                                    del detached_tensor  # Free up memory
                                except Exception as detach_error:
                                    logger.error(f"Detach method failed for uint8 tensor {i}: {detach_error}")
                                    continue
                        else:
                            # If not a CUDA memory error, re-raise
                            raise
                else:
                    logger.warning(f"Skipping visualization for annotation {i} due to unsupported dtype {mask_tensor.dtype}.")
                    continue
            except Exception as conv_e:
                 logger.error(f"Error converting mask {i} to numpy for visualization: {conv_e}")
                 continue
            # --- End Mask Conversion ---

            if np.sum(mask_np) == 0: # Skip empty masks
                continue

            # Find external contours
            # Use CHAIN_APPROX_SIMPLE for efficiency if exact points aren't crucial for display
            contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if not contours:
                continue

            # Draw the largest contour found for this mask
            contour = max(contours, key=cv2.contourArea)
            # Squeeze removes unnecessary dimensions, flatten creates a list [x1, y1, x2, y2, ...]
            contour_flat = contour.squeeze().flatten().tolist()

            if len(contour_flat) >= 4: # Need at least 2 points (4 coords) for a line
                # Draw line on the TRANSPARENT overlay
                # Add first point to end to close the polygon contour
                 draw.line(contour_flat + contour_flat[:2], fill=contour_color_rgba, width=contour_thickness)
                 contours_drawn += 1

        except Exception as e:
            logger.error(f"Error drawing contour for annotation {i}: {e}", exc_info=True)
            continue # Skip this annotation if drawing fails

    if contours_drawn == 0:
        logger.warning("No valid contours were drawn.")
        # Return the base image converted to RGBA without overlay
        return vis_image_rgba

    # Composite the overlay (containing only contours) onto the original image
    # alpha_composite requires both images to be RGBA
    try:
        final_image = Image.alpha_composite(vis_image_rgba, overlay)
        logger.info(f"Contour visualization created with {contours_drawn} contours.")
        return final_image
    except Exception as comp_e:
        logger.error(f"Failed to composite contour overlay: {comp_e}", exc_info=True)
        # Fallback: return the base image (already converted to RGBA)
        return vis_image_rgba