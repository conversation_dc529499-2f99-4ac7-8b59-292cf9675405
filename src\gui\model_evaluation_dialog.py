import os
import json
import logging
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                              QGroupBox, QFormLayout, QFileDialog, QMessageBox,
                              QTabWidget, QWidget, QScrollArea, QCheckBox, QListWidget,
                              QListWidgetItem, QSplitter, QFrame)
from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap, QImage

logger = logging.getLogger(__name__)

class ModelEvaluationDialog(QDialog):
    """Dialog for evaluating model training metrics."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Model Evaluation")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)

        # Initialize variables
        self.metrics_file = ""
        self.metrics_data = []

        # Set up the UI
        self.setup_ui()

    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)

        # File selection
        file_group = QGroupBox("Metrics File")
        file_layout = QFormLayout(file_group)

        file_path_layout = QHBoxLayout()
        self.file_path_label = QLabel("No file selected")
        self.file_path_label.setWordWrap(True)
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_metrics_file)
        file_path_layout.addWidget(self.file_path_label)
        file_path_layout.addWidget(self.browse_button)
        file_layout.addRow("Metrics File:", file_path_layout)

        main_layout.addWidget(file_group)

        # Create tab widget for different visualizations
        self.tab_widget = QTabWidget()

        # Loss tab
        self.loss_tab = QWidget()
        loss_layout = QVBoxLayout(self.loss_tab)

        # Create a splitter for the loss tab
        loss_splitter = QSplitter(Qt.Horizontal)

        # Left panel for metric selection
        loss_selection_frame = QFrame()
        loss_selection_layout = QVBoxLayout(loss_selection_frame)
        loss_selection_layout.addWidget(QLabel("<b>Select Metrics to Display:</b>"))

        # Create checkboxes for loss metrics
        self.loss_metrics = {
            'total_loss': {'label': 'Total Loss', 'checked': True},
            'loss_cls': {'label': 'Classification Loss', 'checked': True},
            'loss_box_reg': {'label': 'Box Regression Loss', 'checked': True},
            'loss_rpn_cls': {'label': 'RPN Classification Loss', 'checked': True},
            'loss_rpn_loc': {'label': 'RPN Localization Loss', 'checked': True},
            'loss_mask': {'label': 'Mask Loss', 'checked': True}
        }

        for key, data in self.loss_metrics.items():
            checkbox = QCheckBox(data['label'])
            checkbox.setChecked(data['checked'])
            checkbox.stateChanged.connect(self.on_metric_selection_changed)
            self.loss_metrics[key]['checkbox'] = checkbox
            loss_selection_layout.addWidget(checkbox)

        loss_selection_layout.addStretch()
        loss_selection_frame.setMaximumWidth(250)

        # Right panel for the plot
        loss_plot_frame = QFrame()
        loss_plot_layout = QVBoxLayout(loss_plot_frame)

        # Add canvas for the plot
        self.loss_canvas = FigureCanvas(Figure(figsize=(8, 6)))
        loss_plot_layout.addWidget(self.loss_canvas)

        # Add save button
        save_loss_layout = QHBoxLayout()
        save_loss_layout.addStretch()
        self.save_loss_btn = QPushButton("Save Plot")
        self.save_loss_btn.clicked.connect(lambda: self.save_current_plot("loss"))
        save_loss_layout.addWidget(self.save_loss_btn)
        loss_plot_layout.addLayout(save_loss_layout)

        # Add frames to splitter
        loss_splitter.addWidget(loss_selection_frame)
        loss_splitter.addWidget(loss_plot_frame)
        loss_splitter.setStretchFactor(1, 3)  # Make plot area larger

        loss_layout.addWidget(loss_splitter)
        self.tab_widget.addTab(self.loss_tab, "Loss Curves")

        # Accuracy tab
        self.accuracy_tab = QWidget()
        accuracy_layout = QVBoxLayout(self.accuracy_tab)

        # Create a splitter for the accuracy tab
        accuracy_splitter = QSplitter(Qt.Horizontal)

        # Left panel for metric selection
        accuracy_selection_frame = QFrame()
        accuracy_selection_layout = QVBoxLayout(accuracy_selection_frame)
        accuracy_selection_layout.addWidget(QLabel("<b>Select Metrics to Display:</b>"))

        # Create checkboxes for accuracy metrics
        self.accuracy_metrics = {
            'fast_rcnn/cls_accuracy': {'label': 'Classification Accuracy', 'checked': True},
            'fast_rcnn/fg_cls_accuracy': {'label': 'Foreground Classification Accuracy', 'checked': True},
            'fast_rcnn/false_negative': {'label': 'False Negative Rate', 'checked': True},
            'mask_rcnn/accuracy': {'label': 'Mask Accuracy', 'checked': True},
            'mask_rcnn/false_negative': {'label': 'Mask False Negative', 'checked': True},
            'mask_rcnn/false_positive': {'label': 'Mask False Positive', 'checked': True}
        }

        for key, data in self.accuracy_metrics.items():
            checkbox = QCheckBox(data['label'])
            checkbox.setChecked(data['checked'])
            checkbox.stateChanged.connect(self.on_metric_selection_changed)
            self.accuracy_metrics[key]['checkbox'] = checkbox
            accuracy_selection_layout.addWidget(checkbox)

        accuracy_selection_layout.addStretch()
        accuracy_selection_frame.setMaximumWidth(250)

        # Right panel for the plot
        accuracy_plot_frame = QFrame()
        accuracy_plot_layout = QVBoxLayout(accuracy_plot_frame)

        # Add canvas for the plot
        self.accuracy_canvas = FigureCanvas(Figure(figsize=(8, 6)))
        accuracy_plot_layout.addWidget(self.accuracy_canvas)

        # Add save button
        save_accuracy_layout = QHBoxLayout()
        save_accuracy_layout.addStretch()
        self.save_accuracy_btn = QPushButton("Save Plot")
        self.save_accuracy_btn.clicked.connect(lambda: self.save_current_plot("accuracy"))
        save_accuracy_layout.addWidget(self.save_accuracy_btn)
        accuracy_plot_layout.addLayout(save_accuracy_layout)

        # Add frames to splitter
        accuracy_splitter.addWidget(accuracy_selection_frame)
        accuracy_splitter.addWidget(accuracy_plot_frame)
        accuracy_splitter.setStretchFactor(1, 3)  # Make plot area larger

        accuracy_layout.addWidget(accuracy_splitter)
        self.tab_widget.addTab(self.accuracy_tab, "Accuracy Metrics")

        # Learning rate tab (simple, no need for selection)
        self.lr_tab = QWidget()
        lr_layout = QVBoxLayout(self.lr_tab)

        # Add canvas for the plot
        self.lr_canvas = FigureCanvas(Figure(figsize=(8, 6)))
        lr_layout.addWidget(self.lr_canvas)

        # Add save button
        save_lr_layout = QHBoxLayout()
        save_lr_layout.addStretch()
        self.save_lr_btn = QPushButton("Save Plot")
        self.save_lr_btn.clicked.connect(lambda: self.save_current_plot("lr"))
        save_lr_layout.addWidget(self.save_lr_btn)
        lr_layout.addLayout(save_lr_layout)

        self.tab_widget.addTab(self.lr_tab, "Learning Rate")

        # ROI metrics tab
        self.roi_tab = QWidget()
        roi_layout = QVBoxLayout(self.roi_tab)

        # Create a splitter for the ROI tab
        roi_splitter = QSplitter(Qt.Horizontal)

        # Left panel for metric selection
        roi_selection_frame = QFrame()
        roi_selection_layout = QVBoxLayout(roi_selection_frame)
        roi_selection_layout.addWidget(QLabel("<b>Select Metrics to Display:</b>"))

        # Create checkboxes for ROI metrics
        self.roi_metrics = {
            'roi_head/num_bg_samples': {'label': 'Background Samples', 'checked': True},
            'roi_head/num_fg_samples': {'label': 'Foreground Samples', 'checked': True},
            'rpn/num_pos_anchors': {'label': 'Positive Anchors', 'checked': True},
            'rpn/num_neg_anchors': {'label': 'Negative Anchors', 'checked': True}
        }

        for key, data in self.roi_metrics.items():
            checkbox = QCheckBox(data['label'])
            checkbox.setChecked(data['checked'])
            checkbox.stateChanged.connect(self.on_metric_selection_changed)
            self.roi_metrics[key]['checkbox'] = checkbox
            roi_selection_layout.addWidget(checkbox)

        roi_selection_layout.addStretch()
        roi_selection_frame.setMaximumWidth(250)

        # Right panel for the plot
        roi_plot_frame = QFrame()
        roi_plot_layout = QVBoxLayout(roi_plot_frame)

        # Add canvas for the plot
        self.roi_canvas = FigureCanvas(Figure(figsize=(8, 6)))
        roi_plot_layout.addWidget(self.roi_canvas)

        # Add save button
        save_roi_layout = QHBoxLayout()
        save_roi_layout.addStretch()
        self.save_roi_btn = QPushButton("Save Plot")
        self.save_roi_btn.clicked.connect(lambda: self.save_current_plot("roi"))
        save_roi_layout.addWidget(self.save_roi_btn)
        roi_plot_layout.addLayout(save_roi_layout)

        # Add frames to splitter
        roi_splitter.addWidget(roi_selection_frame)
        roi_splitter.addWidget(roi_plot_frame)
        roi_splitter.setStretchFactor(1, 3)  # Make plot area larger

        roi_layout.addWidget(roi_splitter)
        self.tab_widget.addTab(self.roi_tab, "ROI Metrics")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget, stretch=1)

        # Buttons
        buttons_layout = QHBoxLayout()

        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.reject)

        self.analyze_button = QPushButton("Analyze Metrics")
        self.analyze_button.clicked.connect(self.analyze_metrics)
        self.analyze_button.setEnabled(False)

        buttons_layout.addWidget(self.close_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.analyze_button)

        main_layout.addLayout(buttons_layout)

    def browse_metrics_file(self):
        """Browse for metrics file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Metrics File", os.path.expanduser("~"),
            "JSON Files (*.json);;Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            self.metrics_file = file_path
            self.file_path_label.setText(file_path)
            self.analyze_button.setEnabled(True)

    def on_metric_selection_changed(self):
        """Handle metric selection changes."""
        # Get the current tab index
        current_tab = self.tab_widget.currentIndex()

        # Update the appropriate plot based on the current tab
        if current_tab == 0:  # Loss Curves tab
            self.generate_loss_plots()
        elif current_tab == 1:  # Accuracy Metrics tab
            self.generate_accuracy_plots()
        elif current_tab == 3:  # ROI Metrics tab
            self.generate_roi_plots()

    def analyze_metrics(self):
        """Analyze the metrics file and generate visualizations."""
        if not self.metrics_file:
            QMessageBox.warning(self, "Warning", "Please select a metrics file")
            return

        try:
            # Load metrics data
            self.metrics_data = self.load_metrics_file(self.metrics_file)

            if not self.metrics_data:
                QMessageBox.warning(self, "Warning", "No valid metrics data found in the file")
                return

            # Generate visualizations
            self.generate_loss_plots()
            self.generate_accuracy_plots()
            self.generate_lr_plot()
            self.generate_roi_plots()

            # Show a success message
            QMessageBox.information(self, "Success", "Metrics analysis completed successfully")

        except Exception as e:
            logger.error(f"Error analyzing metrics: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error analyzing metrics: {str(e)}")

    def load_metrics_file(self, file_path):
        """Load metrics data from file."""
        metrics_data = []

        try:
            with open(file_path, 'r') as f:
                # Try to parse as JSON Lines (each line is a separate JSON object)
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            metrics_data.append(json.loads(line))
                        except json.JSONDecodeError:
                            logger.warning(f"Failed to parse line as JSON: {line}")

            return metrics_data

        except Exception as e:
            logger.error(f"Error loading metrics file: {str(e)}")
            raise

    def generate_loss_plots(self):
        """Generate loss plots."""
        if not self.metrics_data:
            return

        # Extract data
        iterations = [entry.get('iteration', i) for i, entry in enumerate(self.metrics_data)]

        # Get selected metrics
        selected_metrics = {}
        for key, data in self.loss_metrics.items():
            if hasattr(data.get('checkbox'), 'isChecked') and data['checkbox'].isChecked():
                selected_metrics[key] = [entry.get(key, 0) for entry in self.metrics_data]

        if not selected_metrics:
            # No metrics selected, clear the figure
            fig = self.loss_canvas.figure
            fig.clear()
            self.loss_canvas.draw()
            return

        # Sort data by iteration to ensure proper plotting
        sorted_data = [iterations]
        for values in selected_metrics.values():
            sorted_data.append(values)

        sorted_data = list(zip(*sorted_data))
        sorted_data.sort()  # Sort by iteration

        if not sorted_data:
            return

        # Unpack sorted data
        unpacked_data = list(zip(*sorted_data))
        iterations = unpacked_data[0]

        # Create figure
        fig = self.loss_canvas.figure
        fig.clear()

        # Determine if we need one or multiple plots
        if len(selected_metrics) == 1:
            # Only one metric selected, use a single plot
            ax = fig.add_subplot(1, 1, 1)

            metric_key = list(selected_metrics.keys())[0]
            metric_label = self.loss_metrics[metric_key]['label']
            metric_values = unpacked_data[1]

            ax.plot(iterations, metric_values, 'b-', marker='.', markersize=3, label=metric_label)
            ax.set_title(f'{metric_label} vs. Iteration')
            ax.set_xlabel('Iteration')
            ax.set_ylabel('Loss')
            ax.grid(True)
            ax.legend()
        else:
            # Multiple metrics selected, use two plots
            # First plot for total_loss if selected
            if 'total_loss' in selected_metrics and self.loss_metrics['total_loss']['checkbox'].isChecked():
                ax1 = fig.add_subplot(2, 1, 1)
                total_loss_idx = list(selected_metrics.keys()).index('total_loss') + 1
                ax1.plot(iterations, unpacked_data[total_loss_idx], 'b-', marker='.', markersize=3, label='Total Loss')
                ax1.set_title('Total Loss vs. Iteration')
                ax1.set_xlabel('Iteration')
                ax1.set_ylabel('Loss')
                ax1.grid(True)
                ax1.legend()

                # Second plot for component losses
                ax2 = fig.add_subplot(2, 1, 2)
                colors = ['r-', 'g-', 'c-', 'm-', 'y-']
                color_idx = 0

                for key, data in selected_metrics.items():
                    if key != 'total_loss':
                        metric_idx = list(selected_metrics.keys()).index(key) + 1
                        ax2.plot(iterations, unpacked_data[metric_idx], colors[color_idx % len(colors)],
                                marker='.', markersize=3, label=self.loss_metrics[key]['label'])
                        color_idx += 1

                ax2.set_title('Component Losses vs. Iteration')
                ax2.set_xlabel('Iteration')
                ax2.set_ylabel('Loss')
                ax2.grid(True)
                ax2.legend()
            else:
                # No total_loss, just plot all selected metrics in one plot
                ax = fig.add_subplot(1, 1, 1)
                colors = ['b-', 'r-', 'g-', 'c-', 'm-', 'y-']
                color_idx = 0

                for key, data in selected_metrics.items():
                    metric_idx = list(selected_metrics.keys()).index(key) + 1
                    ax.plot(iterations, unpacked_data[metric_idx], colors[color_idx % len(colors)],
                            marker='.', markersize=3, label=self.loss_metrics[key]['label'])
                    color_idx += 1

                ax.set_title('Loss Metrics vs. Iteration')
                ax.set_xlabel('Iteration')
                ax.set_ylabel('Loss')
                ax.grid(True)
                ax.legend()

        fig.tight_layout()
        self.loss_canvas.draw()

    def generate_accuracy_plots(self):
        """Generate accuracy plots."""
        if not self.metrics_data:
            return

        # Extract data
        iterations = [entry.get('iteration', i) for i, entry in enumerate(self.metrics_data)]

        # Get selected metrics
        selected_metrics = {}
        for key, data in self.accuracy_metrics.items():
            if hasattr(data.get('checkbox'), 'isChecked') and data['checkbox'].isChecked():
                selected_metrics[key] = [entry.get(key, 0) for entry in self.metrics_data]

        if not selected_metrics:
            # No metrics selected, clear the figure
            fig = self.accuracy_canvas.figure
            fig.clear()
            self.accuracy_canvas.draw()
            return

        # Sort data by iteration to ensure proper plotting
        sorted_data = [iterations]
        for values in selected_metrics.values():
            sorted_data.append(values)

        sorted_data = list(zip(*sorted_data))
        sorted_data.sort()  # Sort by iteration

        if not sorted_data:
            return

        # Unpack sorted data
        unpacked_data = list(zip(*sorted_data))
        iterations = unpacked_data[0]

        # Create figure
        fig = self.accuracy_canvas.figure
        fig.clear()

        # Group metrics by type (accuracy vs false rates)
        accuracy_metrics = {}
        false_rate_metrics = {}

        for i, key in enumerate(selected_metrics.keys()):
            if 'false' in key.lower():
                false_rate_metrics[key] = unpacked_data[i+1]
            else:
                accuracy_metrics[key] = unpacked_data[i+1]

        # Determine plot layout based on selected metrics
        if len(selected_metrics) == 1:
            # Only one metric selected, use a single plot
            ax = fig.add_subplot(1, 1, 1)

            metric_key = list(selected_metrics.keys())[0]
            metric_label = self.accuracy_metrics[metric_key]['label']
            metric_values = unpacked_data[1]

            ax.plot(iterations, metric_values, 'b-', marker='.', markersize=3, label=metric_label)
            ax.set_title(f'{metric_label} vs. Iteration')
            ax.set_xlabel('Iteration')
            ax.set_ylabel('Value')
            ax.set_ylim(0, 1.1)
            ax.grid(True)
            ax.legend()
        else:
            # Multiple metrics selected
            if accuracy_metrics and false_rate_metrics:
                # We have both accuracy and false rate metrics, use two subplots
                # Accuracy metrics subplot
                ax1 = fig.add_subplot(2, 1, 1)
                colors = ['b-', 'g-', 'c-', 'm-', 'y-']
                color_idx = 0

                for key, values in accuracy_metrics.items():
                    ax1.plot(iterations, values, colors[color_idx % len(colors)],
                            marker='.', markersize=3, label=self.accuracy_metrics[key]['label'])
                    color_idx += 1

                ax1.set_title('Accuracy Metrics vs. Iteration')
                ax1.set_xlabel('Iteration')
                ax1.set_ylabel('Accuracy')
                ax1.set_ylim(0, 1.1)
                ax1.grid(True)
                ax1.legend()

                # False rate metrics subplot
                ax2 = fig.add_subplot(2, 1, 2)
                color_idx = 0

                for key, values in false_rate_metrics.items():
                    ax2.plot(iterations, values, colors[color_idx % len(colors)],
                            marker='.', markersize=3, label=self.accuracy_metrics[key]['label'])
                    color_idx += 1

                ax2.set_title('False Rate Metrics vs. Iteration')
                ax2.set_xlabel('Iteration')
                ax2.set_ylabel('Rate')
                ax2.set_ylim(0, 1.1)
                ax2.grid(True)
                ax2.legend()
            else:
                # Only one type of metric, use a single plot
                ax = fig.add_subplot(1, 1, 1)
                colors = ['b-', 'g-', 'r-', 'c-', 'm-', 'y-']
                color_idx = 0

                for i, key in enumerate(selected_metrics.keys()):
                    ax.plot(iterations, unpacked_data[i+1], colors[color_idx % len(colors)],
                            marker='.', markersize=3, label=self.accuracy_metrics[key]['label'])
                    color_idx += 1

                title = 'Accuracy Metrics vs. Iteration' if accuracy_metrics else 'False Rate Metrics vs. Iteration'
                ylabel = 'Accuracy' if accuracy_metrics else 'Rate'

                ax.set_title(title)
                ax.set_xlabel('Iteration')
                ax.set_ylabel(ylabel)
                ax.set_ylim(0, 1.1)
                ax.grid(True)
                ax.legend()

        fig.tight_layout()
        self.accuracy_canvas.draw()

    def generate_lr_plot(self):
        """Generate learning rate plot."""
        if not self.metrics_data:
            return

        # Extract data
        iterations = [entry.get('iteration', i) for i, entry in enumerate(self.metrics_data)]
        learning_rates = [entry.get('lr', 0) for entry in self.metrics_data]

        # Sort data by iteration to ensure proper plotting
        sorted_data = sorted(zip(iterations, learning_rates))
        if sorted_data:
            iterations, learning_rates = zip(*sorted_data)

        # Create figure
        fig = self.lr_canvas.figure
        fig.clear()

        ax = fig.add_subplot(1, 1, 1)
        ax.plot(iterations, learning_rates, 'b-', marker='.', markersize=3, label='Learning Rate')
        ax.set_title('Learning Rate vs. Iteration')
        ax.set_xlabel('Iteration')
        ax.set_ylabel('Learning Rate')
        ax.grid(True)
        ax.legend()

        fig.tight_layout()
        self.lr_canvas.draw()

    def generate_roi_plots(self):
        """Generate ROI-related plots."""
        if not self.metrics_data:
            return

        # Extract data
        iterations = [entry.get('iteration', i) for i, entry in enumerate(self.metrics_data)]

        # Get selected metrics
        selected_metrics = {}
        for key, data in self.roi_metrics.items():
            if hasattr(data.get('checkbox'), 'isChecked') and data['checkbox'].isChecked():
                selected_metrics[key] = [entry.get(key, 0) for entry in self.metrics_data]

        if not selected_metrics:
            # No metrics selected, clear the figure
            fig = self.roi_canvas.figure
            fig.clear()
            self.roi_canvas.draw()
            return

        # Sort data by iteration to ensure proper plotting
        sorted_data = [iterations]
        for values in selected_metrics.values():
            sorted_data.append(values)

        sorted_data = list(zip(*sorted_data))
        sorted_data.sort()  # Sort by iteration

        if not sorted_data:
            return

        # Unpack sorted data
        unpacked_data = list(zip(*sorted_data))
        iterations = unpacked_data[0]

        # Create figure
        fig = self.roi_canvas.figure
        fig.clear()

        # Group metrics by type (ROI samples vs RPN anchors)
        roi_sample_metrics = {}
        rpn_anchor_metrics = {}

        for i, key in enumerate(selected_metrics.keys()):
            if 'roi_head' in key:
                roi_sample_metrics[key] = unpacked_data[i+1]
            elif 'rpn' in key:
                rpn_anchor_metrics[key] = unpacked_data[i+1]

        # Determine plot layout based on selected metrics
        if len(selected_metrics) == 1:
            # Only one metric selected, use a single plot
            ax = fig.add_subplot(1, 1, 1)

            metric_key = list(selected_metrics.keys())[0]
            metric_label = self.roi_metrics[metric_key]['label']
            metric_values = unpacked_data[1]

            ax.plot(iterations, metric_values, 'b-', marker='.', markersize=3, label=metric_label)
            ax.set_title(f'{metric_label} vs. Iteration')
            ax.set_xlabel('Iteration')
            ax.set_ylabel('Count')
            ax.grid(True)
            ax.legend()
        else:
            # Multiple metrics selected
            if roi_sample_metrics and rpn_anchor_metrics:
                # We have both ROI samples and RPN anchors, use two subplots
                # ROI samples subplot
                ax1 = fig.add_subplot(2, 1, 1)
                colors = ['b-', 'r-', 'g-', 'c-']
                color_idx = 0

                for key, values in roi_sample_metrics.items():
                    ax1.plot(iterations, values, colors[color_idx % len(colors)],
                            marker='.', markersize=3, label=self.roi_metrics[key]['label'])
                    color_idx += 1

                ax1.set_title('ROI Samples vs. Iteration')
                ax1.set_xlabel('Iteration')
                ax1.set_ylabel('Number of Samples')
                ax1.grid(True)
                ax1.legend()

                # RPN anchors subplot
                ax2 = fig.add_subplot(2, 1, 2)
                color_idx = 0

                for key, values in rpn_anchor_metrics.items():
                    ax2.plot(iterations, values, colors[color_idx % len(colors)],
                            marker='.', markersize=3, label=self.roi_metrics[key]['label'])
                    color_idx += 1

                ax2.set_title('RPN Anchors vs. Iteration')
                ax2.set_xlabel('Iteration')
                ax2.set_ylabel('Number of Anchors')
                ax2.grid(True)
                ax2.legend()
            else:
                # Only one type of metric, use a single plot
                ax = fig.add_subplot(1, 1, 1)
                colors = ['b-', 'r-', 'g-', 'c-', 'm-', 'y-']
                color_idx = 0

                for i, key in enumerate(selected_metrics.keys()):
                    ax.plot(iterations, unpacked_data[i+1], colors[color_idx % len(colors)],
                            marker='.', markersize=3, label=self.roi_metrics[key]['label'])
                    color_idx += 1

                title = 'ROI Samples vs. Iteration' if roi_sample_metrics else 'RPN Anchors vs. Iteration'
                ylabel = 'Number of Samples' if roi_sample_metrics else 'Number of Anchors'

                ax.set_title(title)
                ax.set_xlabel('Iteration')
                ax.set_ylabel(ylabel)
                ax.grid(True)
                ax.legend()

        fig.tight_layout()
        self.roi_canvas.draw()

    def save_current_plot(self, plot_type):
        """Save the current plot to a file.

        Args:
            plot_type (str): Type of plot to save ('loss', 'accuracy', 'lr', or 'roi')
        """
        if not self.metrics_data:
            QMessageBox.warning(self, "Warning", "No data to save. Please analyze metrics first.")
            return

        # Determine which canvas to save based on plot_type
        canvas_map = {
            'loss': self.loss_canvas,
            'accuracy': self.accuracy_canvas,
            'lr': self.lr_canvas,
            'roi': self.roi_canvas
        }

        if plot_type not in canvas_map:
            QMessageBox.warning(self, "Warning", f"Unknown plot type: {plot_type}")
            return

        canvas = canvas_map[plot_type]

        # Get file name from user
        default_name = f"model_evaluation_{plot_type}.png"
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Plot", os.path.join(os.path.expanduser("~"), default_name),
            "PNG Files (*.png);;JPEG Files (*.jpg);;PDF Files (*.pdf);;SVG Files (*.svg);;All Files (*)"
        )

        if not file_path:
            return  # User cancelled

        try:
            # Save the figure
            canvas.figure.savefig(file_path, dpi=300, bbox_inches='tight')
            QMessageBox.information(self, "Success", f"Plot saved to {file_path}")
            logger.info(f"Saved {plot_type} plot to {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error saving plot: {str(e)}")
            logger.error(f"Error saving plot: {str(e)}")
