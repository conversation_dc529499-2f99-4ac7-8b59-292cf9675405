"""
Patch scipy.stats._distn_infrastructure to fix the 'obj' not defined error
"""
import os
import sys
import importlib.util
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_module_path(module_name):
    """Find the file path of a module."""
    try:
        spec = importlib.util.find_spec(module_name)
        if spec is None:
            return None
        return spec.origin
    except (ImportError, AttributeError):
        return None

def patch_scipy_stats():
    """Patch scipy.stats._distn_infrastructure to fix the 'obj' not defined error."""
    try:
        # Find the module path
        module_path = find_module_path('scipy.stats._distn_infrastructure')
        if not module_path:
            logger.error("Could not find scipy.stats._distn_infrastructure module")
            return False
        
        logger.info(f"Found module at: {module_path}")
        
        # Read the file
        with open(module_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if the problematic code exists
        if 'for obj in [s for s in dir() if s.startswith(\'_doc_\')]:' in content and 'del obj' in content:
            # Replace the problematic code
            patched_content = content.replace(
                'for obj in [s for s in dir() if s.startswith(\'_doc_\')]:\n    exec(\'del \' + obj)\ndel obj',
                'for obj in [s for s in dir() if s.startswith(\'_doc_\')]:\n    exec(\'del \' + obj)\n# Safely handle obj variable\nif \'obj\' in locals():\n    del obj'
            )
            
            # Write the patched file
            with open(module_path, 'w', encoding='utf-8') as f:
                f.write(patched_content)
            
            logger.info(f"Successfully patched {module_path}")
            return True
        else:
            logger.info("The problematic code was not found or has already been patched")
            return False
    
    except Exception as e:
        logger.error(f"Error patching scipy: {e}")
        return False

if __name__ == "__main__":
    success = patch_scipy_stats()
    sys.exit(0 if success else 1)
