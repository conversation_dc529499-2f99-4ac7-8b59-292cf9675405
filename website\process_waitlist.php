<?php
// Enable error reporting for development (disable in production)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Database configuration
$db_config = [
    'host' => 'localhost',
    'dbname' => 'visionlab_waitlist',
    'username' => 'your_db_username',
    'password' => 'your_db_password'
];

// Email configuration
$email_config = [
    'smtp_host' => 'smtp.hostinger.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'your_email_password',
    'from_email' => '<EMAIL>',
    'from_name' => 'VisionLab AI Team'
];

try {
    // Validate and sanitize input
    $firstName = trim($_POST['firstName'] ?? '');
    $lastName = trim($_POST['lastName'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $organization = trim($_POST['organization'] ?? '');
    $useCase = trim($_POST['useCase'] ?? '');
    
    // Validation
    $errors = [];
    
    if (empty($firstName) || strlen($firstName) < 2) {
        $errors[] = 'First name must be at least 2 characters long';
    }
    
    if (empty($lastName) || strlen($lastName) < 2) {
        $errors[] = 'Last name must be at least 2 characters long';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address';
    }
    
    if (empty($useCase)) {
        $errors[] = 'Please select your primary use case';
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
        exit;
    }
    
    // Connect to database
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset=utf8mb4",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    // Check if email already exists
    $stmt = $pdo->prepare('SELECT id FROM waitlist WHERE email = ?');
    $stmt->execute([$email]);
    
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'This email is already registered on our waitlist']);
        exit;
    }
    
    // Generate unique token for email verification
    $token = bin2hex(random_bytes(32));
    
    // Insert into database
    $stmt = $pdo->prepare('
        INSERT INTO waitlist (first_name, last_name, email, organization, use_case, token, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, NOW())
    ');
    
    $stmt->execute([$firstName, $lastName, $email, $organization, $useCase, $token]);
    $waitlistId = $pdo->lastInsertId();
    
    // Send confirmation email
    $emailSent = sendConfirmationEmail($email, $firstName, $token, $email_config);
    
    if ($emailSent) {
        echo json_encode([
            'success' => true, 
            'message' => 'Successfully joined waitlist! Check your email for confirmation.',
            'id' => $waitlistId
        ]);
    } else {
        echo json_encode([
            'success' => true, 
            'message' => 'Successfully joined waitlist! However, there was an issue sending the confirmation email.',
            'id' => $waitlistId
        ]);
    }
    
} catch (PDOException $e) {
    error_log('Database error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred']);
} catch (Exception $e) {
    error_log('General error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred. Please try again.']);
}

function sendConfirmationEmail($email, $firstName, $token, $config) {
    try {
        // Use PHPMailer for better email handling
        require_once 'vendor/autoload.php';
        
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = $config['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $config['smtp_username'];
        $mail->Password = $config['smtp_password'];
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $config['smtp_port'];
        
        // Recipients
        $mail->setFrom($config['from_email'], $config['from_name']);
        $mail->addAddress($email, $firstName);
        $mail->addReplyTo($config['from_email'], $config['from_name']);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Welcome to VisionLab AI Waitlist!';
        
        $confirmUrl = "https://yourdomain.com/confirm.php?token=" . $token;
        
        $mail->Body = getEmailTemplate($firstName, $confirmUrl);
        $mail->AltBody = getEmailTextVersion($firstName, $confirmUrl);
        
        $mail->send();
        return true;
        
    } catch (Exception $e) {
        error_log('Email error: ' . $e->getMessage());
        
        // Fallback to PHP mail() function
        return sendSimpleEmail($email, $firstName, $token);
    }
}

function sendSimpleEmail($email, $firstName, $token) {
    $subject = 'Welcome to VisionLab AI Waitlist!';
    $confirmUrl = "https://yourdomain.com/confirm.php?token=" . $token;
    
    $message = getEmailTextVersion($firstName, $confirmUrl);
    
    $headers = [
        'From: VisionLab AI Team <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'Content-Type: text/html; charset=UTF-8',
        'MIME-Version: 1.0'
    ];
    
    return mail($email, $subject, getEmailTemplate($firstName, $confirmUrl), implode("\r\n", $headers));
}

function getEmailTemplate($firstName, $confirmUrl) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Welcome to VisionLab AI</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #2563eb, #3b82f6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: white; padding: 30px; border: 1px solid #e2e8f0; }
            .footer { background: #f8fafc; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; font-size: 14px; color: #64748b; }
            .btn { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .features { margin: 20px 0; }
            .feature { margin: 10px 0; padding: 10px; background: #f8fafc; border-radius: 6px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🎉 Welcome to VisionLab AI!</h1>
                <p>You're now on the exclusive early access list</p>
            </div>
            
            <div class='content'>
                <h2>Hi {$firstName},</h2>
                
                <p>Thank you for joining the VisionLab AI waitlist! We're thrilled to have you as part of our early access community.</p>
                
                <p>Please confirm your email address to secure your spot:</p>
                
                <div style='text-align: center;'>
                    <a href='{$confirmUrl}' class='btn'>Confirm Email Address</a>
                </div>
                
                <div class='features'>
                    <h3>What to expect:</h3>
                    <div class='feature'>
                        <strong>🚀 Early Access:</strong> Be among the first to try VisionLab AI
                    </div>
                    <div class='feature'>
                        <strong>💰 Special Pricing:</strong> Exclusive launch discounts for waitlist members
                    </div>
                    <div class='feature'>
                        <strong>📧 Updates:</strong> Regular progress updates and feature previews
                    </div>
                    <div class='feature'>
                        <strong>🎯 Priority Support:</strong> Direct line to our development team
                    </div>
                </div>
                
                <p>We're working hard to bring you the most advanced AI-powered image segmentation software. Expected launch: <strong>Q1 2025</strong></p>
                
                <p>Have questions? Simply reply to this email - we'd love to hear from you!</p>
                
                <p>Best regards,<br>The VisionLab AI Team</p>
            </div>
            
            <div class='footer'>
                <p>VisionLab AI - Revolutionary AI-Powered Image Segmentation</p>
                <p>If you didn't sign up for this, you can safely ignore this email.</p>
            </div>
        </div>
    </body>
    </html>
    ";
}

function getEmailTextVersion($firstName, $confirmUrl) {
    return "
Hi {$firstName},

Thank you for joining the VisionLab AI waitlist! We're thrilled to have you as part of our early access community.

Please confirm your email address to secure your spot:
{$confirmUrl}

What to expect:
🚀 Early Access: Be among the first to try VisionLab AI
💰 Special Pricing: Exclusive launch discounts for waitlist members
📧 Updates: Regular progress updates and feature previews
🎯 Priority Support: Direct line to our development team

We're working hard to bring you the most advanced AI-powered image segmentation software. Expected launch: Q1 2025

Have questions? Simply reply to this email - we'd love to hear from you!

Best regards,
The VisionLab AI Team

---
VisionLab AI - Revolutionary AI-Powered Image Segmentation
If you didn't sign up for this, you can safely ignore this email.
    ";
}
?>