# src/gui/crop_dialog.py
from PySide6.QtWidgets import Q<PERSON>ialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt, QRect, QPoint
from PySide6.QtGui import QPixmap, QImage, QPainter, QPen, QMouseEvent
from src.utils.image_utils import convert_cvimage_to_qpixmap, resize_image
import cv2

class InteractiveLabel(QLabel):
    """Interactive QLabel for handling mouse events for cropping."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.begin = QPoint()
        self.end = QPoint()
        self.rect = QRect()
        self.setMouseTracking(True)  # Enable mouse tracking

    def set_pixmap(self, pixmap):
        """Sets the pixmap and resets the cropping rectangle."""
        self.setPixmap(pixmap)
        self.rect = QRect() # Reset rectangle.
        self.begin = QPoint()
        self.end = QPoint()
        self.update()


    def paintEvent(self, event):
        """Draws the cropping rectangle."""
        super().paintEvent(event)
        if not self.rect.isNull():
            qp = QPainter(self)
            pen = QPen(Qt.red, 2, Qt.DashLine)
            qp.setPen(pen)
            qp.drawRect(self.rect)

    def mousePressEvent(self, event: QMouseEvent):
        """Starts drawing the cropping rectangle."""
        if event.button() == Qt.LeftButton:
            self.begin = event.position().toPoint()
            self.end = self.begin
            self.rect = QRect(self.begin, self.end).normalized()
            self.update()
            event.accept() # Accept the event
        else:
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """Updates the cropping rectangle."""
        if event.buttons() & Qt.LeftButton:  # Check if left button is down
            self.end = event.position().toPoint()
            self.rect = QRect(self.begin, self.end).normalized()
            self.update()
            event.accept() #Accept the event
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """Finalizes the cropping rectangle."""
        if event.button() == Qt.LeftButton:
            self.end = event.position().toPoint()
            self.rect = QRect(self.begin, self.end).normalized()
            self.update()
            event.accept()
        else:
            super().mouseReleaseEvent(event)


class CropDialog(QDialog):
    """Dialog for interactive image cropping."""
    def __init__(self, image, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Crop Image")
        self.image = image
        self.cropped_image = None
        self.crop_rect = None
        self.display_size = (600, 450)  # Fixed display size

        layout = QVBoxLayout(self)
        self.label = InteractiveLabel()
        
        # Set fixed size for display
        self.label.setFixedSize(*self.display_size)
        
        # Calculate aspect ratio preserving resize
        h, w = self.image.shape[:2]
        display_w, display_h = self.display_size
        
        # Calculate scaling to maintain aspect ratio
        scale = min(display_w/w, display_h/h)
        self.scaled_size = (int(w * scale), int(h * scale))
        
        # Calculate padding to center the image
        x_pad = (display_w - self.scaled_size[0]) // 2
        y_pad = (display_h - self.scaled_size[1]) // 2
        
        self.image_offset = (x_pad, y_pad)
        
        # Resize image maintaining aspect ratio
        image_resized = resize_image(self.image, self.scaled_size)
        self.pixmap = convert_cvimage_to_qpixmap(image_resized)
        self.label.set_pixmap(self.pixmap)
        layout.addWidget(self.label)

        # Add buttons
        button_layout = QHBoxLayout()
        self.crop_button = QPushButton("Crop")
        self.crop_button.clicked.connect(self.apply_crop)
        button_layout.addWidget(self.crop_button)

        reset_button = QPushButton("Reset")
        reset_button.clicked.connect(self.reset_crop)
        button_layout.addWidget(reset_button)

        cancel_button = QPushButton("Cancel")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

    def apply_crop(self):
        """Applies the crop and closes the dialog."""
        if not self.label.rect.isNull():
            # Get the displayed image dimensions
            pixmap = self.label.pixmap()
            label_width = self.label.width()
            label_height = self.label.height()
            
            # Calculate the actual displayed image size (maintaining aspect ratio)
            img_aspect = self.image.shape[1] / self.image.shape[0]
            label_aspect = label_width / label_height
            
            if img_aspect > label_aspect:
                # Image is scaled by width
                display_width = label_width
                display_height = int(label_width / img_aspect)
                y_offset = (label_height - display_height) // 2
                x_offset = 0
            else:
                # Image is scaled by height
                display_height = label_height
                display_width = int(label_height * img_aspect)
                x_offset = (label_width - display_width) // 2
                y_offset = 0
            
            # Calculate scaling factors
            x_scale = self.image.shape[1] / display_width
            y_scale = self.image.shape[0] / display_height
            
            # Convert selection coordinates to original image coordinates
            crop_x = max(0, int((self.label.rect.x() - x_offset) * x_scale))
            crop_y = max(0, int((self.label.rect.y() - y_offset) * y_scale))
            crop_width = min(int(self.label.rect.width() * x_scale), self.image.shape[1] - crop_x)
            crop_height = min(int(self.label.rect.height() * y_scale), self.image.shape[0] - crop_y)

            # Apply the crop
            self.crop_rect = QRect(crop_x, crop_y, crop_width, crop_height)
            self.cropped_image = self.image[crop_y:crop_y + crop_height, crop_x:crop_x + crop_width]
            self.accept()
        else:
            self.reject()

    def reset_crop(self):
        """Resets the cropping rectangle."""
        self.label.set_pixmap(self.pixmap)

    def get_cropped_image(self):
        """Returns the cropped image."""
        return self.cropped_image