"""
Template manager for Image Lab operations.

This module provides functionality to save, load, and manage operation templates
for the Image Lab page.
"""

import os
import json
import logging
import datetime
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class TemplateManager:
    """Manages operation templates for Image Lab."""

    def __init__(self, state_dir: str = None):
        """Initialize the template manager.

        Args:
            state_dir: Directory to store templates. If None, uses default location.
        """
        # Set up the template directory
        if state_dir:
            # Use the provided state directory (usually from a project)
            self.state_dir = state_dir
            self.is_project_dir = True
        else:
            # Default to the state directory in the application root
            self.state_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                         'state', 'image_lab_templates')
            self.is_project_dir = False

        # Create the directory structure if it doesn't exist
        try:
            os.makedirs(self.state_dir, exist_ok=True)
            logger.info(f"Template manager initialized with directory: {self.state_dir}")
        except Exception as e:
            # If we can't create the directory, fall back to a temporary directory
            import tempfile
            self.state_dir = os.path.join(tempfile.gettempdir(), 'visionlab_ai_templates')

            os.makedirs(self.state_dir, exist_ok=True)
            self.is_project_dir = False
            logger.warning(f"Could not create template directory. Using fallback: {self.state_dir}")
            logger.error(f"Original error: {str(e)}")

        # Load existing templates
        self.templates = self._load_all_templates()

    def _load_all_templates(self) -> Dict[str, Dict[str, Any]]:
        """Load all templates from the template directory.

        Returns:
            Dictionary of templates with template names as keys.
        """
        templates = {}

        try:
            # Get all JSON files in the template directory
            template_files = [f for f in os.listdir(self.state_dir) if f.endswith('.json')]

            for template_file in template_files:
                try:
                    template_path = os.path.join(self.state_dir, template_file)
                    with open(template_path, 'r') as f:
                        template_data = json.load(f)

                    # Get template name from filename (without extension)
                    template_name = os.path.splitext(template_file)[0]
                    templates[template_name] = template_data
                    logger.debug(f"Loaded template: {template_name}")
                except Exception as e:
                    logger.error(f"Error loading template {template_file}: {str(e)}")

            logger.info(f"Loaded {len(templates)} templates from {self.state_dir}")
        except Exception as e:
            logger.error(f"Error loading templates: {str(e)}")

        return templates

    def save_template(self, name: str, operations: List[Dict[str, Any]]) -> bool:
        """Save a template to disk.

        Args:
            name: Name of the template
            operations: List of operations to save

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create template data
            template = {
                'operations': operations,
                'created': str(datetime.datetime.now()),
                'version': '1.0'
            }

            # Sanitize the template name for use as a filename
            safe_name = self._sanitize_filename(name)
            template_path = os.path.join(self.state_dir, f"{safe_name}.json")

            # Save to disk
            with open(template_path, 'w') as f:
                json.dump(template, f, indent=2)

            # Add to in-memory templates
            self.templates[name] = template

            logger.info(f"Saved template '{name}' with {len(operations)} operations")
            return True
        except Exception as e:
            logger.error(f"Error saving template '{name}': {str(e)}")
            return False

    def load_template(self, name: str) -> Optional[Dict[str, Any]]:
        """Load a template by name.

        Args:
            name: Name of the template to load

        Returns:
            Template data dictionary or None if not found
        """
        if name in self.templates:
            return self.templates[name]

        # Try to load from disk if not in memory
        try:
            safe_name = self._sanitize_filename(name)
            template_path = os.path.join(self.state_dir, f"{safe_name}.json")

            if os.path.exists(template_path):
                with open(template_path, 'r') as f:
                    template_data = json.load(f)

                # Cache in memory
                self.templates[name] = template_data
                return template_data
        except Exception as e:
            logger.error(f"Error loading template '{name}': {str(e)}")

        return None

    def delete_template(self, name: str) -> bool:
        """Delete a template.

        Args:
            name: Name of the template to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            # Remove from in-memory cache
            if name in self.templates:
                del self.templates[name]

            # Remove from disk
            safe_name = self._sanitize_filename(name)
            template_path = os.path.join(self.state_dir, f"{safe_name}.json")

            if os.path.exists(template_path):
                os.remove(template_path)
                logger.info(f"Deleted template: {name}")
                return True
            else:
                logger.warning(f"Template file not found for deletion: {name}")
                return False
        except Exception as e:
            logger.error(f"Error deleting template '{name}': {str(e)}")
            return False

    def rename_template(self, old_name: str, new_name: str) -> bool:
        """Rename a template.

        Args:
            old_name: Current name of the template
            new_name: New name for the template

        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if the template exists
            template = self.load_template(old_name)
            if not template:
                logger.warning(f"Template not found for renaming: {old_name}")
                return False

            # Save with new name
            if self.save_template(new_name, template.get('operations', [])):
                # Delete the old template
                self.delete_template(old_name)
                logger.info(f"Renamed template from '{old_name}' to '{new_name}'")
                return True

            return False
        except Exception as e:
            logger.error(f"Error renaming template from '{old_name}' to '{new_name}': {str(e)}")
            return False

    def get_template_names(self) -> List[str]:
        """Get a list of all template names.

        Returns:
            List of template names
        """
        # Refresh templates from disk
        self.templates = self._load_all_templates()
        return sorted(list(self.templates.keys()))

    def _sanitize_filename(self, name: str) -> str:
        """Sanitize a template name for use as a filename.

        Args:
            name: Template name to sanitize

        Returns:
            Sanitized filename
        """
        # Replace invalid filename characters
        invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
        safe_name = name
        for char in invalid_chars:
            safe_name = safe_name.replace(char, '_')

        return safe_name
