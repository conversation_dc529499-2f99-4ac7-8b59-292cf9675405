# src/gui/advanced_segmentation_page.py
import os
import logging
from PySide6.QtWidgets import QWidget, QMessageBox
from PySide6.QtCore import Slot, Signal

from src.gui.ui.advanced_segmentation_page_ui import AdvancedSegmentationPageUI
from src.gui.handlers.advanced_segmentation_page_handler import AdvancedSegmentationPageHandler
from src.utils.session_state import SessionState

logger = logging.getLogger(__name__)

class AdvancedSegmentationPage(QWidget, AdvancedSegmentationPageUI):
    """Advanced Segmentation page for annotating images and training Faster RCNN models."""

    def __init__(self, parent=None, project=None):
        QWidget.__init__(self, parent)
        AdvancedSegmentationPageUI.__init__(self)

        # Set up the UI
        self.setup_advanced_segmentation_page()

        # Set the project
        self._project = project

        # Initialize session state
        self.session_state = SessionState()

        # Set project directory if available
        if project:
            if hasattr(project, 'project_file'):
                # GrainSightProject
                # Pass the project file path directly to set_project_dir
                self.session_state.set_project_dir(project.project_file)
                logger.info(f"Set session state project directory from GrainSightProject: {project.project_file}")
            elif hasattr(project, 'project_dir'):
                # Regular Project
                self.session_state.set_project_dir(project.project_dir)
                logger.info(f"Set session state project directory from Project: {project.project_dir}")

        # Initialize the handler with session state
        self.handler = AdvancedSegmentationPageHandler(self, session_state=self.session_state)

        # Load state if available
        self.handler.load_state()

    def apply_theme(self, theme_name="dark", font_family=None, font_size=None, style_params=None):
        """Apply the current theme to the advanced segmentation page components."""
        # Apply theme to the handler
        if hasattr(self, 'handler'):
            self.handler.apply_theme(theme_name, style_params)

        # Define button style based on theme
        is_dark = theme_name == "dark"
        button_style = f"""
            QPushButton {{
                background-color: {'#2d2d2d' if is_dark else '#e0e0e0'};
                color: {'#ffffff' if is_dark else '#000000'};
                border: 1px solid {'#555555' if is_dark else '#cccccc'};
                border-radius: 4px;
                padding: 5px;
            }}
            QPushButton:hover {{
                background-color: {'#3d3d3d' if is_dark else '#d0d0d0'};
            }}
            QPushButton:pressed {{
                background-color: {'#1d1d1d' if is_dark else '#c0c0c0'};
            }}
            QPushButton:checked {{
                background-color: {'#4d4d4d' if is_dark else '#b0b0b0'};
                border: 2px solid {'#00aaff' if is_dark else '#0078d7'};
            }}
            QPushButton:disabled {{
                background-color: {'#1a1a1a' if is_dark else '#f0f0f0'};
                color: {'#555555' if is_dark else '#a0a0a0'};
                border: 1px solid {'#333333' if is_dark else '#e0e0e0'};
            }}
        """

        # Apply style to buttons
        for btn in [self.polygon_tool_btn, self.rectangle_tool_btn, self.point_prompt_tool_btn,
                   self.negative_point_prompt_tool_btn, self.magic_wand_tool_btn,
                   self.edit_annotation_btn, self.remove_annotation_btn,
                   self.dataset_preparation_btn, self.export_dataset_btn, self.import_dataset_btn,
                   self.model_trainer_btn, self.export_model_btn, self.model_inference_btn,
                   self.refine_mask_sam_btn]:
            btn.setStyleSheet(button_style)

        # Apply style to list widget
        list_style = f"""
            QListWidget {{
                background-color: {'#1e1e1e' if is_dark else '#ffffff'};
                color: {'#ffffff' if is_dark else '#000000'};
                border: 1px solid {'#555555' if is_dark else '#cccccc'};
                border-radius: 4px;
            }}
            QListWidget::item {{
                padding: 5px;
            }}
            QListWidget::item:selected {{
                background-color: {'#0078d7' if is_dark else '#cce8ff'};
                color: {'#ffffff' if is_dark else '#000000'};
            }}
        """
        self.annotations_list.setStyleSheet(list_style)

        # Apply style to tabs
        tab_style = f"""
            QTabWidget::pane {{
                border: 1px solid {'#555555' if is_dark else '#cccccc'};
                background-color: {'#1e1e1e' if is_dark else '#ffffff'};
            }}
            QTabBar::tab {{
                background-color: {'#2d2d2d' if is_dark else '#e0e0e0'};
                color: {'#ffffff' if is_dark else '#000000'};
                border: 1px solid {'#555555' if is_dark else '#cccccc'};
                border-bottom-color: {'#555555' if is_dark else '#cccccc'};
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 8ex;
                padding: 5px;
            }}
            QTabBar::tab:selected {{
                background-color: {'#1e1e1e' if is_dark else '#ffffff'};
                border-bottom-color: {'#1e1e1e' if is_dark else '#ffffff'};
            }}
            QTabBar::tab:!selected {{
                margin-top: 2px;
            }}
        """
        self.adv_seg_tabs.setStyleSheet(tab_style)

    def load_images_from_project_hub(self, image_paths, image_infos):
        """Load images from the project hub."""
        if hasattr(self, 'handler'):
            # First load the state to ensure we have the latest annotations
            if hasattr(self, 'session_state') and self.session_state and hasattr(self.session_state, 'state_dir') and self.session_state.state_dir:
                logger.info(f"Loading state before loading images from {self.session_state.state_dir}")
                self.load_state()

            # Then load the images
            self.handler.load_images_from_project_hub(image_paths, image_infos)

            # Load the state again after loading images
            logger.info("Loading state after loading images")
            self.load_state()



    def save_state(self):
        """Save the current state to the session state."""
        if hasattr(self, 'handler'):
            self.handler.save_state()

    def set_project(self, project):
        """Set the project and update the session state.

        Args:
            project: The project object
        """
        # Update the session state with the project directory
        if project and hasattr(self, 'session_state'):
            if hasattr(project, 'project_file'):
                # GrainSightProject
                self.session_state.set_project_dir(project.project_file)
                logger.info(f"Set session state project directory from GrainSightProject: {project.project_file}")
            elif hasattr(project, 'project_dir'):
                # Regular Project
                self.session_state.set_project_dir(project.project_dir)
                logger.info(f"Set session state project directory from Project: {project.project_dir}")

            # Update the handler's session state reference
            if hasattr(self, 'handler'):
                self.handler.session_state = self.session_state
                logger.info("Updated Advanced Segmentation handler's session state reference")

                # Force reload state after setting project
                self.handler.load_state()
                logger.info("Reloaded state after setting project")

    def load_state(self):
        """Load the state from the session state."""
        if hasattr(self, 'handler'):
            self.handler.load_state()

    def on_new_project(self):
        """Handle new project event by clearing the state."""
        if hasattr(self, 'handler'):
            self.handler.clear_state()
            logger.info("Cleared Advanced Segmentation page state for new project")
