"""
Intelligent patch artifact handling for GrainSight.

This module provides advanced algorithms for detecting and fixing artifacts
that occur at patch boundaries during segmentation of large images.
"""

import logging
import time
import numpy as np
import torch
import cv2
from typing import List, Tuple, Dict, Optional, Union, Any

logger = logging.getLogger(__name__)

class MaskInfo:
    """Lightweight container for mask information during processing."""
    def __init__(self,
                 mask_id: int,
                 patch_id: int,
                 bbox: Tuple[int, int, int, int],  # x1, y1, x2, y2
                 area: float,
                 score: float,
                 offset: Tuple[int, int],  # x_offset, y_offset
                 mask_tensor: torch.Tensor,  # Binary mask tensor
                 is_boundary: bool = False,
                 boundary_score: float = 0.0):
        self.mask_id = mask_id
        self.patch_id = patch_id
        self.bbox = bbox
        self.area = area
        self.score = score
        self.offset = offset
        self.mask_tensor = mask_tensor
        self.is_boundary = is_boundary
        self.boundary_score = boundary_score
        # Additional properties for artifact detection
        self.is_artifact = False
        self.artifact_score = 0.0
        self.neighbors = []  # List of mask_ids that are neighbors
        self.continuations = []  # List of mask_ids that might be continuations across patches

    def __repr__(self):
        return (f"MaskInfo(id={self.mask_id}, patch={self.patch_id}, "
                f"bbox={self.bbox}, area={self.area:.1f}, score={self.score:.2f}, "
                f"boundary={self.is_boundary}, artifact_score={self.artifact_score:.2f})")


def detect_boundary_masks(mask_infos: List[MaskInfo],
                          patch_dimensions: Dict[int, Tuple[int, int, int, int]]) -> List[MaskInfo]:
    """
    Identifies masks that touch patch boundaries and might be artifacts or split objects.

    Args:
        mask_infos: List of MaskInfo objects
        patch_dimensions: Dictionary mapping patch_id to (x_start, y_start, width, height)

    Returns:
        Updated list of MaskInfo objects with boundary information
    """
    for mask in mask_infos:
        patch_id = mask.patch_id
        if patch_id not in patch_dimensions:
            continue

        x_start, y_start, patch_w, patch_h = patch_dimensions[patch_id]

        # Get mask bbox relative to patch
        x1_rel = mask.bbox[0] - x_start
        y1_rel = mask.bbox[1] - y_start
        x2_rel = mask.bbox[2] - x_start
        y2_rel = mask.bbox[3] - y_start

        # Check if mask touches any patch boundary
        touches_left = (x1_rel <= 1)  # Within 1 pixel of left edge
        touches_right = (x2_rel >= patch_w - 1)  # Within 1 pixel of right edge
        touches_top = (y1_rel <= 1)  # Within 1 pixel of top edge
        touches_bottom = (y2_rel >= patch_h - 1)  # Within 1 pixel of bottom edge

        # Mark as boundary mask if it touches any edge
        mask.is_boundary = touches_left or touches_right or touches_top or touches_bottom

        # Calculate boundary score based on how much of the mask touches boundaries
        if mask.is_boundary:
            # Calculate perimeter of mask bbox
            bbox_perimeter = 2 * ((x2_rel - x1_rel) + (y2_rel - y1_rel))

            # Calculate length of boundary contact
            boundary_length = 0
            if touches_left: boundary_length += (y2_rel - y1_rel)
            if touches_right: boundary_length += (y2_rel - y1_rel)
            if touches_top: boundary_length += (x2_rel - x1_rel)
            if touches_bottom: boundary_length += (x2_rel - x1_rel)

            # Boundary score is ratio of boundary contact to perimeter
            mask.boundary_score = min(1.0, boundary_length / bbox_perimeter if bbox_perimeter > 0 else 0)

    return mask_infos


def find_mask_neighbors(mask_infos: List[MaskInfo],
                        iou_threshold: float = 0.1,
                        distance_threshold: int = 10) -> List[MaskInfo]:
    """
    Finds neighboring masks that might be part of the same object across patch boundaries.

    Args:
        mask_infos: List of MaskInfo objects
        iou_threshold: Minimum IoU to consider masks as potentially related
        distance_threshold: Maximum distance between bboxes to consider as neighbors

    Returns:
        Updated list of MaskInfo objects with neighbor information
    """
    # Create a mapping of mask_id to index in the list for quick lookup
    mask_id_to_index = {mask.mask_id: i for i, mask in enumerate(mask_infos)}

    # For each boundary mask, find potential neighbors
    for i, mask in enumerate(mask_infos):
        if not mask.is_boundary:
            continue

        x1, y1, x2, y2 = mask.bbox
        mask_patch = mask.patch_id

        for j, other_mask in enumerate(mask_infos):
            if i == j or other_mask.patch_id == mask_patch:
                continue  # Skip self and masks from same patch

            if not other_mask.is_boundary:
                continue  # Only consider boundary masks as potential neighbors

            ox1, oy1, ox2, oy2 = other_mask.bbox

            # Check if bboxes are close enough
            horizontal_overlap = (x1 <= ox2 and ox1 <= x2)
            vertical_overlap = (y1 <= oy2 and oy1 <= y2)

            if not (horizontal_overlap or vertical_overlap):
                # Check distance between boxes if they don't overlap
                min_distance = min(
                    abs(x1 - ox2), abs(x2 - ox1),  # Horizontal distance
                    abs(y1 - oy2), abs(y2 - oy1)   # Vertical distance
                )
                if min_distance > distance_threshold:
                    continue

            # Calculate IoU if boxes are close enough
            intersection_x1 = max(x1, ox1)
            intersection_y1 = max(y1, oy1)
            intersection_x2 = min(x2, ox2)
            intersection_y2 = min(y2, oy2)

            if intersection_x2 > intersection_x1 and intersection_y2 > intersection_y1:
                intersection_area = (intersection_x2 - intersection_x1) * (intersection_y2 - intersection_y1)
                mask_area = (x2 - x1) * (y2 - y1)
                other_area = (ox2 - ox1) * (oy2 - oy1)
                union_area = mask_area + other_area - intersection_area
                iou = intersection_area / union_area if union_area > 0 else 0

                if iou > iou_threshold:
                    mask.neighbors.append(other_mask.mask_id)
                    other_mask.neighbors.append(mask.mask_id)

    return mask_infos


def detect_patch_sized_masks(mask_infos: List[MaskInfo],
                            patch_dimensions: Dict[int, Tuple[int, int, int, int]],
                            rectangularity_threshold: float = 0.85,
                            area_ratio_threshold: float = 0.7,
                            alignment_tolerance: int = 15,
                            detect_partial_patches: bool = True,
                            detect_straight_edges: bool = True) -> List[int]:
    """
    Detects masks that are approximately the same size and shape as patches or patch boundaries.
    This is particularly useful for filtering out patch boundary detections from MobileSAM.

    Args:
        mask_infos: List of MaskInfo objects
        patch_dimensions: Dictionary mapping patch_id to (x_start, y_start, width, height)
        rectangularity_threshold: Minimum rectangularity (mask area / bounding box area) to consider as rectangular
        area_ratio_threshold: Minimum ratio of mask area to patch area to consider as patch-sized
        alignment_tolerance: Tolerance in pixels for alignment with patch boundaries
        detect_partial_patches: Whether to also detect partial patch boundaries (e.g., L-shaped corners)
        detect_straight_edges: Whether to detect masks with straight edges that might be patch boundaries

    Returns:
        List of indices of masks that are NOT patch-sized (masks to keep)
    """
    logger.info(f"Checking for patch-sized rectangular masks among {len(mask_infos)} masks")

    # Indices of masks to remove (patch-sized rectangles)
    patch_sized_masks = set()

    # First pass: detect full patch-sized rectangles
    for i, mask in enumerate(mask_infos):
        patch_id = mask.patch_id
        if patch_id not in patch_dimensions:
            continue

        # Get patch dimensions
        x_start, y_start, patch_w, patch_h = patch_dimensions[patch_id]
        patch_area = patch_w * patch_h

        # Get mask dimensions
        x1, y1, x2, y2 = mask.bbox
        bbox_width = x2 - x1
        bbox_height = y2 - y1
        bbox_area = bbox_width * bbox_height

        # Skip if mask is much smaller than the patch
        if bbox_area < 0.4 * patch_area:  # More permissive threshold to catch smaller rectangles
            continue

        # Calculate rectangularity (how rectangular the mask is)
        mask_area = mask.area
        rectangularity = mask_area / bbox_area if bbox_area > 0 else 0

        # Calculate how much of the patch area is covered by the mask
        area_ratio = mask_area / patch_area if patch_area > 0 else 0

        # Check if mask is approximately rectangular
        if rectangularity > rectangularity_threshold:
            # Check if the mask is aligned with patch boundaries
            x_aligned = (abs(x1 - x_start) < alignment_tolerance or
                         abs(x2 - (x_start + patch_w)) < alignment_tolerance)
            y_aligned = (abs(y1 - y_start) < alignment_tolerance or
                         abs(y2 - (y_start + patch_h)) < alignment_tolerance)

            # Full patch alignment (both x and y aligned)
            if (x_aligned and y_aligned and area_ratio > area_ratio_threshold):
                logger.info(f"Detected patch-sized rectangular mask: patch {patch_id}, "
                           f"rectangularity={rectangularity:.2f}, area_ratio={area_ratio:.2f}")
                patch_sized_masks.add(i)
                continue

            # Check for partial patch boundaries (e.g., edges or corners)
            if detect_partial_patches:
                # Check if one dimension matches a patch boundary and covers a significant portion
                edge_aligned = False

                # Check for horizontal edge alignment (top or bottom edge of patch)
                if abs(y1 - y_start) < alignment_tolerance or abs(y2 - (y_start + patch_h)) < alignment_tolerance:
                    # Check if width is significant
                    if bbox_width > 0.5 * patch_w and bbox_height < 0.3 * patch_h:
                        edge_aligned = True

                # Check for vertical edge alignment (left or right edge of patch)
                if abs(x1 - x_start) < alignment_tolerance or abs(x2 - (x_start + patch_w)) < alignment_tolerance:
                    # Check if height is significant
                    if bbox_height > 0.5 * patch_h and bbox_width < 0.3 * patch_w:
                        edge_aligned = True

                # Check for corner alignment (L-shaped corners)
                corner_aligned = False
                if (abs(x1 - x_start) < alignment_tolerance and abs(y1 - y_start) < alignment_tolerance) or \
                   (abs(x2 - (x_start + patch_w)) < alignment_tolerance and abs(y1 - y_start) < alignment_tolerance) or \
                   (abs(x1 - x_start) < alignment_tolerance and abs(y2 - (y_start + patch_h)) < alignment_tolerance) or \
                   (abs(x2 - (x_start + patch_w)) < alignment_tolerance and abs(y2 - (y_start + patch_h)) < alignment_tolerance):
                    # Check if size is reasonable for a corner
                    if bbox_width < 0.5 * patch_w and bbox_height < 0.5 * patch_h and bbox_area > 0.05 * patch_area:
                        corner_aligned = True

                if edge_aligned or corner_aligned:
                    logger.info(f"Detected partial patch boundary: patch {patch_id}, "
                               f"edge_aligned={edge_aligned}, corner_aligned={corner_aligned}")
                    patch_sized_masks.add(i)

    # Second pass: detect masks that are aligned with patch boundaries but not necessarily rectangular
    if detect_partial_patches:
        for i, mask in enumerate(mask_infos):
            if i in patch_sized_masks:
                continue  # Skip already identified patch masks

            patch_id = mask.patch_id
            if patch_id not in patch_dimensions:
                continue

            # Get patch dimensions
            x_start, y_start, patch_w, patch_h = patch_dimensions[patch_id]

            # Get mask dimensions
            x1, y1, x2, y2 = mask.bbox

            # Check if mask is aligned with any patch boundary
            boundary_aligned = False

            # Check alignment with patch boundaries
            left_aligned = abs(x1 - x_start) < alignment_tolerance
            right_aligned = abs(x2 - (x_start + patch_w)) < alignment_tolerance
            top_aligned = abs(y1 - y_start) < alignment_tolerance
            bottom_aligned = abs(y2 - (y_start + patch_h)) < alignment_tolerance

            # If aligned with at least two boundaries, it's likely a patch boundary
            if (left_aligned and (top_aligned or bottom_aligned)) or \
               (right_aligned and (top_aligned or bottom_aligned)):
                # Extract mask and analyze shape
                mask_np = mask.mask_tensor.cpu().numpy().astype(np.uint8)
                contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                if contours:
                    # Analyze the largest contour
                    largest_contour = max(contours, key=cv2.contourArea)

                    # Check if contour has straight edges
                    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
                    approx = cv2.approxPolyDP(largest_contour, epsilon, True)

                    # If the approximated contour has few points, it's likely a simple shape like a rectangle
                    if len(approx) < 8:  # Fewer points means more straight edges
                        boundary_aligned = True

            if boundary_aligned:
                logger.info(f"Detected boundary-aligned mask in patch {patch_id}")
                patch_sized_masks.add(i)

    # Third pass: detect masks with straight edges that might be patch boundaries
    if detect_straight_edges:
        for i, mask in enumerate(mask_infos):
            if i in patch_sized_masks:
                continue  # Skip already identified patch masks

            # Skip very small masks
            if mask.area < 500:  # Arbitrary threshold for small masks
                continue

            # Extract mask and analyze shape
            mask_np = mask.mask_tensor.cpu().numpy().astype(np.uint8)
            contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                continue

            # Analyze the largest contour
            largest_contour = max(contours, key=cv2.contourArea)

            # Skip very small contours
            if cv2.contourArea(largest_contour) < 500:
                continue

            # Check if contour has straight edges
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            approx = cv2.approxPolyDP(largest_contour, epsilon, True)

            # If the approximated contour has few points and is large, it's likely a patch boundary
            if len(approx) <= 6:  # Very few points means very straight edges
                # Calculate aspect ratio
                x, y, w, h = cv2.boundingRect(largest_contour)
                aspect_ratio = max(w, h) / (min(w, h) if min(w, h) > 0 else 1)

                # If it's very rectangular (high aspect ratio) and aligned with image axes, it's likely a patch boundary
                if aspect_ratio > 5:  # Very elongated shape
                    # Check if it's aligned with image axes (horizontal or vertical)
                    rect = cv2.minAreaRect(largest_contour)
                    angle = rect[2]
                    # Normalize angle to 0-90 degrees
                    if angle < -45:
                        angle += 90
                    if angle > 45:
                        angle -= 90
                    angle = abs(angle)

                    # If angle is close to 0 or 90 degrees, it's aligned with image axes
                    if angle < 5 or angle > 85:
                        logger.info(f"Detected straight-edged mask with aspect ratio {aspect_ratio:.2f} and angle {angle:.2f}")
                        patch_sized_masks.add(i)

    # Return indices of masks to keep (not patch-sized)
    keep_indices = [i for i in range(len(mask_infos)) if i not in patch_sized_masks]
    logger.info(f"Filtered out {len(patch_sized_masks)} patch-sized/boundary masks, keeping {len(keep_indices)}")
    return keep_indices


def detect_patch_artifacts(mask_infos: List[MaskInfo],
                           patch_dimensions: Dict[int, Tuple[int, int, int, int]],
                           artifact_threshold: float = 0.7) -> List[MaskInfo]:
    """
    Detects likely artifacts at patch boundaries based on shape and position.

    Args:
        mask_infos: List of MaskInfo objects with boundary information
        patch_dimensions: Dictionary mapping patch_id to (x_start, y_start, width, height)
        artifact_threshold: Threshold for classifying a mask as an artifact

    Returns:
        Updated list of MaskInfo objects with artifact information
    """
    for mask in mask_infos:
        if not mask.is_boundary:
            continue

        # Get patch dimensions
        patch_id = mask.patch_id
        if patch_id not in patch_dimensions:
            continue

        x_start, y_start, patch_w, patch_h = patch_dimensions[patch_id]

        # Extract mask tensor and convert to numpy for analysis
        mask_np = mask.mask_tensor.cpu().numpy().astype(np.uint8)

        # Find contours
        contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            continue

        # Analyze the largest contour
        largest_contour = max(contours, key=cv2.contourArea)

        # Calculate shape features
        area = cv2.contourArea(largest_contour)
        perimeter = cv2.arcLength(largest_contour, True)

        # Circularity: 4*pi*area/perimeter^2 (1 for perfect circle, less for irregular shapes)
        circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0

        # Fit a rectangle
        rect = cv2.minAreaRect(largest_contour)
        box = cv2.boxPoints(rect)
        box = np.int0(box)
        rect_area = cv2.contourArea(box)

        # Rectangularity: ratio of contour area to bounding rectangle area
        rectangularity = area / rect_area if rect_area > 0 else 0

        # Get relative position in patch
        x1_rel = mask.bbox[0] - x_start
        y1_rel = mask.bbox[1] - y_start
        x2_rel = mask.bbox[2] - x_start
        y2_rel = mask.bbox[3] - y_start

        # Calculate artifact score based on multiple factors
        artifact_score = 0.0

        # Factor 1: Boundary contact (higher score for more contact)
        artifact_score += mask.boundary_score * 0.4

        # Factor 2: Shape irregularity (higher score for less circular shapes)
        artifact_score += (1.0 - circularity) * 0.3

        # Factor 3: Rectangularity (higher score for more rectangular shapes)
        # Rectangular artifacts are common at patch boundaries
        artifact_score += rectangularity * 0.3

        # Factor 4: Position (higher score for masks at corners)
        is_at_corner = ((x1_rel <= 1 and y1_rel <= 1) or  # Top-left
                        (x1_rel <= 1 and y2_rel >= patch_h - 1) or  # Bottom-left
                        (x2_rel >= patch_w - 1 and y1_rel <= 1) or  # Top-right
                        (x2_rel >= patch_w - 1 and y2_rel >= patch_h - 1))  # Bottom-right

        if is_at_corner:
            artifact_score += 0.2  # Bonus for corner position

        # Normalize final score to 0-1 range
        mask.artifact_score = min(1.0, artifact_score)

        # Classify as artifact if score exceeds threshold
        mask.is_artifact = mask.artifact_score > artifact_threshold

    return mask_infos


def merge_split_masks(mask_infos: List[MaskInfo],
                      full_image_shape: Tuple[int, int],
                      device: torch.device) -> List[torch.Tensor]:
    """
    Merges masks that are likely split across patch boundaries.

    Args:
        mask_infos: List of MaskInfo objects with neighbor information
        full_image_shape: (height, width) of the full image
        device: Torch device for tensor operations

    Returns:
        List of merged mask tensors
    """
    full_height, full_width = full_image_shape

    # Create a mapping of mask_id to index in the list for quick lookup
    mask_id_to_index = {mask.mask_id: i for i, mask in enumerate(mask_infos)}

    # Create a set to track which masks have been processed
    processed_masks = set()

    # List to store final merged masks
    merged_masks = []

    # Process masks in order of decreasing score (non-artifacts first)
    sorted_masks = sorted(mask_infos, key=lambda m: (m.is_artifact, -m.score))

    for mask in sorted_masks:
        if mask.mask_id in processed_masks:
            continue

        if mask.is_artifact and not mask.neighbors:
            # Skip isolated artifacts
            processed_masks.add(mask.mask_id)
            continue

        # Create a full-size mask for this mask
        full_mask = torch.zeros((full_height, full_width), dtype=torch.uint8, device='cpu')

        # Place this mask on the full canvas
        x_offset, y_offset = mask.offset
        mask_h, mask_w = mask.mask_tensor.shape

        # Ensure we don't go out of bounds
        place_h = min(mask_h, full_height - y_offset) if y_offset < full_height else 0
        place_w = min(mask_w, full_width - x_offset) if x_offset < full_width else 0

        if place_h > 0 and place_w > 0:
            full_mask[y_offset:y_offset+place_h, x_offset:x_offset+place_w] = \
                mask.mask_tensor[:place_h, :place_w]

        # Add this mask to processed set
        processed_masks.add(mask.mask_id)

        # Check if this mask has neighbors to merge with
        if mask.neighbors:
            # Process all connected neighbors
            connected_neighbors = set(mask.neighbors)
            processed_neighbors = set()

            while connected_neighbors:
                neighbor_id = connected_neighbors.pop()

                if neighbor_id in processed_masks or neighbor_id in processed_neighbors:
                    continue

                if neighbor_id not in mask_id_to_index:
                    continue

                neighbor = mask_infos[mask_id_to_index[neighbor_id]]

                # Skip if neighbor is an artifact with high score
                if neighbor.is_artifact and neighbor.artifact_score > 0.8:
                    processed_neighbors.add(neighbor_id)
                    continue

                # Place neighbor mask on the full canvas
                nx_offset, ny_offset = neighbor.offset
                nmask_h, nmask_w = neighbor.mask_tensor.shape

                # Ensure we don't go out of bounds
                nplace_h = min(nmask_h, full_height - ny_offset) if ny_offset < full_height else 0
                nplace_w = min(nmask_w, full_width - nx_offset) if nx_offset < full_width else 0

                if nplace_h > 0 and nplace_w > 0:
                    # Use maximum to combine overlapping regions
                    region = full_mask[ny_offset:ny_offset+nplace_h, nx_offset:nx_offset+nplace_w]
                    full_mask[ny_offset:ny_offset+nplace_h, nx_offset:nx_offset+nplace_w] = \
                        torch.maximum(region, neighbor.mask_tensor[:nplace_h, :nplace_w])

                # Add this neighbor to processed sets
                processed_masks.add(neighbor_id)
                processed_neighbors.add(neighbor_id)

                # Add neighbor's neighbors to the connected set
                for nn_id in neighbor.neighbors:
                    if nn_id not in processed_masks and nn_id not in processed_neighbors:
                        connected_neighbors.add(nn_id)

        # Add the merged mask to the final list
        merged_masks.append(full_mask)

    # Move masks to the target device
    return [mask.to(device) for mask in merged_masks]


def filter_artifacts(mask_infos: List[MaskInfo],
                     artifact_threshold: float = 0.7) -> List[int]:
    """
    Filters out masks that are likely artifacts based on their artifact score.

    Args:
        mask_infos: List of MaskInfo objects with artifact information
        artifact_threshold: Threshold for filtering artifacts

    Returns:
        List of indices to keep (non-artifacts)
    """
    keep_indices = []

    for i, mask in enumerate(mask_infos):
        # Keep masks that are not artifacts or have low artifact scores
        if not mask.is_artifact or mask.artifact_score <= artifact_threshold:
            keep_indices.append(i)

    return keep_indices


def detect_duplicate_grains(mask_infos: List[MaskInfo],
                          full_image_shape: Tuple[int, int],
                          containment_threshold: float = 0.7,
                          batch_size: int = 100,
                          max_pairs_to_check: int = 10000) -> List[int]:
    """
    Identifies and filters out duplicate grains where one is a partial version of another.
    Uses batch processing and early termination for better performance.

    Args:
        mask_infos: List of MaskInfo objects
        full_image_shape: (height, width) of the full image
        containment_threshold: Threshold for considering one mask contained in another
        batch_size: Number of masks to process in each batch
        max_pairs_to_check: Maximum number of mask pairs to check in detail

    Returns:
        List of indices to keep (non-duplicates)
    """
    logger.info(f"Starting optimized duplicate grain detection with {len(mask_infos)} masks and threshold {containment_threshold:.2f}")
    full_height, full_width = full_image_shape
    t_start = time.time()

    # Create a list to track which masks should be removed
    masks_to_remove = set()

    # Sort masks by area (largest first) to prioritize keeping complete grains
    sorted_indices = sorted(range(len(mask_infos)),
                           key=lambda i: -mask_infos[i].area)

    # For efficiency, only create bounding box representations first
    # We'll only create full canvases when needed for overlap calculation
    bbox_areas = []
    for mask in mask_infos:
        x1, y1, x2, y2 = mask.bbox
        bbox_areas.append((x1, y1, x2, y2, mask.area))

    # First pass: quick filtering using bounding boxes
    # Use spatial indexing to reduce the number of comparisons
    bbox_overlaps = []

    # Group masks by their spatial location using a grid-based approach
    grid_size = 100  # Size of each grid cell in pixels
    grid = {}  # Dictionary mapping grid cell to list of mask indices

    # Assign masks to grid cells
    for i in sorted_indices:
        x1, y1, x2, y2, _ = bbox_areas[i]
        # Get grid cells that this mask overlaps with
        grid_x1, grid_y1 = x1 // grid_size, y1 // grid_size
        grid_x2, grid_y2 = (x2 - 1) // grid_size, (y2 - 1) // grid_size

        # Add mask to all overlapping grid cells
        for gx in range(grid_x1, grid_x2 + 1):
            for gy in range(grid_y1, grid_y2 + 1):
                grid_key = (gx, gy)
                if grid_key not in grid:
                    grid[grid_key] = []
                grid[grid_key].append(i)

    # Find potential overlapping pairs using the grid
    for grid_key, indices in grid.items():
        # Only check pairs within the same grid cell
        for idx1 in range(len(indices)):
            i = indices[idx1]
            if i in masks_to_remove:
                continue

            x1_i, y1_i, x2_i, y2_i, area_i = bbox_areas[i]

            for idx2 in range(idx1 + 1, len(indices)):
                j = indices[idx2]
                if j in masks_to_remove or i == j:
                    continue

                x1_j, y1_j, x2_j, y2_j, area_j = bbox_areas[j]

                # Check if bounding boxes overlap
                if x1_i > x2_j or x2_i < x1_j or y1_i > y2_j or y2_i < y1_j:
                    continue  # No overlap

                # Calculate bounding box intersection
                intersection_x1 = max(x1_i, x1_j)
                intersection_y1 = max(y1_i, y1_j)
                intersection_x2 = min(x2_i, x2_j)
                intersection_y2 = min(y2_i, y2_j)

                bbox_intersection = (intersection_x2 - intersection_x1) * (intersection_y2 - intersection_y1)
                smaller_bbox_area = min((x2_i - x1_i) * (y2_i - y1_i), (x2_j - x1_j) * (y2_j - y1_j))

                # If bounding boxes have significant overlap, add to candidates for precise check
                if bbox_intersection > 0 and bbox_intersection / smaller_bbox_area > 0.5:
                    bbox_overlaps.append((i, j))

                    # Early termination if we have too many pairs to check
                    if len(bbox_overlaps) >= max_pairs_to_check:
                        logger.warning(f"Reached maximum number of pairs to check ({max_pairs_to_check}), stopping early")
                        break

            # Early termination check
            if len(bbox_overlaps) >= max_pairs_to_check:
                break

        # Early termination check
        if len(bbox_overlaps) >= max_pairs_to_check:
            break

    logger.info(f"Found {len(bbox_overlaps)} potential overlapping mask pairs for detailed analysis in {time.time() - t_start:.2f}s")

    # Second pass: precise check only for masks with overlapping bounding boxes
    # Process in batches to reduce memory usage
    duplicates_found = 0

    # Group overlapping pairs by mask index to minimize canvas creation
    mask_to_pairs = {}
    for i, j in bbox_overlaps:
        if i not in mask_to_pairs:
            mask_to_pairs[i] = []
        if j not in mask_to_pairs:
            mask_to_pairs[j] = []
        mask_to_pairs[i].append((i, j))
        mask_to_pairs[j].append((i, j))

    # Process masks in batches
    masks_to_process = list(mask_to_pairs.keys())
    for batch_start in range(0, len(masks_to_process), batch_size):
        batch_end = min(batch_start + batch_size, len(masks_to_process))
        batch_masks = masks_to_process[batch_start:batch_end]

        # Create canvases only for masks in this batch
        canvases = {}
        for idx in batch_masks:
            if idx in masks_to_remove:
                continue

            mask = mask_infos[idx]

            # Use a more memory-efficient approach: create canvas only for the bounding box region
            x1, y1, x2, y2 = mask.bbox
            bbox_width, bbox_height = x2 - x1, y2 - y1

            # Skip if bounding box is invalid
            if bbox_width <= 0 or bbox_height <= 0:
                continue

            # Create a smaller canvas just for the bounding box
            canvas = torch.zeros((bbox_height, bbox_width), dtype=torch.uint8, device='cpu')

            # Place the mask on the canvas with adjusted offset
            x_offset, y_offset = mask.offset
            rel_x = x1 - x_offset
            rel_y = y1 - y_offset

            mask_h, mask_w = mask.mask_tensor.shape

            # Calculate the region to copy
            src_x1 = max(0, rel_x)
            src_y1 = max(0, rel_y)
            src_x2 = min(mask_w, rel_x + bbox_width)
            src_y2 = min(mask_h, rel_y + bbox_height)

            dst_x1 = max(0, -rel_x)
            dst_y1 = max(0, -rel_y)

            # Copy the mask region to the canvas
            if src_x2 > src_x1 and src_y2 > src_y1:
                width = src_x2 - src_x1
                height = src_y2 - src_y1
                canvas[dst_y1:dst_y1+height, dst_x1:dst_x1+width] = mask.mask_tensor[src_y1:src_y2, src_x1:src_x2]

            # Store the canvas and its metadata
            canvases[idx] = {
                'canvas': canvas,
                'bbox': (x1, y1, x2, y2),
                'area': torch.sum(canvas).item()
            }

        # Check overlaps for all pairs involving masks in this batch
        batch_pairs = set()
        for idx in batch_masks:
            if idx in mask_to_pairs:
                batch_pairs.update(mask_to_pairs[idx])

        for i, j in batch_pairs:
            if i in masks_to_remove or j in masks_to_remove:
                continue

            # Skip if either canvas wasn't created
            if i not in canvases or j not in canvases:
                continue

            canvas_i_data = canvases[i]
            canvas_j_data = canvases[j]

            area_i = canvas_i_data['area']
            area_j = canvas_j_data['area']

            # Skip if either mask has zero area
            if area_i == 0 or area_j == 0:
                continue

            # Calculate intersection using bounding box coordinates
            bbox_i = canvas_i_data['bbox']
            bbox_j = canvas_j_data['bbox']

            # Calculate intersection region
            inter_x1 = max(bbox_i[0], bbox_j[0])
            inter_y1 = max(bbox_i[1], bbox_j[1])
            inter_x2 = min(bbox_i[2], bbox_j[2])
            inter_y2 = min(bbox_i[3], bbox_j[3])

            # Skip if no intersection
            if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
                continue

            # Calculate relative coordinates in each canvas
            rel_i_x1 = inter_x1 - bbox_i[0]
            rel_i_y1 = inter_y1 - bbox_i[1]
            rel_i_x2 = inter_x2 - bbox_i[0]
            rel_i_y2 = inter_y2 - bbox_i[1]

            rel_j_x1 = inter_x1 - bbox_j[0]
            rel_j_y1 = inter_y1 - bbox_j[1]
            rel_j_x2 = inter_x2 - bbox_j[0]
            rel_j_y2 = inter_y2 - bbox_j[1]

            # Extract intersection regions
            region_i = canvas_i_data['canvas'][rel_i_y1:rel_i_y2, rel_i_x1:rel_i_x2]
            region_j = canvas_j_data['canvas'][rel_j_y1:rel_j_y2, rel_j_x1:rel_j_x2]

            # Calculate intersection
            intersection = torch.sum(region_i & region_j).item()

            # Calculate containment ratios
            smaller_area = min(area_i, area_j)
            if smaller_area == 0:
                continue

            overlap_ratio = intersection / smaller_area

            # If significant overlap, mark the smaller one for removal
            if overlap_ratio > containment_threshold:
                duplicates_found += 1
                if area_i >= area_j:
                    masks_to_remove.add(j)  # Remove the smaller mask
                else:
                    masks_to_remove.add(i)  # Remove the smaller mask

        # Clear canvases after processing this batch to free memory
        del canvases

        # Log progress for large datasets
        if len(masks_to_process) > batch_size:
            logger.info(f"Processed batch {batch_start//batch_size + 1}/{(len(masks_to_process) + batch_size - 1)//batch_size}, "
                       f"found {duplicates_found} duplicates so far")

    t_end = time.time()
    logger.info(f"Found {duplicates_found} duplicate masks based on containment in {t_end - t_start:.2f}s")

    # Return indices of masks to keep
    keep_indices = [i for i in range(len(mask_infos)) if i not in masks_to_remove]
    logger.info(f"Keeping {len(keep_indices)}/{len(mask_infos)} masks after duplicate detection")
    return keep_indices


def detect_boundary_duplicates(mask_infos: List[MaskInfo],
                              patch_dimensions: Dict[int, Tuple[int, int, int, int]],
                              full_image_shape: Tuple[int, int],
                              overlap_threshold: float = 0.5,
                              batch_size: int = 100,
                              max_pairs_to_check: int = 5000) -> List[int]:
    """
    Specifically targets duplicates at patch boundaries.
    Uses batch processing and optimized memory management.

    Args:
        mask_infos: List of MaskInfo objects
        patch_dimensions: Dictionary mapping patch_id to (x_start, y_start, width, height)
        full_image_shape: (height, width) of the full image
        overlap_threshold: Threshold for considering masks as overlapping
        batch_size: Number of masks to process in each batch
        max_pairs_to_check: Maximum number of mask pairs to check in detail

    Returns:
        List of indices to keep (non-duplicates)
    """
    t_start = time.time()
    logger.info(f"Starting optimized boundary duplicate detection with {len(mask_infos)} masks and threshold {overlap_threshold:.2f}")
    full_height, full_width = full_image_shape

    # First, identify boundary masks
    boundary_masks = []
    for i, mask in enumerate(mask_infos):
        # Check if mask has is_boundary attribute
        if hasattr(mask, 'is_boundary') and mask.is_boundary:
            boundary_masks.append(i)
        # For masks that were created after merging and don't have the is_boundary attribute
        elif mask.patch_id == -1:  # Merged masks have patch_id = -1
            # Check if mask is near any patch boundary
            x1, y1, x2, y2 = mask.bbox
            for patch_id, (px, py, pw, ph) in patch_dimensions.items():
                if (abs(x1 - px) <= 5 or abs(x2 - (px + pw)) <= 5 or
                    abs(y1 - py) <= 5 or abs(y2 - (py + ph)) <= 5):
                    boundary_masks.append(i)
                    break

    logger.info(f"Identified {len(boundary_masks)} masks at patch boundaries in {time.time() - t_start:.2f}s")

    if not boundary_masks:
        logger.info("No boundary masks found, skipping boundary duplicate detection")
        return list(range(len(mask_infos)))

    # For efficiency, use spatial indexing to find potential overlaps
    bbox_overlaps = []

    # Group masks by their spatial location using a grid-based approach
    grid_size = 50  # Smaller grid size for more precise boundary detection
    grid = {}  # Dictionary mapping grid cell to list of mask indices

    # Add boundary masks to the grid
    for i in boundary_masks:
        mask = mask_infos[i]
        x1, y1, x2, y2 = mask.bbox

        # Get grid cells that this mask overlaps with
        grid_x1, grid_y1 = x1 // grid_size, y1 // grid_size
        grid_x2, grid_y2 = (x2 - 1) // grid_size, (y2 - 1) // grid_size

        # Add mask to all overlapping grid cells
        for gx in range(grid_x1, grid_x2 + 1):
            for gy in range(grid_y1, grid_y2 + 1):
                grid_key = (gx, gy)
                if grid_key not in grid:
                    grid[grid_key] = []
                grid[grid_key].append(i)

    # Add non-boundary masks to the grid for comparison
    for j, mask in enumerate(mask_infos):
        if j in boundary_masks:
            continue  # Already added

        x1, y1, x2, y2 = mask.bbox

        # Get grid cells that this mask overlaps with
        grid_x1, grid_y1 = x1 // grid_size, y1 // grid_size
        grid_x2, grid_y2 = (x2 - 1) // grid_size, (y2 - 1) // grid_size

        # Add mask to all overlapping grid cells
        for gx in range(grid_x1, grid_x2 + 1):
            for gy in range(grid_y1, grid_y2 + 1):
                grid_key = (gx, gy)
                if grid_key not in grid:
                    grid[grid_key] = []
                grid[grid_key].append(j)

    # Find potential overlapping pairs using the grid
    for grid_key, indices in grid.items():
        boundary_indices = [i for i in indices if i in boundary_masks]

        # Skip if no boundary masks in this cell
        if not boundary_indices:
            continue

        # Compare boundary masks with all masks in this cell
        for i in boundary_indices:
            mask_i = mask_infos[i]
            x1_i, y1_i, x2_i, y2_i = mask_i.bbox
            patch_id_i = mask_i.patch_id

            for j in indices:
                if i == j:
                    continue

                mask_j = mask_infos[j]

                # Skip if from same patch
                if mask_j.patch_id == patch_id_i and patch_id_i != -1:  # Allow comparison if both are merged masks
                    continue

                x1_j, y1_j, x2_j, y2_j = mask_j.bbox

                # Check if bounding boxes overlap
                if x1_i > x2_j or x2_i < x1_j or y1_i > y2_j or y2_i < y1_j:
                    continue  # No overlap

                # Calculate bounding box intersection
                intersection_x1 = max(x1_i, x1_j)
                intersection_y1 = max(y1_i, y1_j)
                intersection_x2 = min(x2_i, x2_j)
                intersection_y2 = min(y2_i, y2_j)

                bbox_intersection = (intersection_x2 - intersection_x1) * (intersection_y2 - intersection_y1)
                smaller_bbox_area = min((x2_i - x1_i) * (y2_i - y1_i), (x2_j - x1_j) * (y2_j - y1_j))

                # If bounding boxes have significant overlap, add to candidates for precise check
                if bbox_intersection > 0 and bbox_intersection / smaller_bbox_area > 0.3:  # Lower threshold for initial filtering
                    bbox_overlaps.append((i, j))

                    # Early termination if we have too many pairs to check
                    if len(bbox_overlaps) >= max_pairs_to_check:
                        logger.warning(f"Reached maximum number of boundary pairs to check ({max_pairs_to_check}), stopping early")
                        break

            # Early termination check
            if len(bbox_overlaps) >= max_pairs_to_check:
                break

        # Early termination check
        if len(bbox_overlaps) >= max_pairs_to_check:
            break

    logger.info(f"Found {len(bbox_overlaps)} potential boundary overlap pairs for detailed analysis in {time.time() - t_start:.2f}s")

    # Process in batches to reduce memory usage
    duplicates = set()
    duplicates_found = 0

    # Group overlapping pairs by mask index to minimize canvas creation
    mask_to_pairs = {}
    for i, j in bbox_overlaps:
        if i not in mask_to_pairs:
            mask_to_pairs[i] = []
        if j not in mask_to_pairs:
            mask_to_pairs[j] = []
        mask_to_pairs[i].append((i, j))
        mask_to_pairs[j].append((i, j))

    # Process masks in batches
    masks_to_process = list(mask_to_pairs.keys())
    for batch_start in range(0, len(masks_to_process), batch_size):
        batch_end = min(batch_start + batch_size, len(masks_to_process))
        batch_masks = masks_to_process[batch_start:batch_end]

        # Create canvases only for masks in this batch
        canvases = {}
        for idx in batch_masks:
            if idx in duplicates:
                continue

            mask = mask_infos[idx]

            # Use a more memory-efficient approach: create canvas only for the bounding box region
            x1, y1, x2, y2 = mask.bbox
            bbox_width, bbox_height = x2 - x1, y2 - y1

            # Skip if bounding box is invalid
            if bbox_width <= 0 or bbox_height <= 0:
                continue

            # Create a smaller canvas just for the bounding box
            canvas = torch.zeros((bbox_height, bbox_width), dtype=torch.uint8, device='cpu')

            # Place the mask on the canvas with adjusted offset
            x_offset, y_offset = mask.offset
            rel_x = x1 - x_offset
            rel_y = y1 - y_offset

            mask_h, mask_w = mask.mask_tensor.shape

            # Calculate the region to copy
            src_x1 = max(0, rel_x)
            src_y1 = max(0, rel_y)
            src_x2 = min(mask_w, rel_x + bbox_width)
            src_y2 = min(mask_h, rel_y + bbox_height)

            dst_x1 = max(0, -rel_x)
            dst_y1 = max(0, -rel_y)

            # Copy the mask region to the canvas
            if src_x2 > src_x1 and src_y2 > src_y1:
                width = src_x2 - src_x1
                height = src_y2 - src_y1
                canvas[dst_y1:dst_y1+height, dst_x1:dst_x1+width] = mask.mask_tensor[src_y1:src_y2, src_x1:src_x2]

            # Store the canvas and its metadata
            canvases[idx] = {
                'canvas': canvas,
                'bbox': (x1, y1, x2, y2),
                'area': torch.sum(canvas).item()
            }

        # Check overlaps for all pairs involving masks in this batch
        batch_pairs = set()
        for idx in batch_masks:
            if idx in mask_to_pairs:
                batch_pairs.update(mask_to_pairs[idx])

        for i, j in batch_pairs:
            if i in duplicates or j in duplicates:
                continue

            # Skip if either canvas wasn't created
            if i not in canvases or j not in canvases:
                continue

            canvas_i_data = canvases[i]
            canvas_j_data = canvases[j]

            area_i = canvas_i_data['area']
            area_j = canvas_j_data['area']

            # Skip if either mask has zero area
            if area_i == 0 or area_j == 0:
                continue

            # Calculate intersection using bounding box coordinates
            bbox_i = canvas_i_data['bbox']
            bbox_j = canvas_j_data['bbox']

            # Calculate intersection region
            inter_x1 = max(bbox_i[0], bbox_j[0])
            inter_y1 = max(bbox_i[1], bbox_j[1])
            inter_x2 = min(bbox_i[2], bbox_j[2])
            inter_y2 = min(bbox_i[3], bbox_j[3])

            # Skip if no intersection
            if inter_x2 <= inter_x1 or inter_y2 <= inter_y1:
                continue

            # Calculate relative coordinates in each canvas
            rel_i_x1 = inter_x1 - bbox_i[0]
            rel_i_y1 = inter_y1 - bbox_i[1]
            rel_i_x2 = inter_x2 - bbox_i[0]
            rel_i_y2 = inter_y2 - bbox_i[1]

            rel_j_x1 = inter_x1 - bbox_j[0]
            rel_j_y1 = inter_y1 - bbox_j[1]
            rel_j_x2 = inter_x2 - bbox_j[0]
            rel_j_y2 = inter_y2 - bbox_j[1]

            # Extract intersection regions
            region_i = canvas_i_data['canvas'][rel_i_y1:rel_i_y2, rel_i_x1:rel_i_x2]
            region_j = canvas_j_data['canvas'][rel_j_y1:rel_j_y2, rel_j_x1:rel_j_x2]

            # Calculate intersection
            intersection = torch.sum(region_i & region_j).item()

            # Calculate containment ratios
            smaller_area = min(area_i, area_j)
            if smaller_area == 0:
                continue

            overlap_ratio = intersection / smaller_area

            # If significant overlap, mark the smaller one as duplicate
            if overlap_ratio > overlap_threshold:  # Significant overlap
                duplicates_found += 1
                if area_i < area_j:
                    duplicates.add(i)
                else:
                    duplicates.add(j)

        # Clear canvases after processing this batch to free memory
        del canvases

        # Log progress for large datasets
        if len(masks_to_process) > batch_size:
            logger.info(f"Processed boundary batch {batch_start//batch_size + 1}/{(len(masks_to_process) + batch_size - 1)//batch_size}, "
                       f"found {duplicates_found} duplicates so far")

    t_end = time.time()
    logger.info(f"Found {duplicates_found} boundary duplicate masks in {t_end - t_start:.2f}s")

    # Return indices to keep
    keep_indices = [i for i in range(len(mask_infos)) if i not in duplicates]
    logger.info(f"Keeping {len(keep_indices)}/{len(mask_infos)} masks after boundary duplicate detection")
    return keep_indices


# Global cache for patch merging results to avoid redundant computations
_patch_merge_cache = {}
_cache_max_size = 50  # Maximum number of cached results

def _get_patch_cache_key(patch_annotations, full_image_shape, artifact_sensitivity, duplicate_sensitivity, model_type):
    """Generate a cache key for patch merging results."""
    try:
        # Create a hash based on patch data and parameters
        import hashlib

        # Hash the patch data
        patch_hash_data = []
        for patch in patch_annotations:
            if patch is not None:
                annotations, offset = patch
                if isinstance(annotations, torch.Tensor):
                    # Use tensor shape and a sample of values for hashing
                    patch_hash_data.append(f"{annotations.shape}_{annotations.sum().item():.6f}_{offset}")
                else:
                    patch_hash_data.append(f"none_{offset}")
            else:
                patch_hash_data.append("none")

        # Combine all parameters
        cache_data = {
            'patches': '|'.join(patch_hash_data),
            'shape': full_image_shape,
            'artifact_sens': round(artifact_sensitivity, 3),
            'duplicate_sens': round(duplicate_sensitivity, 3),
            'model': model_type
        }

        # Create hash
        cache_str = str(cache_data)
        return hashlib.md5(cache_str.encode()).hexdigest()
    except Exception:
        # If hashing fails, return None to disable caching for this call
        return None

def _manage_cache_size():
    """Remove oldest entries if cache is too large."""
    global _patch_merge_cache
    if len(_patch_merge_cache) > _cache_max_size:
        # Remove oldest entries (simple FIFO)
        keys_to_remove = list(_patch_merge_cache.keys())[:-_cache_max_size//2]
        for key in keys_to_remove:
            del _patch_merge_cache[key]
        logger.info(f"Cache cleanup: removed {len(keys_to_remove)} old entries")

def intelligent_patch_merge(
    patch_annotations: List[Optional[Tuple[torch.Tensor, Tuple[int, int]]]],
    full_image_shape: Tuple[int, int],
    device: torch.device,
    artifact_sensitivity: float = 0.5,  # 0.0 (keep all) to 1.0 (aggressive filtering)
    duplicate_sensitivity: float = 0.7,  # 0.0 (keep all) to 1.0 (aggressive duplicate removal)
    min_mask_area_threshold: int = 10,
    model_type: str = "fastsam",  # Model type: 'fastsam' or 'mobilesam'
    duplicate_detection_timeout: int = 60,  # Maximum time for duplicate detection in seconds
    batch_size: int = 100,  # Number of masks to process in each batch
    max_pairs_to_check: int = 10000,  # Maximum number of mask pairs to check in detail
    enable_caching: bool = True  # Enable caching to avoid redundant computations
) -> torch.Tensor:
    """
    Intelligently merges patch annotations with advanced artifact handling.
    Uses optimized algorithms for better performance with large numbers of grains.

    Args:
        patch_annotations: List of (annotations_tensor, (offset_x, offset_y)) tuples
        full_image_shape: (height, width) of the full image
        device: Torch device for tensor operations
        artifact_sensitivity: Controls how aggressively to filter artifacts (0.0-1.0)
        duplicate_sensitivity: Controls how aggressively to filter duplicates (0.0-1.0)
        min_mask_area_threshold: Minimum area for a mask to be considered
        model_type: Model type used for segmentation ('fastsam' or 'mobilesam')
        duplicate_detection_timeout: Maximum time for duplicate detection in seconds
        batch_size: Number of masks to process in each batch
        max_pairs_to_check: Maximum number of mask pairs to check in detail

    Returns:
        Tensor of merged masks (N, H, W)
    """
    t_start = time.time()
    full_height, full_width = full_image_shape

    # Check cache first if enabled
    cache_key = None
    if enable_caching:
        cache_key = _get_patch_cache_key(patch_annotations, full_image_shape,
                                       artifact_sensitivity, duplicate_sensitivity, model_type)
        if cache_key and cache_key in _patch_merge_cache:
            cached_result = _patch_merge_cache[cache_key]
            logger.info(f"CACHE HIT: Retrieved cached patch merge result in {time.time() - t_start:.3f}s")
            logger.info("PERFORMANCE BOOST: Avoided expensive patch merging computation!")
            return cached_result.to(device)

    # Adjust thresholds based on sensitivity parameters
    # Higher sensitivity = lower threshold = more aggressive filtering
    artifact_threshold = 0.8 - (artifact_sensitivity * 0.5)
    containment_threshold = 0.6 + (duplicate_sensitivity * 0.3)  # 0.6 to 0.9 based on sensitivity

    logger.info(f"Starting intelligent patch merge with model type {model_type}, "
                f"artifact sensitivity {artifact_sensitivity:.2f} (threshold: {artifact_threshold:.2f}) "
                f"and duplicate sensitivity {duplicate_sensitivity:.2f} (threshold: {containment_threshold:.2f})")
    logger.info(f"Using batch size {batch_size}, max pairs {max_pairs_to_check}, and timeout {duplicate_detection_timeout}s")
    if enable_caching:
        logger.info(f"Caching enabled - cache key: {cache_key[:16] if cache_key else 'None'}...")

    # Adjust parameters based on model type
    is_mobilesam = model_type.lower() == 'mobilesam'
    if is_mobilesam:
        logger.info("Using MobileSAM-specific parameters for patch processing")
        # MobileSAM tends to detect patch boundaries as objects, so we need to be more aggressive
        # in filtering them out

    # Step 1: Extract mask information from patches
    mask_infos = []
    patch_dimensions = {}
    mask_id_counter = 0

    for patch_id, patch_data in enumerate(patch_annotations):
        if patch_data is None:
            continue

        patch_mask_tensor, (offset_x, offset_y) = patch_data

        if patch_mask_tensor is None or patch_mask_tensor.dim() != 3 or patch_mask_tensor.shape[0] == 0:
            continue

        # Store patch dimensions for boundary detection
        patch_h, patch_w = patch_mask_tensor.shape[1:]
        patch_dimensions[patch_id] = (offset_x, offset_y, patch_w, patch_h)

        # Process each mask in the patch
        for mask_idx in range(patch_mask_tensor.shape[0]):
            mask_tensor = patch_mask_tensor[mask_idx].cpu()

            # Skip masks that are too small
            mask_area = torch.sum(mask_tensor).item()
            if mask_area < min_mask_area_threshold:
                continue

            # Find bounding box
            mask_np = mask_tensor.numpy().astype(np.uint8)
            y_indices, x_indices = np.where(mask_np > 0)

            if len(y_indices) == 0 or len(x_indices) == 0:
                continue

            x1, y1 = np.min(x_indices), np.min(y_indices)
            x2, y2 = np.max(x_indices) + 1, np.max(y_indices) + 1

            # Convert to full image coordinates
            x1_full, y1_full = x1 + offset_x, y1 + offset_y
            x2_full, y2_full = x2 + offset_x, y2 + offset_y

            # Create mask info
            mask_info = MaskInfo(
                mask_id=mask_id_counter,
                patch_id=patch_id,
                bbox=(x1_full, y1_full, x2_full, y2_full),
                area=mask_area,
                score=1.0,  # Default score, will be adjusted later
                offset=(offset_x, offset_y),
                mask_tensor=mask_tensor
            )

            mask_infos.append(mask_info)
            mask_id_counter += 1

    if not mask_infos:
        logger.warning("No valid masks found in patches")
        return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)

    logger.info(f"Extracted {len(mask_infos)} masks from patches")

    # For MobileSAM, filter out patch-sized rectangular masks before further processing
    if is_mobilesam:
        # Use more aggressive thresholds for MobileSAM
        keep_indices = detect_patch_sized_masks(
            mask_infos,
            patch_dimensions,
            rectangularity_threshold=0.8,  # Even lower threshold to catch more patch-sized masks
            area_ratio_threshold=0.6,  # Even lower threshold to catch more patch-sized masks
            alignment_tolerance=20,  # Larger tolerance to catch more boundary-aligned masks
            detect_partial_patches=True,  # Enable detection of partial patch boundaries
            detect_straight_edges=True  # Enable detection of straight-edged masks
        )

        if len(keep_indices) < len(mask_infos):
            # Filter mask_infos to keep only non-patch-sized masks
            mask_infos = [mask_infos[i] for i in keep_indices]
            logger.info(f"After filtering patch-sized masks: {len(mask_infos)} masks remaining")

            if not mask_infos:
                logger.warning("No valid masks remained after filtering patch-sized masks")
                return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)

        # Run a second pass with even more aggressive parameters if we still have many masks
        # This helps catch any remaining patch boundaries that might have been missed
        if len(mask_infos) > 100:  # Only if we have many masks left
            logger.info("Running second pass of patch boundary detection with more aggressive parameters")
            keep_indices = detect_patch_sized_masks(
                mask_infos,
                patch_dimensions,
                rectangularity_threshold=0.75,  # Even more aggressive
                area_ratio_threshold=0.5,  # Even more aggressive
                alignment_tolerance=25,  # Even larger tolerance
                detect_partial_patches=True,
                detect_straight_edges=True
            )

            if len(keep_indices) < len(mask_infos):
                # Filter mask_infos again
                mask_infos = [mask_infos[i] for i in keep_indices]
                logger.info(f"After second pass filtering: {len(mask_infos)} masks remaining")

                if not mask_infos:
                    logger.warning("No valid masks remained after second pass filtering")
                    return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)

    # Step 2: Detect boundary masks
    mask_infos = detect_boundary_masks(mask_infos, patch_dimensions)
    boundary_count = sum(1 for m in mask_infos if m.is_boundary)
    logger.info(f"Detected {boundary_count} masks at patch boundaries")

    # Step 3: Find neighboring masks
    mask_infos = find_mask_neighbors(mask_infos)

    # Step 4: Detect artifacts
    mask_infos = detect_patch_artifacts(mask_infos, patch_dimensions, artifact_threshold)
    artifact_count = sum(1 for m in mask_infos if m.is_artifact)
    logger.info(f"Detected {artifact_count} potential artifacts")

    # Step 5: Merge split masks
    merged_masks = merge_split_masks(mask_infos, full_image_shape, device)

    if not merged_masks:
        logger.warning("No masks remained after merging")
        return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)

    # Step 6: Convert merged masks back to MaskInfo format for duplicate detection
    merged_mask_infos = []
    for i, mask_tensor in enumerate(merged_masks):
        # Find bounding box
        mask_np = mask_tensor.cpu().numpy().astype(np.uint8)
        y_indices, x_indices = np.where(mask_np > 0)

        if len(y_indices) == 0 or len(x_indices) == 0:
            continue

        x1, y1 = np.min(x_indices), np.min(y_indices)
        x2, y2 = np.max(x_indices) + 1, np.max(y_indices) + 1

        # Create mask info
        mask_info = MaskInfo(
            mask_id=i,
            patch_id=-1,  # No specific patch after merging
            bbox=(x1, y1, x2, y2),
            area=torch.sum(mask_tensor).item(),
            score=1.0,
            offset=(0, 0),  # Full image coordinates
            mask_tensor=mask_tensor.cpu()
        )

        merged_mask_infos.append(mask_info)

    logger.info(f"Created {len(merged_mask_infos)} merged mask infos for duplicate detection")

    # Step 7: Detect and filter duplicate grains with timeout protection
    if duplicate_sensitivity > 0.1:  # Only perform duplicate detection if sensitivity is significant
        try:
            # Set a timeout for duplicate detection
            duplicate_detection_start = time.time()

            # Adjust batch size based on number of masks
            adaptive_batch_size = batch_size
            if len(merged_mask_infos) > 1000:
                # Use larger batches for very large datasets
                adaptive_batch_size = min(200, len(merged_mask_infos) // 5)
                logger.info(f"Adjusting batch size to {adaptive_batch_size} for large dataset")

            # First apply general duplicate detection with optimized parameters
            logger.info("Starting optimized general duplicate detection...")
            keep_indices = detect_duplicate_grains(
                merged_mask_infos,
                full_image_shape,
                containment_threshold=containment_threshold,
                batch_size=adaptive_batch_size,
                max_pairs_to_check=max_pairs_to_check
            )
            logger.info(f"General duplicate detection: keeping {len(keep_indices)}/{len(merged_mask_infos)} masks")

            # Check if we're approaching timeout
            if time.time() - duplicate_detection_start > duplicate_detection_timeout * 0.7:
                logger.warning("General duplicate detection took too long, skipping boundary duplicate detection")
                final_keep_indices = keep_indices
            else:
                # Then apply boundary-specific duplicate detection with optimized parameters
                logger.info("Starting optimized boundary duplicate detection...")
                boundary_keep_indices = detect_boundary_duplicates(
                    [merged_mask_infos[i] for i in keep_indices],
                    patch_dimensions,
                    full_image_shape,
                    overlap_threshold=0.4 + (duplicate_sensitivity * 0.3),  # 0.4 to 0.7 based on sensitivity
                    batch_size=adaptive_batch_size,
                    max_pairs_to_check=max_pairs_to_check // 2  # Use fewer pairs for boundary detection
                )

                # Map back to original indices
                final_keep_indices = [keep_indices[i] for i in boundary_keep_indices]
                logger.info(f"Boundary duplicate detection: keeping {len(final_keep_indices)}/{len(keep_indices)} masks")

            # Get final masks
            final_masks = [merged_masks[i] for i in final_keep_indices]

            # Log total duplicate detection time
            duplicate_detection_time = time.time() - duplicate_detection_start
            logger.info(f"Duplicate detection completed in {duplicate_detection_time:.2f} seconds")

            # Check if we exceeded the timeout
            if duplicate_detection_time > duplicate_detection_timeout:
                logger.warning(f"Duplicate detection took longer than the configured timeout ({duplicate_detection_timeout}s)")
                logger.warning("Consider increasing the timeout or reducing the sensitivity for better performance")

        except Exception as e:
            logger.error(f"Error during duplicate detection: {e}")
            logger.warning("Falling back to using all merged masks without duplicate detection")
            final_masks = merged_masks
    else:
        # Skip duplicate detection if sensitivity is very low
        final_masks = merged_masks
        logger.info(f"Skipping duplicate detection due to low sensitivity: keeping all {len(merged_masks)} masks")

    if not final_masks:
        logger.warning("No masks remained after duplicate filtering")
        return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)

    # Step 8: Stack final masks
    try:
        final_masks_tensor = torch.stack([mask.to(device) for mask in final_masks])
    except Exception as e:
        logger.error(f"Error stacking final masks: {e}")
        return torch.empty((0, full_height, full_width), dtype=torch.uint8, device=device)

    # Store result in cache if enabled
    if enable_caching and cache_key:
        try:
            # Store a copy on CPU to avoid device issues
            _patch_merge_cache[cache_key] = final_masks_tensor.cpu()
            _manage_cache_size()
            logger.info(f"Cached patch merge result with key: {cache_key[:16]}...")
        except Exception as e:
            logger.warning(f"Failed to cache result: {e}")

    t_end = time.time()
    logger.info(f"Intelligent patch merge completed in {t_end - t_start:.3f}s. "
                f"Final tensor shape: {final_masks_tensor.shape}")

    return final_masks_tensor
