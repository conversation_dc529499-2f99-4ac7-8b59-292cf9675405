/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
}

.nav-logo i {
    margin-right: 10px;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
}

.nav-menu li {
    margin-left: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #2563eb;
}

.nav-link.cta-btn {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav-link.cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
    color: white;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') repeat;
    opacity: 0.5;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease-out;
}

.gradient-text {
    background: linear-gradient(135deg, #2563eb, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: #64748b;
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

.btn-secondary {
    background: white;
    color: #2563eb;
    border: 2px solid #2563eb;
}

.btn-secondary:hover {
    background: #2563eb;
    color: white;
    transform: translateY(-2px);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #2563eb;
}

.stat-label {
    font-size: 0.9rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-visual {
    position: relative;
    animation: fadeInRight 1s ease-out 0.8s both;
}

.hero-image {
    width: 400px;
    height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    position: relative;
    margin: 0 auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.floating-card {
    position: absolute;
    background: white;
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    font-size: 1.2rem;
    color: #2563eb;
}

.floating-card span {
    font-weight: 600;
    font-size: 0.9rem;
}

.card-1 {
    top: 20px;
    left: -50px;
    animation-delay: 0s;
}

.card-2 {
    top: 50%;
    right: -60px;
    animation-delay: 1s;
}

.card-3 {
    bottom: 30px;
    left: -40px;
    animation-delay: 2s;
}

/* Sections */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1e293b;
}

.section-header p {
    font-size: 1.1rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: 100px 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #f1f5f9;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #2563eb, #3b82f6);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
}

.feature-card p {
    color: #64748b;
    line-height: 1.6;
}

/* About Section */
.about {
    padding: 100px 0;
    background: #f8fafc;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #1e293b;
}

.about-description {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.about-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.about-feature i {
    color: #10b981;
    font-size: 1.2rem;
}

.about-feature span {
    font-weight: 500;
    color: #374151;
}

.about-visual {
    position: relative;
}

.about-image {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tech-stack {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.tech-item {
    background: white;
    padding: 15px 20px;
    border-radius: 12px;
    text-align: center;
    font-weight: 600;
    color: #2563eb;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Timeline Section */
.timeline {
    padding: 100px 0;
    background: white;
}

.timeline-container {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e2e8f0;
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-marker {
    width: 20px;
    height: 20px;
    background: #e2e8f0;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    border: 4px solid white;
}

.timeline-item.completed .timeline-marker {
    background: #10b981;
}

.timeline-item.current .timeline-marker {
    background: #2563eb;
    animation: pulse 2s infinite;
}

.timeline-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    width: 45%;
    border: 1px solid #f1f5f9;
}

.timeline-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.timeline-content p {
    color: #64748b;
    margin-bottom: 1rem;
}

.timeline-date {
    font-size: 0.9rem;
    color: #2563eb;
    font-weight: 600;
}

/* Waitlist Section */
.waitlist {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.waitlist-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.waitlist-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.waitlist-text p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.waitlist-benefits {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.benefit {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.benefit i {
    color: #fbbf24;
    font-size: 1.2rem;
}

.waitlist-form-container {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.waitlist-form h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
}

.form-note {
    font-size: 0.9rem;
    color: #64748b;
    text-align: center;
    margin-top: 1rem;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: #f8fafc;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.contact-item i {
    font-size: 1.5rem;
    color: #2563eb;
    width: 40px;
    text-align: center;
}

.contact-item h4 {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #1e293b;
}

.contact-item p {
    color: #64748b;
}

.contact-form {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-logo i {
    margin-right: 10px;
    color: #3b82f6;
}

.footer-section h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #f1f5f9;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #3b82f6;
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 2rem;
    text-align: center;
    color: #94a3b8;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #64748b;
    transition: color 0.3s ease;
}

.close:hover {
    color: #1e293b;
}

.modal-body {
    padding: 3rem 2rem;
    text-align: center;
}

.modal-body i {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.modal-body h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
}

.modal-body p {
    color: #64748b;
    line-height: 1.6;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .hero-image {
        width: 300px;
        height: 300px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .waitlist-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .timeline-container::before {
        left: 20px;
    }

    .timeline-item {
        flex-direction: row !important;
        padding-left: 50px;
    }

    .timeline-marker {
        left: 20px !important;
        transform: none !important;
    }

    .timeline-content {
        width: 100%;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .about-text h2,
    .waitlist-text h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-image {
        width: 250px;
        height: 250px;
    }

    .floating-card {
        padding: 10px 15px;
    }

    .floating-card span {
        font-size: 0.8rem;
    }

    .waitlist-form-container,
    .contact-form {
        padding: 1.5rem;
    }

    .modal-content {
        width: 95%;
        margin: 20% auto;
    }

    .modal-body {
        padding: 2rem 1.5rem;
    }
}