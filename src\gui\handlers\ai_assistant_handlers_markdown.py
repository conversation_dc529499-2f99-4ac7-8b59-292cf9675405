"""
Modified AI Assistant handlers to support markdown rendering.
This module extends the existing AI Assistant handlers with markdown rendering capabilities.
"""

import logging
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Slot, Qt

from src.gui.utils.markdown_text_edit import MarkdownTextEdit

logger = logging.getLogger(__name__)

class AIAssistantMarkdownHandlers:
    """Mixin class for adding markdown rendering to AI Assistant handlers."""

    def setup_markdown_support(self):
        """Sets up markdown support for the AI Assistant page."""
        logger.info("Setting up markdown support for AI Assistant")

        # Check if the text results widget exists
        if not hasattr(self, 'ai_assistant_text_results'):
            logger.error("ai_assistant_text_results widget not found")
            return False

        # Store a reference to the original text widget
        self._original_text_results = self.ai_assistant_text_results

        # Create a new markdown text edit widget
        self.markdown_text_edit = MarkdownTextEdit(self.ai_assistant_text_results.parent())
        self.markdown_text_edit.setReadOnly(True)
        self.markdown_text_edit.setPlaceholderText("Analysis results will appear here...")

        # Replace the original widget with our markdown widget
        parent_layout = self.ai_assistant_text_results.parent().layout()
        if parent_layout:
            # Find the index of the original widget in the layout
            for i in range(parent_layout.count()):
                item = parent_layout.itemAt(i)
                if item and item.widget() == self.ai_assistant_text_results:
                    # Remove the original widget from the layout
                    parent_layout.removeWidget(self.ai_assistant_text_results)
                    self.ai_assistant_text_results.hide()

                    # Add the markdown widget to the layout at the same position
                    parent_layout.insertWidget(i, self.markdown_text_edit)

                    # Replace the reference to the text results widget
                    self.ai_assistant_text_results = self.markdown_text_edit

                    logger.info("Markdown text edit widget installed successfully")
                    return True

        logger.error("Failed to install markdown text edit widget")
        return False

    @Slot(str)
    def handle_ai_assistant_text_result(self, text):
        """Handles the text result from the Gemini API with markdown rendering."""
        logger.info(f"Received text result from Gemini API: {len(text)} characters")
        logger.info(f"Text preview: {text[:100]}..." if len(text) > 100 else f"Text: {text}")

        # Check if the text results widget exists
        if hasattr(self, 'ai_assistant_text_results'):
            logger.info("Setting text in ai_assistant_text_results widget")

            # Check if we're using the markdown widget
            if isinstance(self.ai_assistant_text_results, MarkdownTextEdit):
                logger.info("Using markdown rendering")
                # Clean up the text to ensure proper markdown rendering
                # Remove any code block fences that might be around JSON
                cleaned_text = text
                if cleaned_text.strip().startswith('```') and cleaned_text.strip().endswith('```'):
                    # Extract content between code fences
                    lines = cleaned_text.strip().split('\n')
                    if len(lines) > 2:  # At least 3 lines (opening fence, content, closing fence)
                        cleaned_text = '\n'.join(lines[1:-1])
                        logger.info("Removed code fences from text")

                # Set the markdown text
                self.ai_assistant_text_results.setMarkdown(cleaned_text)
            else:
                # Fall back to plain text
                logger.info("Using plain text rendering")
                self.ai_assistant_text_results.setText(text)
        else:
            logger.error("ai_assistant_text_results widget not found")

        # Save the result
        if hasattr(self, 'ai_assistant_current_image_id') and self.ai_assistant_current_image_id:
            if hasattr(self, 'save_ai_assistant_analysis_results'):
                self.save_ai_assistant_analysis_results()

        # Update UI state
        if hasattr(self, 'ai_assistant_analyze_button'):
            self.ai_assistant_analyze_button.setEnabled(True)
        if hasattr(self, 'ai_assistant_cancel_button'):
            self.ai_assistant_cancel_button.setEnabled(False)

        # Restore normal cursor
        QApplication.restoreOverrideCursor()

        # Update status
        if hasattr(self, 'update_ai_assistant_status'):
            self.update_ai_assistant_status("Analysis complete.")
