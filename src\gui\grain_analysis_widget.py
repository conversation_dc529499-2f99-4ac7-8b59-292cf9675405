# src/grainsight_components/gui/grain_analysis_widget.py

import sys
import os
import math
import json
import logging
import threading
import time
from datetime import datetime
import locale
from typing import Optional, List, Dict, Union, Tuple, Any

import numpy as np
import pandas as pd
import torch

# Import the grain analysis gallery
from src.widgets.grain_analysis_gallery import GrainAnalysisGallery
import cv2
from ultralytics import YOLO

# Use our wrapper module for MobileSAM
from src.utils.mobilesam_wrapper import is_available as is_mobilesam_available
from src.utils.mobilesam_wrapper import get_sam_model_registry, get_sam_predictor, get_sam_automatic_mask_generator

# Check if MobileSAM is available without importing it
MOBILE_SAM_AVAILABLE = is_mobilesam_available()

from PIL import Image

# PySide6 Imports
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import (Qt, QSize, QPoint, QRect, QPointF, QRectF,
                           QItemSelectionModel, QItemSelection, Signal, Slot,
                           QObject, QThread, QTimer, QMetaObject, Q_ARG, Q_RETURN_ARG)
from PySide6.QtGui import (QPixmap, QImage, QIcon, QPainter, QPen, QColor,
                           QFont, QFontDatabase, QPalette, QDoubleValidator,
                           QStandardItemModel, QStandardItem)
from PySide6.QtWidgets import (QWidget, QFileDialog, QMessageBox, QVBoxLayout,
                               QLabel, QSlider, QCheckBox, QLineEdit, QPushButton,
                               QGraphicsView, QGraphicsScene, QGraphicsPixmapItem,
                               QGraphicsLineItem, QFrame, QGridLayout, QRadioButton,
                               QHBoxLayout, QScrollArea, QTreeView, QHeaderView,
                               QProgressDialog, QDialog, QAbstractItemView, QSizePolicy,
                               QGraphicsPolygonItem, QGraphicsTextItem, QTabWidget, QGroupBox,
                               QListWidget, QListWidgetItem, QSplitter, QGraphicsRectItem,
                               QToolButton, QProgressBar, QStackedWidget, QSpinBox, QApplication,
                               QComboBox)

# --- Local GUI Imports from GrainSight Components ---
# Adjust paths relative to this file location within the project structure
try:
    from src.grainsight_components.gui.widgets import (CustomGraphicsView, ResultsViewWidget,
                          FastSAMParameterWidget, MobileSAMParameterWidget, CustomPixmapItem)
    from src.grainsight_components.gui.widgets import DEFAULT_CONTOUR_THICKNESS
    from src.grainsight_components.gui.workers import ProcessingWorker, PatchProcessingWorker
    from src.grainsight_components.gui.dialogs import (show_plot_selection_dialog, show_crop_dialog,
                          show_about_dialog, PlotDisplayDialog, show_patch_config_dialog)
    from src.grainsight_components.gui.utils import (pil_image_to_qimage, resource_path, load_theme_preference,
                        save_theme_preference, define_light_theme, define_dark_theme,
                        apply_stylesheet, find_system_font, load_font)
    from src.gui.grain_statistics_dialog import show_grain_statistics_dialog
except ImportError as e:
    print(f"ERROR importing GrainSight GUI components: {e}. Check relative paths.")
    # Add fallbacks for critical components
    # CustomGraphicsView fallback with required signals and methods
    class CustomGraphicsView(QGraphicsView):
        # Define required signals
        scale_line_drawn = Signal(QPointF, QPointF)
        scene_clicked = Signal(QPointF)
        zoom_changed = Signal(float)

        # Define mode constants
        MODE_SELECTION = 'selection'
        MODE_PAN = 'pan'
        MODE_ZOOM = 'zoom'
        MODE_SCALE = 'scale'

        def __init__(self, parent=None):
            super().__init__(parent)
            self.setRenderHint(QPainter.Antialiasing)
            self.setRenderHint(QPainter.SmoothPixmapTransform)
            self.setRenderHint(QPainter.TextAntialiasing)

            self.setDragMode(QGraphicsView.NoDrag)
            self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
            self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
            self.setInteractive(True)

            self._scale_factor = 1.0
            self.zoom_factor_base = 1.15
            self.min_zoom_level_steps = -10
            self.max_zoom_level_steps = 15
            self._zoom_level_steps = 0

            self._pan_start_pos = None
            self._is_panning = False
            self.mode = self.MODE_SELECTION

            # Scale Line Drawing state
            self.scale_line_start = None
            self.scale_line_end = None
            self.scale_line_item = None

        def set_mode(self, mode):
            self.mode = mode
            if mode == self.MODE_PAN:
                self.setCursor(Qt.OpenHandCursor)
            elif mode == self.MODE_SCALE:
                self.setCursor(Qt.CrossCursor)
            else:  # Default/selection mode
                self.setCursor(Qt.ArrowCursor)

        def reset_view(self):
            """Resets zoom, pan, and potentially clears temporary items."""
            # Reset transform to identity (no zoom/rotation)
            self.resetTransform()
            self._scale_factor = 1.0
            self._zoom_level_steps = 0
            self._is_panning = False

            # Check if scene and items exist before fitting
            if self.scene() and self.scene().items():
                # Fit all items in view
                self.fitInView(self.scene().itemsBoundingRect(), Qt.KeepAspectRatio)

            # Reset to default mode
            self.set_mode(self.MODE_SELECTION)

            # Emit zoom changed signal
            self.zoom_changed.emit(self._scale_factor)

        def set_scale_line_points(self, start_point, end_point):
            """Sets the scale line points and updates the display if needed."""
            self.scale_line_start = start_point
            self.scale_line_end = end_point

            # Clear any existing scale line
            if self.scale_line_item and self.scale_line_item.scene():
                self.scene().removeItem(self.scale_line_item)
                self.scale_line_item = None

            # Draw new scale line if both points are provided
            if start_point and end_point and self.scene():
                pen = QPen(Qt.red, 2, Qt.SolidLine)
                pen.setCosmetic(True)
                self.scale_line_item = QGraphicsLineItem()
                self.scale_line_item.setPen(pen)
                self.scale_line_item.setLine(start_point.x(), start_point.y(),
                                           end_point.x(), end_point.y())
                self.scene().addItem(self.scale_line_item)

    # ResultsViewWidget fallback with required signals
    class ResultsViewWidget(QWidget):
        selection_changed = Signal(set)
        delete_requested = Signal(set)

        def __init__(self, parent=None):
            super().__init__(parent)
            self.selected_df_indices = set()
            layout = QVBoxLayout(self)
            self.tree_model = QStandardItemModel()
            self.result_tree = QTreeView()
            self.result_tree.setModel(self.tree_model)
            layout.addWidget(self.result_tree)

            # Add delete button
            self.delete_button = QPushButton("Delete Selected")
            self.delete_button.clicked.connect(lambda: self.delete_requested.emit(self.selected_df_indices))
            layout.addWidget(self.delete_button)

        def populate(self, df):
            # Basic implementation to populate the tree view
            self.tree_model.clear()
            if df is not None and not df.empty:
                self.tree_model.setHorizontalHeaderLabels(df.columns)

        def set_dataframe(self, df):
            """Sets the dataframe and populates the tree view.

            This is a convenience method that combines setting the dataframe and populating the tree view.

            Args:
                df: The dataframe to set
            """
            self.populate(df)

        def get_selected_indices(self):
            return self.selected_df_indices

        def set_selected_indices(self, indices):
            """Sets the selected indices.

            Args:
                indices: Set of indices to select
            """
            self.selected_df_indices = indices
            self.update_delete_button_state()
            self.selection_changed.emit(self.selected_df_indices)

        def update_delete_button_state(self):
            # Enable/disable delete button based on selection
            if hasattr(self, 'delete_button'):
                self.delete_button.setEnabled(len(self.selected_df_indices) > 0)

        def clear(self):
            """Clears the results view and resets selection."""
            self.tree_model.clear()
            self.selected_df_indices = set()
            self.update_delete_button_state()

    # Parameter widget fallbacks
    class BaseParameterWidget(QWidget):
        parameters_changed = Signal()

        def __init__(self, parent=None):
            super().__init__(parent)
            layout = QVBoxLayout(self)
            layout.addWidget(QLabel("Parameters not available - import failed"))

    class FastSAMParameterWidget(BaseParameterWidget):
        def __init__(self, parent=None):
            super().__init__(parent)

    class MobileSAMParameterWidget(BaseParameterWidget):
        def __init__(self, parent=None):
            super().__init__(parent)

    # CustomPixmapItem fallback
    class CustomPixmapItem(QGraphicsPixmapItem):
        def __init__(self, pixmap, parent=None):
            super().__init__(pixmap, parent)
            self.setTransformationMode(Qt.SmoothTransformation)

    # Worker class fallbacks
    class ProcessingWorker(QObject):
        finished = Signal(object, object, object)
        progress = Signal(int)
        error = Signal(str)

        def __init__(self):
            super().__init__()

    class PatchProcessingWorker(QObject):
        finished = Signal(object, object, object)
        progress = Signal(int)
        error = Signal(str)

        def __init__(self):
            super().__init__()

    # Dialog function fallbacks
    def show_plot_selection_dialog(*args, **kwargs):
        QMessageBox.information(None, "Import Error", "Plot selection dialog not available due to import error")
        return None

    def show_crop_dialog(*args, **kwargs):
        QMessageBox.information(None, "Import Error", "Crop dialog not available due to import error")
        return None

    def show_about_dialog(*args, **kwargs): pass

    def show_patch_config_dialog(*args, **kwargs):
        QMessageBox.information(None, "Import Error", "Patch configuration dialog not available due to import error")
        return None

    # Utility function fallbacks
    def pil_image_to_qimage(pil_image):
        if pil_image is None:
            return None
        # Basic conversion from PIL to QImage
        data = pil_image.tobytes("raw", "RGBA")
        return QImage(data, pil_image.width, pil_image.height, QImage.Format_RGBA8888)

    def resource_path(p):
        return p  # Basic fallback

    # Constants
    DEFAULT_CONTOUR_THICKNESS = 1

# --- Core Logic Imports from GrainSight Components ---
try:
    from src.grainsight_components.core.analysis import calculate_parameters
    from src.grainsight_components.core.image_utils import create_segmented_visualization
    from src.grainsight_components.core.io import generate_coco_dict
except ImportError as e:
    print(f"ERROR importing GrainSight core components: {e}. Check relative paths.")
    def calculate_parameters(*args, **kwargs): return None, None
    def create_segmented_visualization(*args, **kwargs): return None
    def generate_coco_dict(*args, **kwargs): return None

logger = logging.getLogger(__name__) # Use standard logging

class GrainAnalysisWidget(QWidget):
    # Signals specific to this widget if needed for communication *up* to VisionLabAiApp
    # For now, most signals are internal or handled by workers
    # Signal to safely handle model loading between threads
    model_loaded_signal = Signal(object, str)  # Emits (model_object, model_type_str)
    model_load_failed_signal = Signal(str)     # Emits (error_message)
    # Signal for progress updates from worker threads
    progress_update_signal = Signal(int)       # Emits progress value (0-100)
    # Signal for showing error messages from worker threads
    error_message_signal = Signal(str, str)    # Emits (title, message)

    def __init__(self, parent=None):
        super().__init__(parent)
        logger.info("Initializing GrainAnalysisWidget...")

        # Update slider labels when the widget is shown
        self.showEvent = self.on_show_event

        # --- State Variables (Specific to this widget) ---
        self.grain_uploaded_image: Optional[Image.Image] = None # Original PIL Image
        self.grain_processed_image_vis: Optional[Image.Image] = None # PIL Image with visualization (contours)
        self.grain_annotations: Optional[Union[list, torch.Tensor]] = None # List/Tensor of valid masks
        self.grain_df: Optional[pd.DataFrame] = None # Pandas DataFrame with results
        self.grain_current_scale_factor: Optional[float] = None # Âµm per pixel
        self.grain_original_pixel_length: Optional[float] = None # Pixel length of scale line on original image
        self.grain_image_file_path: Optional[str] = None # Path to the loaded image
        self.grain_image_filename: Optional[str] = None # Filename of the loaded image
        self.grain_last_save_dir: str = os.path.expanduser("~") # Default save dir
        self.grain_default_save_dir: Optional[str] = None # User-defined default save directory
        self.polygons_loaded: bool = False # Flag to track if polygons are currently loaded

        # Project reference (set by the main app when switching to this page)
        self.project = None # Reference to the current project (GrainSightProject or Project)

        self.grain_items: Dict[Any, Dict[str, Union[QGraphicsPolygonItem, QGraphicsTextItem]]] = {} # Mapping: df_index -> {'poly': QGraphicsPolygonItem, 'text': QGraphicsTextItem}

        # State tracking for efficient saving
        self.state_modified: bool = False  # Flag to track if current image state has been modified
        self.images_needing_save: set = set()  # Set of image paths that need saving

        self.grain_processing_thread: Optional[QThread] = None
        self.grain_processing_worker: Optional[ProcessingWorker] = None
        self.grain_patch_processing_thread: Optional[QThread] = None
        self.grain_patch_processing_worker: Optional[PatchProcessingWorker] = None

        self.grain_pixmap_item: Optional[CustomPixmapItem] = None # QGraphicsPixmapItem displaying the image

        self.grain_plot_dialogs: List[PlotDisplayDialog] = [] # Track open plot dialogs

        self.grain_model: Optional[object] = None # Loaded model (YOLO or base SAM)
        self.grain_model_type: Optional[str] = None # Track the currently loaded model type ('fastsam' or 'mobilesam')
        self.grain_device: torch.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        logger.info(f"Grain Analysis using device: {self.grain_device}")

        # --- Image Lists for Multi-Image Support ---
        self.grain_images = []  # List of PIL Image objects
        self.grain_image_filenames = []  # List of image filenames
        self.grain_image_file_paths = []  # List of full image paths
        self.grain_image_states = {}  # Dictionary to store analysis states for each image

        # --- UI Setup ---
        # Use a main layout for this widget
        main_layout = QHBoxLayout(self)
        self.setup_grain_widgets(main_layout) # Setup the internal UI

        # --- Connect Signals/Slots ---
        self.view.scale_line_drawn.connect(self.on_scale_line_drawn)
        self.view.scene_clicked.connect(self.on_scene_clicked)

        # Connect gallery signals if available
        if hasattr(self, 'grain_gallery'):
            self.grain_gallery.image_clicked.connect(self.on_gallery_image_clicked)

        self.view.zoom_changed.connect(self.on_zoom_changed)

        self.results_widget.selection_changed.connect(self.on_results_view_selection_changed)
        self.results_widget.delete_requested.connect(self.delete_selected_grains)

        # Connect Model Loading Signals
        self.model_loaded_signal.connect(self._handle_model_loaded)
        self.model_load_failed_signal.connect(self._handle_model_load_failed)
        # Connect progress and error signals
        self.progress_update_signal.connect(self.update_progress)
        self.error_message_signal.connect(self._show_error_message)

        # Update all slider labels initially
        QTimer.singleShot(100, self.update_all_slider_labels)

        # --- Model Loading ---
        self.load_model_async() # Start loading model in background

        # --- Final UI State ---
        self.update_action_states()
        logger.info("GrainAnalysisWidget initialization complete.")

    # --- UI Setup Method (Adapted from GrainSightApp) ---
    def setup_grain_widgets(self, main_layout: QHBoxLayout):
        # Main container (Splitter)
        main_container = QSplitter(Qt.Horizontal, self)
        main_layout.addWidget(main_container) # Add splitter to the widget's layout

        # --- Left Panel (Controls) ---
        left_frame = QFrame()
        left_layout = QVBoxLayout(left_frame)
        # Increase minimum width and maximum width for more space
        left_frame.setMinimumWidth(300)
        left_frame.setMaximumWidth(450)
        # Make layout more compact by reducing margins
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(5)
        main_container.addWidget(left_frame)

        # --- Image Gallery Group ---
        gallery_group = QGroupBox("Image Gallery")
        gallery_layout = QVBoxLayout(gallery_group)
        # set max height for gallery
        gallery_group.setMaximumHeight(200)
        gallery_layout.setContentsMargins(5, 5, 5, 5)  # Reduce margins
        gallery_layout.setSpacing(2)  # Reduce spacing between elements

        # Create the grain analysis gallery
        self.grain_gallery = GrainAnalysisGallery()
        self.grain_gallery.image_clicked.connect(self.on_gallery_image_clicked)
        gallery_layout.addWidget(self.grain_gallery)

        # Add gallery group to left panel
        left_layout.addWidget(gallery_group)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        control_widget_container = QWidget() # Widget inside scroll area
        control_layout = QVBoxLayout(control_widget_container)
        # Make control layout more compact
        control_layout.setContentsMargins(5, 5, 5, 5)
        control_layout.setSpacing(5)
        scroll_area.setWidget(control_widget_container)
        left_layout.addWidget(scroll_area)

        # --- Top Toolbar Controls (Adapted into layout) ---
        toolbar_layout = QHBoxLayout()
        # Make toolbar more compact
        toolbar_layout.setContentsMargins(0, 0, 0, 0)
        toolbar_layout.setSpacing(3)

        # Create a grid layout for buttons to make them more compact
        buttons_grid = QGridLayout()
        buttons_grid.setContentsMargins(0, 0, 0, 0)
        buttons_grid.setSpacing(3)

        # First row of buttons
        # upload_btn removed - images are now only loaded from project hub

        crop_btn = QPushButton(QIcon(resource_path("icons/crop.png")), "Crop")
        crop_btn.setToolTip("Crop Image...")
        crop_btn.clicked.connect(self.crop_image)
        self.crop_action_button = crop_btn # Keep ref to enable/disable
        buttons_grid.addWidget(crop_btn, 0, 0)

        # Second row of buttons
        save_results_btn = QPushButton(QIcon(resource_path("icons/save.png")), "Save")
        save_results_btn.setToolTip("Save Results (CSV+Image)...")
        save_results_btn.clicked.connect(self.save_results)
        self.save_results_action_button = save_results_btn
        buttons_grid.addWidget(save_results_btn, 0, 1)

        export_coco_btn = QPushButton(QIcon(resource_path("icons/coco.png")), "Export")
        export_coco_btn.setToolTip("Export COCO Annotations...")
        export_coco_btn.clicked.connect(self.save_coco_annotations)
        self.export_coco_action_button = export_coco_btn
        buttons_grid.addWidget(export_coco_btn, 0, 2)

        # Second row of buttons
        plot_btn = QPushButton(QIcon(resource_path("icons/plot.png")), "Plot")
        plot_btn.setToolTip("Generate Plots...")
        plot_btn.clicked.connect(self.show_plotting_dialog)
        self.plot_action_button = plot_btn
        buttons_grid.addWidget(plot_btn, 1, 0)

        reset_btn = QPushButton(QIcon(resource_path("icons/reset.png")), "Reset")
        reset_btn.setToolTip("Reset State")
        reset_btn.clicked.connect(self.reset_grain_analysis) # Use specific reset
        self.reset_action_button = reset_btn
        buttons_grid.addWidget(reset_btn, 1, 1)

        # Add the buttons grid to the toolbar layout
        toolbar_layout.addLayout(buttons_grid)
        toolbar_layout.addStretch()
        control_layout.addLayout(toolbar_layout) # Add toolbar buttons at the top

        # --- Model Type Selection ---
        model_type_group = QGroupBox("Segmentation Model")
        model_type_layout = QHBoxLayout(model_type_group)
        self.fastsam_radio = QRadioButton("FastSAM")
        self.mobilesam_radio = QRadioButton("MobileSAM")
        if not MOBILE_SAM_AVAILABLE:
            self.mobilesam_radio.setEnabled(False)
            self.mobilesam_radio.setToolTip("MobileSAM library not detected.")
        self.fastsam_radio.setChecked(True) # Default
        model_type_layout.addWidget(self.fastsam_radio)
        model_type_layout.addWidget(self.mobilesam_radio)
        control_layout.addWidget(model_type_group)
        self.fastsam_radio.toggled.connect(self.on_model_type_changed)

        # --- Model Parameters Stack ---
        param_group = QGroupBox("Model Parameters")
        param_group_layout = QVBoxLayout(param_group)
        control_layout.addWidget(param_group)
        self.model_params_stack = QStackedWidget()
        param_group_layout.addWidget(self.model_params_stack)
        self.fastsam_params_widget = FastSAMParameterWidget()
        self.mobilesam_params_widget = MobileSAMParameterWidget()
        self.model_params_stack.addWidget(self.fastsam_params_widget)
        self.model_params_stack.addWidget(self.mobilesam_params_widget)
        if not MOBILE_SAM_AVAILABLE:
            # Ensure mobile sam widget is disabled if lib not present
             self.mobilesam_params_widget.setEnabled(False)

        # --- Intelligent Artifact Handling Group ---
        artifact_group = QGroupBox("Intelligent Artifact Handling")
        artifact_layout = QVBoxLayout(artifact_group)
        control_layout.addWidget(artifact_group)

        # Preset selection
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("Preset:"))
        self.preset_combo = QComboBox()
        self.preset_combo.addItems(["Conservative", "Balanced", "Aggressive"])
        self.preset_combo.setCurrentIndex(1)  # Default to Balanced
        self.preset_combo.setToolTip("Conservative: Keeps more grains, may include artifacts\nBalanced: Good balance for most images\nAggressive: Removes more artifacts, may remove some valid grains")
        preset_layout.addWidget(self.preset_combo)
        artifact_layout.addLayout(preset_layout)

        # Artifact removal sensitivity slider
        sensitivity_layout = QHBoxLayout()
        sensitivity_layout.addWidget(QLabel("Artifact Sensitivity:"))
        self.artifact_sensitivity_slider = QSlider(Qt.Horizontal)
        self.artifact_sensitivity_slider.setRange(0, 100)  # 0-100 scale (0.0-1.0)
        self.artifact_sensitivity_slider.setValue(50)  # Default middle value (0.5)
        self.artifact_sensitivity_slider.setTickInterval(10)
        self.artifact_sensitivity_slider.setTickPosition(QSlider.TicksBelow)
        self.artifact_sensitivity_slider.setToolTip("Controls how aggressively artifacts are detected and removed")
        sensitivity_layout.addWidget(self.artifact_sensitivity_slider)
        self.artifact_sensitivity_label = QLabel("0.50")
        self.artifact_sensitivity_label.setMinimumWidth(40)
        sensitivity_layout.addWidget(self.artifact_sensitivity_label)
        artifact_layout.addLayout(sensitivity_layout)

        # Connect the sensitivity slider to update its label
        self.artifact_sensitivity_slider.valueChanged.connect(
            lambda value: self.artifact_sensitivity_label.setText(f"{value/100.0:.2f}")
        )

        # Duplicate detection sensitivity slider
        duplicate_layout = QHBoxLayout()
        duplicate_layout.addWidget(QLabel("Duplicate Sensitivity:"))
        self.duplicate_sensitivity_slider = QSlider(Qt.Horizontal)
        self.duplicate_sensitivity_slider.setRange(0, 100)  # 0-100 scale (0.0-1.0)
        self.duplicate_sensitivity_slider.setValue(70)  # Default value (0.7)
        self.duplicate_sensitivity_slider.setTickInterval(10)
        self.duplicate_sensitivity_slider.setTickPosition(QSlider.TicksBelow)
        self.duplicate_sensitivity_slider.setToolTip("Controls how aggressively duplicate grains are detected and removed")
        duplicate_layout.addWidget(self.duplicate_sensitivity_slider)
        self.duplicate_sensitivity_label = QLabel("0.70")
        self.duplicate_sensitivity_label.setMinimumWidth(40)
        duplicate_layout.addWidget(self.duplicate_sensitivity_label)
        artifact_layout.addLayout(duplicate_layout)

        # Connect the duplicate slider to update its label
        self.duplicate_sensitivity_slider.valueChanged.connect(
            lambda value: self.duplicate_sensitivity_label.setText(f"{value/100.0:.2f}")
        )

        # Note: Advanced performance settings removed for better performance
        # Using optimized defaults: timeout=60s, batch_size=100, max_pairs=10000

        # Connect preset combo to update settings
        self.preset_combo.currentIndexChanged.connect(self.apply_artifact_preset)

        # Help text
        help_text = QLabel("Our intelligent artifact handling system automatically detects and fixes artifacts "
                          "at patch boundaries. Higher artifact sensitivity removes more artifacts but may affect valid grains. "
                          "Higher duplicate sensitivity removes more partial grains at patch boundaries.")
        help_text.setWordWrap(True)
        artifact_layout.addWidget(help_text)

        # --- Scale Calculation Group ---
        scale_group = QGroupBox("Image Scale")
        scale_layout = QVBoxLayout(scale_group)
        control_layout.addWidget(scale_group)
        # (Scale controls setup copied from GrainSightApp main_window)
        scale_layout.addWidget(QLabel("<b>Method 1: Draw Scale Line</b>"))
        scale_instruct_layout = QHBoxLayout()
        scale_instruct_layout.addWidget(QLabel("Real-world length (µm):"))
        self.real_world_length_edit = QLineEdit("100")
        self.real_world_length_edit.setValidator(QDoubleValidator(0.01, 1000000.0, 3))
        scale_instruct_layout.addWidget(self.real_world_length_edit)
        scale_layout.addLayout(scale_instruct_layout)
        # Create scale mode button with improved styling for better visual feedback
        scale_mode_button = QPushButton(QIcon(resource_path("icons/scale.png")), " Draw Scale Line")
        scale_mode_button.setToolTip("Activate scale drawing mode")
        scale_mode_button.setCheckable(True)
        scale_mode_button.clicked.connect(self._toggle_scale_mode)

        # Set a stylesheet for the button to show active state more clearly
        # Using more subtle colors that work well with both light and dark themes
        scale_mode_button.setStyleSheet("""
            QPushButton:checked {
                background-color: rgba(255, 0, 0, 0.2);
                border: 2px solid rgba(255, 0, 0, 0.5);
                font-weight: bold;
                color: rgba(255, 255, 255, 0.9);
            }

            /* Dark theme specific adjustments */
            .dark QPushButton:checked {
                background-color: rgba(180, 0, 0, 0.3);
                border: 2px solid rgba(180, 0, 0, 0.6);
                color: rgba(255, 255, 255, 0.9);
            }
        """)

        self.scale_mode_button = scale_mode_button # Store reference
        scale_layout.addWidget(scale_mode_button)

        scale_layout.addWidget(QLabel("<hr><b>Method 2: Enter Scale Directly</b>"))
        manual_scale_layout = QHBoxLayout()
        manual_scale_layout.addWidget(QLabel("Scale (µm/pixel):"))
        self.manual_scale_edit = QLineEdit("0.0")
        self.manual_scale_edit.setValidator(QDoubleValidator(0.000001, 1000.0, 6))
        self.manual_scale_edit.setFixedWidth(120)
        manual_scale_layout.addWidget(self.manual_scale_edit)
        set_scale_button = QPushButton("Set")
        set_scale_button.setObjectName("setManualScaleButton")
        set_scale_button.setToolTip("Apply the manually entered scale factor")
        set_scale_button.clicked.connect(self.set_manual_scale)
        manual_scale_layout.addWidget(set_scale_button)
        manual_scale_layout.addStretch()
        scale_layout.addLayout(manual_scale_layout)

        self.scale_factor_label = QLabel("<b>Current Scale: Not set</b>")
        self.scale_factor_label.setStyleSheet("padding-top: 5px;")
        scale_layout.addWidget(self.scale_factor_label)

        # Scale Save/Load/Reset Buttons
        scale_actions_layout = QHBoxLayout()
        save_scale_btn = QPushButton(QIcon(resource_path("icons/save_scale.png")), "Save")
        save_scale_btn.setToolTip("Save Scale")
        save_scale_btn.clicked.connect(self.save_scale_factor)
        self.save_scale_action_button = save_scale_btn
        load_scale_btn = QPushButton(QIcon(resource_path("icons/load_scale.png")), "Load")
        load_scale_btn.setToolTip("Load Scale")
        load_scale_btn.clicked.connect(self.load_scale_factor)
        self.load_scale_action_button = load_scale_btn
        reset_scale_btn = QPushButton(QIcon(resource_path("icons/reset_scale.png")), "Reset")
        reset_scale_btn.setToolTip("Reset Scale")
        reset_scale_btn.clicked.connect(self.reset_scale)
        self.reset_scale_action_button = reset_scale_btn
        scale_actions_layout.addWidget(save_scale_btn)
        scale_actions_layout.addWidget(load_scale_btn)
        scale_actions_layout.addWidget(reset_scale_btn)
        scale_actions_layout.addStretch()
        scale_layout.addLayout(scale_actions_layout)


        # --- Action Buttons ---
        action_layout = QVBoxLayout()
        control_layout.addLayout(action_layout)
        # Make Segment in Patches the default button with focus and styling
        self.process_button = QPushButton(QIcon(resource_path("icons/grid.png")), " Segment in Patches")
        self.process_button.setToolTip("Segment large images using patches (default method)")
        self.process_button.clicked.connect(self.analyze_with_patches)
        self.process_button.setDefault(True)  # Make it the default button
        self.process_button.setFocus()  # Give it focus
        # Add some styling to make it stand out
        self.process_button.setStyleSheet("QPushButton { font-weight: bold; background-color: #021a42: white; }")
        action_layout.addWidget(self.process_button)

        # Add Show/Hide Visualization button
        self.reload_results_button = QPushButton(QIcon(resource_path("icons/reload.png")), " Show Grain Visualization")
        self.reload_results_button.setToolTip("Toggle interactive grain visualization")
        self.reload_results_button.clicked.connect(self.toggle_grain_visualization)
        action_layout.addWidget(self.reload_results_button)

        self.recalculate_button = QPushButton(QIcon(resource_path("icons/recalculate.png")), " Recalculate Parameters")
        self.recalculate_button.setToolTip("Recalculate parameters using current scale")
        self.recalculate_button.clicked.connect(self.recalculate_parameters_with_new_scale)
        action_layout.addWidget(self.recalculate_button)

        # Add measurement type selection for statistics
        self.measurement_type_group = QGroupBox("Grain Size Measurement Type")
        measurement_type_layout = QVBoxLayout(self.measurement_type_group)

        self.ecd_radio = QRadioButton("Equivalent Circular Diameter (ECD)")
        self.ecd_radio.setToolTip("Use the diameter of a circle with the same area as the grain")
        self.ecd_radio.setChecked(True)  # Default option

        self.length_radio = QRadioButton("Length (Longest Feret Diameter)")
        self.length_radio.setToolTip("Use the longest dimension of the grain")

        measurement_type_layout.addWidget(self.ecd_radio)
        measurement_type_layout.addWidget(self.length_radio)
        action_layout.addWidget(self.measurement_type_group)

        # Add Generate Statistical Reports button
        self.stats_button = QPushButton(QIcon(resource_path("icons/stats.png")), " Generate Statistical Reports")
        self.stats_button.setToolTip("Generate detailed grain size statistical reports")
        self.stats_button.clicked.connect(self.show_grain_statistics)
        action_layout.addWidget(self.stats_button)

        # Add manual save/load buttons for better performance
        save_load_layout = QHBoxLayout()

        self.save_results_button = QPushButton(QIcon(resource_path("icons/save.png")), " Save Current Results")
        self.save_results_button.setToolTip("Save current analysis results to a file")
        self.save_results_button.clicked.connect(self.save_current_results)
        save_load_layout.addWidget(self.save_results_button)

        self.load_results_button = QPushButton(QIcon(resource_path("icons/load.png")), " Load Saved Results")
        self.load_results_button.setToolTip("Load previously saved analysis results from a file")
        self.load_results_button.clicked.connect(self.load_saved_results)
        save_load_layout.addWidget(self.load_results_button)

        action_layout.addLayout(save_load_layout)

        control_layout.addStretch()

        # --- Right Panel (Image and Results) ---
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)
        main_container.addWidget(right_frame)

        # Image Display (Custom Graphics View)
        self.view = CustomGraphicsView(self)
        self.scene = QGraphicsScene(self)
        self.view.setScene(self.scene)
        # Let parent app set theme/background
        # bg_color = self.palette().color(QPalette.ColorRole.Base)
        # self.view.setBackgroundBrush(bg_color)
        right_layout.addWidget(self.view, 5) # Stretch factor for view

        # Status/Reminder Label (Simplified)
        self.status_label = QLabel("Load image and set scale.")
        self.status_label.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(self.status_label)

        # Progress Bar
        self.status_progress = QProgressBar()
        self.status_progress.setMaximumSize(200, 20)  # Increased size for better visibility
        self.status_progress.setMinimumHeight(20)  # Ensure minimum height
        self.status_progress.setTextVisible(True)  # Show percentage text
        self.status_progress.setStyleSheet("QProgressBar { min-height: 20px; font-weight: bold; }")
        self.status_progress.hide() # Hide initially
        # Add progress bar to a layout below the view? Or rely on main app status bar?
        # Let's add it here for self-containment
        progress_layout = QHBoxLayout()
        progress_layout.addStretch()
        progress_layout.addWidget(self.status_progress)
        right_layout.addLayout(progress_layout)


        # Results Display (ResultsViewWidget)
        self.results_widget = ResultsViewWidget(self)
        right_layout.addWidget(self.results_widget, 3) # Stretch factor for results

        # --- Splitter Sizes ---
        # Needs to be called after widget is shown, maybe via parent?
        # QTimer.singleShot(100, lambda: main_container.setSizes([int(self.width() * 0.3), int(self.width() * 0.7)]))

        # Connect mode radio buttons to view now that it's initialized
        # Simplified mode buttons for widget context
        mode_layout = QHBoxLayout()
        self.select_mode_button = QPushButton(QIcon(resource_path("icons/select.png")), " Select")
        self.select_mode_button.setToolTip("Select grains")
        self.select_mode_button.setCheckable(True)
        self.select_mode_button.setChecked(True) # Default mode
        self.pan_mode_button = QPushButton(QIcon(resource_path("icons/pan.png")), " Pan")
        self.pan_mode_button.setToolTip("Pan image")
        self.pan_mode_button.setCheckable(True)

        self.select_mode_button.clicked.connect(self._toggle_select_mode)
        self.pan_mode_button.clicked.connect(self._toggle_pan_mode)
        # Scale mode is toggled by its dedicated button

        mode_layout.addWidget(self.select_mode_button)
        mode_layout.addWidget(self.pan_mode_button)
        mode_layout.addStretch()
        left_layout.insertLayout(0, mode_layout) # Add mode buttons at the top of left panel

    # --- Mode Toggling Slots with Improved Error Handling ---
    def _toggle_scale_mode(self, checked):
        """
        Toggle scale drawing mode with improved error handling and visual feedback.
        This method has been enhanced to ensure the scale mode can be properly deactivated
        and provides better visual feedback for dark themes.

        Args:
            checked: Whether the scale mode button is checked
        """
        try:
            logger.debug(f"Scale mode toggle: checked={checked}, current mode={self.view.mode}")

            if checked:
                # Activate scale mode
                self.view.set_mode(CustomGraphicsView.MODE_SCALE)
                # Update UI to reflect scale mode is active
                self.select_mode_button.setChecked(False)
                self.pan_mode_button.setChecked(False)
                # Update status bar to provide user feedback
                self.update_status("Scale drawing mode active. Click and drag to draw a scale line.")

                # Set cursor to crosshair for better visual feedback
                self.view.setCursor(Qt.CrossCursor)

                # Add a subtle highlight to the status label to indicate scale mode is active
                self.status_label.setStyleSheet("color: rgba(255, 100, 100, 0.9); font-weight: bold;")
            else:
                # Only switch to select mode if we're currently in scale mode
                # This prevents issues when clicking the button while it's already inactive
                if self.view.mode == CustomGraphicsView.MODE_SCALE:
                    # Force switch to selection mode
                    self.view.set_mode(CustomGraphicsView.MODE_SELECTION)
                    self.select_mode_button.setChecked(True)
                    # Update status bar
                    self.update_status("Scale drawing mode deactivated.")

                    # Reset cursor
                    self.view.setCursor(Qt.ArrowCursor)

                    # Reset status label style
                    self.status_label.setStyleSheet("")

            # Force update the button state to match the actual mode
            # This ensures the button state is always consistent with the actual mode
            self.scale_mode_button.setChecked(self.view.mode == CustomGraphicsView.MODE_SCALE)

        except Exception as e:
            # Log error but don't crash
            logger.error(f"Error toggling scale mode: {e}")
            # Reset to a safe state
            self.view.set_mode(CustomGraphicsView.MODE_SELECTION)
            self.scale_mode_button.setChecked(False)
            self.select_mode_button.setChecked(True)
            self.view.setCursor(Qt.ArrowCursor)
            self.status_label.setStyleSheet("")
            self.update_status("Error toggling scale mode. Reverted to selection mode.")

    def _toggle_select_mode(self, checked):
        """Toggle selection mode with improved error handling."""
        try:
            if checked:
                # Activate selection mode
                self.view.set_mode(CustomGraphicsView.MODE_SELECTION)
                # Update UI
                self.scale_mode_button.setChecked(False)
                self.pan_mode_button.setChecked(False)
                # Update status
                self.update_status("Selection mode active.")
            elif not self.scale_mode_button.isChecked() and not self.pan_mode_button.isChecked():
                # Ensure one mode is always active
                self.select_mode_button.setChecked(True)
                self.view.set_mode(CustomGraphicsView.MODE_SELECTION)
        except Exception as e:
            # Log error but don't crash
            logger.error(f"Error toggling select mode: {e}")
            # Reset to a safe state
            self.view.set_mode(CustomGraphicsView.MODE_SELECTION)
            self.select_mode_button.setChecked(True)
            self.scale_mode_button.setChecked(False)
            self.pan_mode_button.setChecked(False)

    def _toggle_pan_mode(self, checked):
        """Toggle pan mode with improved error handling."""
        try:
            if checked:
                # Activate pan mode
                self.view.set_mode(CustomGraphicsView.MODE_PAN)
                # Update UI
                self.scale_mode_button.setChecked(False)
                self.select_mode_button.setChecked(False)
                # Update status
                self.update_status("Pan mode active. Click and drag to pan the image.")
            elif not self.scale_mode_button.isChecked() and not self.select_mode_button.isChecked():
                # Revert to select if others off
                self.select_mode_button.setChecked(True)
                self.view.set_mode(CustomGraphicsView.MODE_SELECTION)
        except Exception as e:
            # Log error but don't crash
            logger.error(f"Error toggling pan mode: {e}")
            # Reset to a safe state
            self.view.set_mode(CustomGraphicsView.MODE_SELECTION)
            self.select_mode_button.setChecked(True)
            self.scale_mode_button.setChecked(False)
            self.pan_mode_button.setChecked(False)


    # --- Adapted Methods from GrainSightApp ---
    # (Copy necessary methods here, renaming state variables like self.uploaded_image to self.grain_uploaded_image)
    # --- Model Loading Methods ---
    def load_model_async(self):
        if self.grain_model is not None: return logger.info("Grain model already loaded.")
        self.update_status("Loading grain analysis model...")
        self.update_action_states(processing=True)
        thread = threading.Thread(target=self._load_model_worker, daemon=True)
        thread.start()

    def on_model_type_changed(self):
        selected_type = 'fastsam' if self.fastsam_radio.isChecked() else 'mobilesam'
        logger.debug(f"Grain model type selection changed to: {selected_type}")
        self.model_params_stack.setCurrentWidget(
            self.fastsam_params_widget if selected_type == 'fastsam' else self.mobilesam_params_widget
        )
        if self.grain_model is not None and selected_type != self.grain_model_type:
            reply = QMessageBox.question(
                self, "Change Grain Model", f"Unload {self.grain_model_type.upper()} and load {selected_type.upper()}?",
                QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Yes)
            if reply == QMessageBox.Yes:
                self.grain_model = None
                self.grain_model_type = None
                self.load_model_async()
            else:
                self._update_model_radio_buttons(self.grain_model_type == 'fastsam')
        elif self.grain_model is None:
             logger.info("Model type selection changed, but no model loaded yet.")

    def _load_model_worker(self):
        model_to_load = None
        loaded_type = None
        error_msg = None
        use_fastsam = False
        try:
            use_fastsam = QMetaObject.invokeMethod(
                self, "_get_fastsam_selection", Qt.BlockingQueuedConnection, Q_RETURN_ARG(bool))

            # Determine base path relative to *this* file's location
            component_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # Should be src/grainsight_components

            if use_fastsam:
                model_dir = os.path.join(component_root, 'models')
                model_path = os.path.join(model_dir, "FastSAM-x.pt")
                if not os.path.exists(model_path):
                    os.makedirs(model_dir, exist_ok=True)
                    error_msg = f"FastSAM model not found: {model_path}\nDownload from FastSAM releases."
                else:
                    logger.info(f"Loading FastSAM model from: {model_path}")
                    try:
                        model_to_load = YOLO(model_path) # Load on CPU initially
                        loaded_type = "fastsam"
                        logger.info("FastSAM loaded (CPU).")
                    except Exception as e: error_msg = f"Failed to load FastSAM: {e}"
            else: # MobileSAM
                 if not MOBILE_SAM_AVAILABLE: error_msg = "MobileSAM library not installed."
                 else:
                      weights_dir = os.path.join(component_root, 'weights')
                      model_path = os.path.join(weights_dir, "mobile_sam.pt")
                      if not os.path.exists(model_path):
                           os.makedirs(weights_dir, exist_ok=True)
                           error_msg = f"MobileSAM weights not found: {model_path}\nDownload from MobileSAM repo."
                      else:
                           logger.info(f"Loading MobileSAM base model from: {model_path}")
                           try:
                               # Get the sam_model_registry from our wrapper
                               sam_model_registry_obj = get_sam_model_registry()
                               if sam_model_registry_obj is None:
                                   error_msg = "Failed to get sam_model_registry from wrapper"
                                   logger.error(error_msg)
                               else:
                                   # Load base SAM model on CPU
                                   sam = sam_model_registry_obj["vit_t"](checkpoint=model_path)
                                   model_to_load = sam
                                   loaded_type = "mobilesam"
                                   logger.info("MobileSAM loaded (CPU).")
                           except Exception as e: error_msg = f"Failed to load MobileSAM: {e}"
        except Exception as e: error_msg = f"Model loading failed: {e}"

        if error_msg: self.model_load_failed_signal.emit(error_msg)
        elif model_to_load and loaded_type: self.model_loaded_signal.emit(model_to_load, loaded_type)
        else: self.model_load_failed_signal.emit("Unknown model loading error.")

    @Slot(result=bool)
    def _get_fastsam_selection(self) -> bool:
        return self.fastsam_radio.isChecked()

    @Slot(object, str)
    def _handle_model_loaded(self, loaded_model, loaded_type):
        self.grain_model = loaded_model
        self.grain_model_type = loaded_type
        status = f"{self.grain_model_type.upper()} model loaded. Ready."
        self.update_status(status)
        self._update_model_radio_buttons(self.grain_model_type == 'fastsam')
        self.update_action_states()

    @Slot(str)
    def _handle_model_load_failed(self, error_message):
        self.grain_model = None
        self.grain_model_type = None
        self._show_error_message("Grain Model Load Error", error_message)
        self.update_status(f"Error loading grain model: {error_message[:50]}...")
        self._update_model_radio_buttons(self.fastsam_radio.isChecked())
        self.update_action_states()

    @Slot(bool)
    def _update_model_radio_buttons(self, use_fastsam: bool):
        self.fastsam_radio.blockSignals(True)
        self.mobilesam_radio.blockSignals(True)
        self.fastsam_radio.setChecked(use_fastsam)
        self.mobilesam_radio.setChecked(not use_fastsam)
        self.model_params_stack.setCurrentWidget(
            self.fastsam_params_widget if use_fastsam else self.mobilesam_params_widget)
        self.fastsam_radio.blockSignals(False)
        self.mobilesam_radio.blockSignals(False)

    # --- Image Handling ---
    def load_image_list(self, image_paths, image_infos=None):
        """Load a list of images from file paths. This method is called from the Project Hub.

        Args:
            image_paths: List of file paths to load
            image_infos: Optional list of image info objects
        """
        if not image_paths:
            logger.error("No image paths provided to load_image_list")
            return

        logger.info(f"Loading {len(image_paths)} images into Grain Analysis widget")

        # Check if we have a gallery to load images into
        if hasattr(self, 'grain_gallery'):
            # Check for existing images in the gallery
            existing_images = len(self.grain_gallery.images) > 0
            existing_paths = set(self.grain_gallery.file_paths) if existing_images else set()

            # Only clear if no existing images
            if not existing_images:
                self.grain_gallery.clear_images()

            # Add new images, skipping duplicates
            added_count = 0
            first_added_index = -1

            for i, file_path in enumerate(image_paths):
                try:
                    # Skip if already loaded
                    if file_path in existing_paths:
                        logger.info(f"Image already loaded: {os.path.basename(file_path)}")
                        continue

                    # Load the image
                    new_image = Image.open(file_path)
                    # Convert to RGB for consistency
                    if new_image.mode != 'RGB':
                        new_image = new_image.convert('RGB')

                    # Add to gallery
                    self.grain_gallery.add_image(new_image, os.path.basename(file_path), file_path)
                    added_count += 1
                    existing_paths.add(file_path)

                    # Track the first added image
                    if first_added_index == -1:
                        first_added_index = len(self.grain_gallery.images) - 1

                    # Update last save directory
                    self.grain_last_save_dir = os.path.dirname(file_path)

                except Exception as e:
                    logger.exception(f"Failed to load grain image: {file_path}")
                    self._show_error_message("Image Load Error", f"Could not load image file: {os.path.basename(file_path)}\nError: {e}")

            # Select first image if we added any
            if first_added_index >= 0:
                self.grain_gallery.select_image(first_added_index)
                self.on_gallery_image_clicked(first_added_index)

            # Show message about loaded images
            if added_count > 0:
                self.update_status(f"Loaded {added_count} new images. Total: {len(self.grain_gallery.images)}")
            else:
                self.update_status("No new images were loaded.")
        else:
            # Fallback to loading just the first image if gallery is not available
            if image_paths:
                self.load_image(image_paths[0])

    def load_image(self, file_path):
        """Load an image from a file path. This method is called from the Project Hub."""
        if not file_path or not os.path.exists(file_path):
            logger.error(f"Failed to load image: {file_path}")
            return

        # Add to gallery if available
        if hasattr(self, 'grain_gallery'):
            try:
                self.update_status(f"Loading grain image: {os.path.basename(file_path)}...")

                new_image = Image.open(file_path)
                # Convert to RGB for consistency
                if new_image.mode != 'RGB': new_image = new_image.convert('RGB')

                # Update last save directory
                self.grain_last_save_dir = os.path.dirname(file_path)

                # Check if the image is already in the gallery by file path
                if file_path in self.grain_gallery.file_paths:
                    # Find the index of the image in the gallery
                    index = self.grain_gallery.file_paths.index(file_path)
                    # Select the image in the gallery
                    self.grain_gallery.select_image(index)
                    # Trigger the gallery image clicked handler
                    self.on_gallery_image_clicked(index)
                else:
                    # Add to gallery
                    self.grain_gallery.add_image(new_image, os.path.basename(file_path), file_path)
                    # Select the newly added image
                    self.grain_gallery.select_image(len(self.grain_gallery.images) - 1)
                    # Trigger the gallery image clicked handler
                    self.on_gallery_image_clicked(len(self.grain_gallery.images) - 1)

                logger.info(f"Grain image loaded: {os.path.basename(file_path)} ({new_image.width}x{new_image.height})")
                self.update_status(f"Grain image '{os.path.basename(file_path)}' loaded. Set scale.")
                self.update_scale_reminder()

            except Exception as e:
                logger.exception(f"Failed to load grain image: {file_path}")
                self._show_error_message("Image Load Error", f"Could not load image file.\nError: {e}")
                self.update_status("Error loading grain image.")
        else:
            # Fallback to old method if gallery is not available
            # Initialize image lists if needed
            if not hasattr(self, 'grain_images') or self.grain_images is None:
                self.grain_images = []
            if not hasattr(self, 'grain_image_filenames') or self.grain_image_filenames is None:
                self.grain_image_filenames = []
            if not hasattr(self, 'grain_image_file_paths') or self.grain_image_file_paths is None:
                self.grain_image_file_paths = []
            if not hasattr(self, 'grain_image_states') or self.grain_image_states is None:
                self.grain_image_states = {}

            # Check if the image is already loaded
            if file_path in self.grain_image_file_paths:
                # Find the index of the image
                index = self.grain_image_file_paths.index(file_path)
                # Select the image
                self.select_grain_image(index)
                return

            try:
                self.update_status(f"Loading grain image: {os.path.basename(file_path)}...")

                new_image = Image.open(file_path)
                # Convert to RGB for consistency
                if new_image.mode != 'RGB': new_image = new_image.convert('RGB')

                # Add to image lists
                self.grain_images.append(new_image)
                self.grain_image_filenames.append(os.path.basename(file_path))
                self.grain_image_file_paths.append(file_path)

                # Get the index of the newly added image
                index = len(self.grain_images) - 1

                # Update last save directory
                self.grain_last_save_dir = os.path.dirname(file_path)

                # Select the newly added image
                self.select_grain_image(index)

                logger.info(f"Grain image loaded: {os.path.basename(file_path)} ({new_image.width}x{new_image.height})")
                self.update_status(f"Grain image '{os.path.basename(file_path)}' loaded. Set scale.")
                self.update_scale_reminder()

            except Exception as e:
                logger.exception(f"Failed to load grain image: {file_path}")
                self._show_error_message("Image Load Error", f"Could not load image file.\nError: {e}")
                self.update_status("Error loading grain image.")

    @Slot(int)
    def on_gallery_image_clicked(self, index):
        """Handler for when an image is clicked in the gallery.

        This method implements an improved image switching approach that:
        1. Properly saves the current state before switching
        2. Efficiently loads the new state
        3. Ensures consistent UI state during transitions
        4. Provides better user feedback with reduced redundant messages
        """
        # Check if we're already processing this image to avoid redundant operations
        if hasattr(self, '_current_loading_index') and self._current_loading_index == index:
            logger.debug(f"Already loading image at index {index}, ignoring redundant click")
            return

        # Set the current loading index to prevent redundant operations
        self._current_loading_index = index

        # Show a loading indicator immediately
        self.status_progress.show()
        self.status_progress.setValue(10)
        # Only log at debug level to reduce log spam
        logger.debug("Loading image...")
        QApplication.processEvents()

        # Save current state if modified before switching images
        if hasattr(self, 'state_modified') and self.state_modified and \
           hasattr(self, 'grain_image_file_path') and self.grain_image_file_path:
            logger.info(f"Saving state for current image before switching: {self.grain_image_file_path}")
            self.save_grain_analysis_state()
            self.status_progress.setValue(20)
            QApplication.processEvents()

        # Mark current image as needing save if it has been modified
        if (hasattr(self, 'grain_image_file_path') and self.grain_image_file_path and
            hasattr(self, 'grain_annotations') and self.grain_annotations is not None and
            hasattr(self, 'state_modified') and self.state_modified):
            # Instead of saving immediately, just track that this image needs saving
            if not hasattr(self, 'images_needing_save'):
                self.images_needing_save = set()
            self.images_needing_save.add(self.grain_image_file_path)
            logger.info(f"Marked image for later saving: {self.grain_image_file_path}")
        else:
            logger.debug("Skipping state tracking - no annotations loaded or no changes made")

        if 0 <= index < len(self.grain_gallery.images):
            # Get the selected image
            image = self.grain_gallery.images[index]
            filename = self.grain_gallery.filenames[index]
            file_path = self.grain_gallery.file_paths[index] if index < len(self.grain_gallery.file_paths) else None

            logger.info(f"Switching to image: {filename}, path: {file_path}")
            # Don't update status here to reduce redundant messages
            self.status_progress.setValue(30)
            QApplication.processEvents()

            # Display the image
            self.display_image(image, filename, file_path)

            # Update UI state
            self.update_action_states()

            # Hide progress indicator when done
            self.status_progress.hide()
            self.update_status(f"Image loaded: {filename}")

        # Clear the current loading index
        self._current_loading_index = None

    def display_image(self, image, filename, file_path=None):
        """Displays an image in the grain analysis widget.

        Args:
            image: The image to display (PIL Image, numpy array, QImage, or QPixmap)
            filename: The filename of the image
            file_path: Optional full path to the image file
        """
        # Start a timer to measure performance
        start_time = time.time()

        # Show progress indicator but log at debug level to reduce log spam
        self.status_progress.show()
        self.status_progress.setValue(10)
        logger.debug(f"Loading image: {filename}...")
        QApplication.processEvents()

        # Store image information
        self.grain_uploaded_image = image
        self.grain_image_filename = filename
        if file_path:
            self.grain_image_file_path = file_path
            # Set current_grain_image_path for compatibility with old code
            self.current_grain_image_path = file_path

        # Update progress
        self.status_progress.setValue(30)
        logger.debug(f"Loading image metadata...")
        QApplication.processEvents()

        # Try to load state from project first if available
        state = None
        image_id = None

        # Use efficient image ID lookup
        if hasattr(self, 'project') and self.project and hasattr(self.project, 'load_grain_analysis_state') and \
           hasattr(self, 'grain_image_file_path') and self.grain_image_file_path:
            # Get image ID using the efficient cached lookup method
            logger.debug(f"Finding image in project...")
            QApplication.processEvents()
            image_id = self._get_image_id_for_path(self.grain_image_file_path)

            # Update progress
            self.status_progress.setValue(50)
            if image_id:
                logger.debug(f"Loading results for image {filename}...")
                QApplication.processEvents()
                # Load state but don't load annotations yet - they'll be loaded on demand when user clicks Show Polygons
                state = self.project.load_grain_analysis_state(image_id, load_annotations=False)
                logger.debug(f"Loaded state from project for image ID: {image_id}")

                # Store the image_id for later use
                self.current_image_id = image_id
            else:
                logger.debug(f"No image ID found for path: {self.grain_image_file_path}")

        # If no state from project, try from memory
        if not state and hasattr(self, 'grain_image_states') and hasattr(self, 'grain_image_file_path') and \
           self.grain_image_file_path in self.grain_image_states:
            logger.debug(f"Loading cached results...")
            QApplication.processEvents()
            state = self.grain_image_states[self.grain_image_file_path]

        # Update progress
        self.status_progress.setValue(70)
        logger.debug(f"Preparing display...")
        QApplication.processEvents()

        # Reset the polygons_loaded flag to ensure polygons are always hidden when switching images
        self.polygons_loaded = False
        self.reload_results_button.setText(" Show Polygons")

        # Load state if available
        if state:
            logger.debug(f"Loading state data...")
            QApplication.processEvents()
            self.load_grain_analysis_state(state)

            # Ensure the button is enabled if we have annotations, regardless of whether they're loaded
            if hasattr(self, 'grain_df') and self.grain_df is not None and not self.grain_df.empty:
                logger.debug("Enabling Show Polygons button because dataframe exists")
                self.reload_results_button.setEnabled(True)

                # Store the image_id for later use when loading annotations
                if image_id:
                    self.current_image_id = image_id
        else:
            # Reset the analysis state but keep the image
            logger.debug(f"Initializing new image...")
            QApplication.processEvents()
            self.reset_grain_analysis_state(clear_image=False)
            self.reload_results_button.setEnabled(False)

        # Update progress
        self.status_progress.setValue(90)
        logger.debug(f"Rendering image...")
        QApplication.processEvents()

        # Display the image on the scene
        self.display_image_on_scene(self.grain_uploaded_image)

        # Calculate and display elapsed time
        elapsed_time = time.time() - start_time

        # Update the status based on whether this image has been segmented
        if hasattr(self, 'grain_df') and self.grain_df is not None and not self.grain_df.empty:
            self.update_status(f"Loaded segmented image: {filename} in {elapsed_time:.2f} seconds. Click 'Show Polygons' to display grain outlines.")
        else:
            self.update_status(f"Loaded image: {filename} in {elapsed_time:.2f} seconds")

    @Slot()
    def upload_image(self):
        """Upload image method - no longer used as upload buttons have been removed.
        Images are now only loaded from the project hub.
        This method is kept for compatibility but should not be called directly anymore.
        """
        # Stop processing if running
        if self.grain_processing_thread and self.grain_processing_thread.isRunning():
            # ... (confirm stop processing) ...
             if QMessageBox.question(self, "Confirm", "Processing ongoing. Stop and load new image?",
                                     QMessageBox.Yes | QMessageBox.Cancel) == QMessageBox.Cancel:
                  return
             self.stop_processing_thread() # Assuming stop methods exist

        # Use getOpenFileNames to allow selecting multiple files
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "Open Image Files", self.grain_last_save_dir,
            "Image Files (*.png *.jpg *.jpeg *.tif *.tiff *.bmp);;All Files (*)",
        )
        if not file_paths: return

        # Initialize image lists if needed
        if not hasattr(self, 'grain_images') or self.grain_images is None:
            self.grain_images = []
        if not hasattr(self, 'grain_image_filenames') or self.grain_image_filenames is None:
            self.grain_image_filenames = []
        if not hasattr(self, 'grain_image_file_paths') or self.grain_image_file_paths is None:
            self.grain_image_file_paths = []
        if not hasattr(self, 'grain_image_states') or self.grain_image_states is None:
            self.grain_image_states = {}

        # Track if this is the first image loaded
        is_first_image = len(self.grain_images) == 0
        first_loaded_index = -1

        # Load each image
        for file_path in file_paths:
            try:
                self.update_status(f"Loading grain image: {os.path.basename(file_path)}...")

                # Skip if already loaded
                if file_path in self.grain_image_file_paths:
                    logger.info(f"Image already loaded: {os.path.basename(file_path)}")
                    continue

                new_image = Image.open(file_path)
                # Convert to RGB for consistency
                if new_image.mode != 'RGB': new_image = new_image.convert('RGB')

                # Add to image lists
                self.grain_images.append(new_image)
                self.grain_image_filenames.append(os.path.basename(file_path))
                self.grain_image_file_paths.append(file_path)

                # Track the index of the first loaded image
                if first_loaded_index == -1:
                    first_loaded_index = len(self.grain_images) - 1

                # Update last save directory
                self.grain_last_save_dir = os.path.dirname(file_path)

                logger.info(f"Grain image loaded: {os.path.basename(file_path)} ({new_image.width}x{new_image.height})")

            except Exception as e:
                logger.exception(f"Failed to load grain image: {file_path}")
                self._show_error_message("Image Load Error", f"Could not load image file: {os.path.basename(file_path)}\nError: {e}")

        # Display the first loaded image if this is the first time loading images
        if is_first_image and first_loaded_index >= 0:
            self.select_grain_image(first_loaded_index)
        # Otherwise, update the image counter
        elif first_loaded_index >= 0:
            self.update_grain_image_counter()
            self.update_status(f"Loaded {len(file_paths)} new images. Total: {len(self.grain_images)}")
        else:
            self.update_status("No new images were loaded.")

    def display_image_on_scene(self, pil_image_to_display: Optional[Image.Image]):
        # Clear the scene completely, including polygons
        self.clear_scene_graphics_items(clear_pixmap=True)
        self.grain_items = {}
        self.polygons_loaded = False

        if pil_image_to_display is None:
            logger.warning("Attempted to display None image")
            self.view.reset_view()
            return

        qimage = pil_image_to_qimage(pil_image_to_display)
        if qimage is None or qimage.isNull():
            logger.error("Failed to convert PIL image to QImage")
            return self._show_error_message("Display Error", "Could not convert image.")

        pixmap = QPixmap.fromImage(qimage)
        if pixmap.isNull():
            logger.error("Failed to create pixmap from QImage")
            return self._show_error_message("Display Error", "Failed to create pixmap.")

        self.grain_pixmap_item = CustomPixmapItem(pixmap)
        self.scene.addItem(self.grain_pixmap_item)
        self.scene.setSceneRect(self.grain_pixmap_item.boundingRect())
        self.view.reset_view()

        # We no longer automatically redraw polygons here
        # Instead, we'll wait for the user to click the "Save & Show Visualization" button
        # This improves performance when switching between images

        # Make sure the reload button is properly labeled
        if hasattr(self, 'reload_results_button'):
            self.reload_results_button.setText(" Save & Show Visualization")

        logger.debug(f"Displayed grain image ({pil_image_to_display.width}x{pil_image_to_display.height}) on scene.")

        # Update action states to enable the reload button if needed
        self.update_action_states()

    def clear_scene_graphics_items(self, clear_pixmap=False):
        items_to_remove = []
        for item in self.scene.items():
            if isinstance(item, (QGraphicsPolygonItem, QGraphicsTextItem, QGraphicsLineItem)):
                items_to_remove.append(item)
            elif clear_pixmap and item == self.grain_pixmap_item:
                items_to_remove.append(item)
        for item in items_to_remove: self.scene.removeItem(item)
        if clear_pixmap: self.grain_pixmap_item = None
        self.grain_items = {}

    @Slot()
    def crop_image(self):
        if self.grain_uploaded_image is None: return self._show_error_message("No Image", "Load image first.")
        # if self.grain_processing_thread and self.grain_processing_thread.isRunning(): return QMessageBox.warning(self, "Busy", "Cannot crop.")
        cropped_image = show_crop_dialog(self.grain_uploaded_image, self)
        if cropped_image:
            self.reset_grain_analysis_state(clear_image=True)
            self.grain_uploaded_image = cropped_image
            if self.grain_image_filename:
                base, ext = os.path.splitext(self.grain_image_filename)
                self.grain_image_filename = f"{base}_cropped{ext}"
            self.display_image_on_scene(self.grain_uploaded_image)
            self.update_action_states()
            self.update_scale_reminder()
            self.update_status("Grain image cropped.")

    # --- Scale Handling ---
    @Slot(QPointF, QPointF)
    def on_scale_line_drawn(self, start_point_scene: QPointF, end_point_scene: QPointF):
        if self.grain_uploaded_image is None or self.grain_pixmap_item is None: return
        try:
            display_width = self.grain_pixmap_item.pixmap().width()
            display_height = self.grain_pixmap_item.pixmap().height()
            original_width = self.grain_uploaded_image.width
            original_height = self.grain_uploaded_image.height
            if display_width == 0 or display_height == 0: raise ValueError("Invalid pixmap dimensions")
            ratio_x = original_width / display_width; ratio_y = original_height / display_height
            start_orig = QPointF(start_point_scene.x() * ratio_x, start_point_scene.y() * ratio_y)
            end_orig = QPointF(end_point_scene.x() * ratio_x, end_point_scene.y() * ratio_y)
            line = QtCore.QLineF(start_orig, end_orig)
            pixel_length = line.length()
            self.grain_original_pixel_length = pixel_length
            if pixel_length < 1.0: raise ValueError("Scale line too short.")
            real_length_str = self.real_world_length_edit.text().replace(locale.localeconv()['decimal_point'], '.')
            real_length = float(real_length_str)
            if real_length <= 0: raise ValueError("Real-world length must be positive.")
            self.grain_current_scale_factor = real_length / pixel_length
            self.scale_factor_label.setText(f"<b>Scale: {self.grain_current_scale_factor:.4f} µm/pixel</b>")
            self.manual_scale_edit.setText(f"{self.grain_current_scale_factor:.6f}")
            self.update_scale_reminder(is_set=True)

            # Mark state as modified
            self.mark_state_as_modified()
            logger.info(f"Grain scale factor calculated: {self.grain_current_scale_factor:.4f}")
            self.update_action_states()
            self.scale_mode_button.setChecked(False) # Turn off scale mode button
            self.select_mode_button.setChecked(True) # Revert to select mode
        except Exception as e:
            self._show_error_message("Scale Error", f"Error calculating scale: {e}")
            self.view.set_scale_line_points(None, None)
            self.grain_current_scale_factor = None; self.grain_original_pixel_length = None
            self.update_scale_reminder()

    @Slot()
    def set_manual_scale(self):
        if self.grain_uploaded_image is None: return
        try:
            scale_str = self.manual_scale_edit.text().replace(locale.localeconv()['decimal_point'], '.')
            scale_factor = float(scale_str)
            if scale_factor <= 0: raise ValueError("Scale factor must be positive.")
            self.grain_current_scale_factor = scale_factor
            self.grain_original_pixel_length = None
            self.scale_factor_label.setText(f"<b>Scale: {self.grain_current_scale_factor:.4f} µm/pixel</b>")
            self.update_scale_reminder(is_set=True)
            logger.info(f"Grain scale factor set manually: {self.grain_current_scale_factor:.4f}")
            self.view.set_scale_line_points(None, None)
            self.update_action_states()

            # Mark state as modified
            self.mark_state_as_modified()
        except Exception as e:
            self._show_error_message("Input Error", f"Invalid scale factor: {e}")
            self.grain_current_scale_factor = None; self.update_scale_reminder()

    @Slot()
    def reset_scale(self):
        if self.grain_current_scale_factor is None: return
        reply = QMessageBox.question(self, "Confirm Reset Scale", "Clear grain analysis scale factor?", QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel)
        if reply == QMessageBox.Cancel: return
        logger.info("Resetting grain scale factor.")
        self.grain_current_scale_factor = None; self.grain_original_pixel_length = None
        self.view.set_scale_line_points(None, None)
        self.scale_factor_label.setText("<b>Current Scale: Not set</b>")
        self.manual_scale_edit.setText("0.0")
        self.update_scale_reminder(); self.update_action_states()
        self.update_status("Grain scale reset.")

    def update_scale_display(self):
        """Updates the UI elements to display the current scale information."""
        if hasattr(self, 'grain_current_scale_factor') and self.grain_current_scale_factor is not None:
            # Update scale factor label
            if hasattr(self, 'scale_factor_label'):
                self.scale_factor_label.setText(f"<b>Scale: {self.grain_current_scale_factor:.4f} µm/pixel</b>")

            # Update manual scale edit
            if hasattr(self, 'manual_scale_edit'):
                self.manual_scale_edit.setText(f"{self.grain_current_scale_factor:.6f}")

            # Update scale unit combo if available
            if hasattr(self, 'scale_unit_combo') and hasattr(self, 'grain_current_scale_unit'):
                index = self.scale_unit_combo.findText(self.grain_current_scale_unit)
                if index >= 0:
                    self.scale_unit_combo.setCurrentIndex(index)

            # Update scale line in view if available
            if hasattr(self, 'view') and hasattr(self, 'grain_scale_line'):
                self.view.set_scale_line_points(self.grain_scale_line.p1(), self.grain_scale_line.p2())

            # Update scale reminder
            self.update_scale_reminder(is_set=True)
        else:
            # Clear scale display if no scale is set
            if hasattr(self, 'scale_factor_label'):
                self.scale_factor_label.setText("<b>Current Scale: Not set</b>")

            if hasattr(self, 'manual_scale_edit'):
                self.manual_scale_edit.setText("0.0")

            # Update scale reminder
            self.update_scale_reminder(is_set=False)

    def update_scale_reminder(self, is_set=None):
        is_set = self.grain_current_scale_factor is not None if is_set is None else is_set
        if self.grain_uploaded_image is None:
            self.status_label.setText("Load grain image to start.")
        elif not is_set:
            self.status_label.setText("Grain scale not set.")
        else:
            # Use green color for success message instead of default (red)
            self.status_label.setText("<span style='color: #90EE90;'>Grain scale set.</span>")

    @Slot()
    def save_scale_factor(self):
        if self.grain_current_scale_factor is None: return self._show_error_message("No Scale", "No scale factor set.")
        target_dir = self.grain_default_save_dir or self.grain_last_save_dir
        suggested_filename = f"{os.path.splitext(self.grain_image_filename or 'scale')[0]}_grain_scale.json"
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Grain Scale Factor", os.path.join(target_dir, suggested_filename), "JSON files (*.json)")
        if not file_path: return
        self.grain_last_save_dir = os.path.dirname(file_path)
        try: rwl = float(self.real_world_length_edit.text().replace(locale.localeconv()['decimal_point'], '.'))
        except: rwl = None
        scale_data = {
            'scale_factor_um_per_pixel': self.grain_current_scale_factor,
            'real_world_length_um': rwl,
            'original_image_pixel_length': self.grain_original_pixel_length,
            'source_image': self.grain_image_file_path,
            'timestamp': datetime.now().isoformat()
        }
        try:
            with open(file_path, 'w') as f: json.dump(scale_data, f, indent=4)
            self.update_status(f"Grain scale saved: {os.path.basename(file_path)}")
        except Exception as e: self._show_error_message("Save Error", f"Failed to save scale: {e}")

    @Slot()
    def load_scale_factor(self):
        if self.grain_uploaded_image is None: return self._show_error_message("Load Image First", "Load image before loading scale.")
        target_dir = self.grain_default_save_dir or self.grain_last_save_dir
        file_path, _ = QFileDialog.getOpenFileName(self, "Load Grain Scale Factor", target_dir, "JSON files (*.json)")
        if not file_path: return
        self.grain_last_save_dir = os.path.dirname(file_path)
        try:
            with open(file_path, 'r') as f: scale_data = json.load(f)
            loaded_scale = scale_data.get('scale_factor_um_per_pixel')
            if loaded_scale is None or loaded_scale <= 0: raise ValueError("Invalid scale factor.")
            self.grain_current_scale_factor = loaded_scale
            self.grain_original_pixel_length = scale_data.get('original_image_pixel_length')
            rwl = scale_data.get('real_world_length_um')
            self.scale_factor_label.setText(f"<b>Scale: {self.grain_current_scale_factor:.4f} µm/pixel</b>")
            if rwl: self.real_world_length_edit.setText(locale.format_string("%.2f", rwl))
            self.manual_scale_edit.setText(locale.format_string("%.6f", self.grain_current_scale_factor))
            self.update_scale_reminder(is_set=True)
            self.view.set_scale_line_points(None, None)
            self.update_action_states()
            self.update_status(f"Grain scale loaded from {os.path.basename(file_path)}")
        except Exception as e:
            self._show_error_message("Load Error", f"Failed to load scale: {e}")
            self.grain_current_scale_factor = None; self.update_scale_reminder()

    # --- Processing ---
    def on_show_event(self, event):
        """Called when the widget is shown."""
        logger.info("GrainAnalysisWidget shown, updating slider labels")
        QTimer.singleShot(100, self.update_all_slider_labels)
        # Call the original showEvent
        super().showEvent(event)

    def update_all_slider_labels(self):
        """Updates all slider labels at once, regardless of which slider triggered the update."""
        logger.info("Updating all slider labels")
        # Update simplified artifact handling controls
        if hasattr(self, 'artifact_strength_slider') and hasattr(self, 'artifact_strength_label'):
            self.artifact_strength_label.setText(f"{self.artifact_strength_slider.value()}")

        self.update_slider_labels()

    @Slot()
    def update_slider_labels(self):
        """Updates the labels for all sliders to show their current values."""
        # Log which slider triggered the update
        sender = self.sender()
        if sender:
            logger.debug(f"Slider update triggered by: {sender.objectName()}")
        else:
            logger.debug("Slider update triggered without sender")

        # NMS Parameters
        if hasattr(self, 'containment_threshold'):
            value = self.containment_threshold.value() / 100.0
            self.containment_threshold_label.setText(f"{value:.2f}")
            logger.debug(f"Updated containment_threshold label to {value:.2f}")

        if hasattr(self, 'size_ratio_threshold'):
            value = self.size_ratio_threshold.value() / 10.0
            self.size_ratio_threshold_label.setText(f"{value:.1f}")
            logger.debug(f"Updated size_ratio_threshold label to {value:.1f}")

        if hasattr(self, 'border_penalty'):
            value = self.border_penalty.value() / 100.0
            self.border_penalty_label.setText(f"{value:.2f}")
            logger.debug(f"Updated border_penalty label to {value:.2f}")

        # Subgrain Parameters
        if hasattr(self, 'subgrain_angle_threshold'):
            value = self.subgrain_angle_threshold.value()
            self.subgrain_angle_threshold_label.setText(f"{value:.1f}°")
            logger.debug(f"Updated subgrain_angle_threshold label to {value:.1f}°")

        if hasattr(self, 'subgrain_edge_straightness'):
            value = self.subgrain_edge_straightness.value() / 100.0
            self.subgrain_edge_straightness_label.setText(f"{value:.2f}")
            logger.debug(f"Updated subgrain_edge_straightness label to {value:.2f}")

        if hasattr(self, 'subgrain_parallel_edges'):
            value = self.subgrain_parallel_edges.value()
            self.subgrain_parallel_edges_label.setText(f"{value}")
            logger.debug(f"Updated subgrain_parallel_edges label to {value}")

        if hasattr(self, 'straight_edge_ratio_slider'):
            value = self.straight_edge_ratio_slider.value() / 100.0
            self.straight_edge_ratio_label.setText(f"{value:.2f}")
            logger.debug(f"Updated straight_edge_ratio label to {value:.2f}")

        # Force update of the UI
        QApplication.processEvents()

    @Slot()
    def analyze_with_patches(self):
        logger.info("Grain patch segmentation requested.")
        if self.grain_model is None: return self._show_error_message("Model Error", "Grain model not loaded.")
        if self.grain_uploaded_image is None: return self._show_error_message("Warning", "Upload grain image first.")
        if self.grain_current_scale_factor is None: return self._show_error_message("Scale Error", "Set grain image scale.")
        if self.grain_patch_processing_thread and self.grain_patch_processing_thread.isRunning(): return QMessageBox.warning(self, "Busy", "Processing.")

        patch_config = show_patch_config_dialog(self.grain_uploaded_image.width, self.grain_uploaded_image.height, self)
        if not patch_config: return logger.info("Patch configuration canceled.")

        try:
            # Get model parameters from the current widget
            current_param_widget = self.model_params_stack.currentWidget()
            params = current_param_widget.get_parameters()
            model_type_to_run = params['model_type']

            # Get artifact handling parameters based on the intelligent artifact handling UI
            # Apply the current preset to ensure parameters are in sync
            if hasattr(self, 'preset_combo') and hasattr(self, 'artifact_sensitivity_slider'):
                self.apply_artifact_preset(self.preset_combo.currentIndex())

            # Add intelligent artifact handling parameters to the params dictionary
            if hasattr(self, 'artifact_sensitivity_slider'):
                params['artifact_sensitivity'] = self.artifact_sensitivity_slider.value() / 100.0

            # Add duplicate detection parameters
            if hasattr(self, 'duplicate_sensitivity_slider'):
                params['duplicate_sensitivity'] = self.duplicate_sensitivity_slider.value() / 100.0

            # Use optimized hardcoded performance settings for better performance
            params['duplicate_detection_timeout'] = 60  # Balanced timeout
            params['batch_size'] = 100  # Balanced batch size
            params['max_pairs_to_check'] = 10000  # Balanced max pairs

            # Add preset info for reference
            if hasattr(self, 'preset_combo'):
                params['artifact_preset'] = self.preset_combo.currentText()

            params['performance_preset'] = 'Balanced'  # Always use balanced preset

            # Enable intelligent patch merging
            params['use_intelligent_patch_merge'] = True

            logger.info(f"Using intelligent artifact handling preset: {params.get('artifact_preset', 'Balanced')} "
                       f"with artifact sensitivity {params.get('artifact_sensitivity', 0.5):.2f} "
                       f"and duplicate sensitivity {params.get('duplicate_sensitivity', 0.7):.2f}")

            # Log performance settings
            logger.info(f"Performance settings: timeout={params.get('duplicate_detection_timeout', 60)}s, "
                       f"batch_size={params.get('batch_size', 100)}, "
                       f"max_pairs={params.get('max_pairs_to_check', 10000)}, "
                       f"preset={params.get('performance_preset', 'Balanced')}")
        except Exception as e: return self._show_error_message("Parameter Error", f"Invalid parameters: {e}")

        # Determine device (same logic as GrainSightApp)
        target_device = self.grain_device # Use the device determined at init
        if model_type_to_run == 'fastsam':
             target_device = torch.device("cpu")
             logger.info("FastSAM selected for grains: Forcing CPU processing.")
        # MobileSAM will use self.grain_device (CUDA if available, else CPU)

        # Reset previous results
        self.grain_annotations = None; self.grain_df = None; self.grain_processed_image_vis = None
        self.clear_scene_graphics_items(clear_pixmap=False)
        self.results_widget.clear()

        # Setup Worker and Thread
        self.grain_patch_processing_worker = PatchProcessingWorker(
            self.grain_uploaded_image, self.grain_model, target_device, # Use target_device
            self.grain_current_scale_factor, params, patch_config
        )
        self.grain_patch_processing_thread = QThread()
        self.grain_patch_processing_worker.moveToThread(self.grain_patch_processing_thread)

        # Connections
        self.grain_patch_processing_worker.finished.connect(self.on_processing_finished)
        self.grain_patch_processing_worker.error.connect(self.on_processing_error)
        self.grain_patch_processing_worker.progress.connect(self.update_progress)
        self.grain_patch_processing_worker.status.connect(self.update_status)
        self.grain_patch_processing_thread.started.connect(self.grain_patch_processing_worker.run)
        # Cleanup
        self.grain_patch_processing_worker.finished.connect(self.grain_patch_processing_thread.quit)
        self.grain_patch_processing_worker.finished.connect(self.grain_patch_processing_worker.deleteLater)
        self.grain_patch_processing_thread.finished.connect(self.grain_patch_processing_thread.deleteLater)
        self.grain_patch_processing_thread.finished.connect(self._on_patch_thread_finished)

        self.grain_patch_processing_thread.start()
        self.update_action_states(processing=True)
        self.update_status("Starting grain patch segmentation...")

    # Stop methods for workers (adapt from GrainSightApp if needed)
    def stop_processing_thread(self):
         if self.grain_processing_thread and self.grain_processing_thread.isRunning():
              logger.info("Attempting to stop grain processing thread.")
              if self.grain_processing_worker: self.grain_processing_worker.stop()
              QTimer.singleShot(100, self.grain_processing_thread.quit)
         if self.grain_patch_processing_thread and self.grain_patch_processing_thread.isRunning():
              logger.info("Attempting to stop grain patch processing thread.")
              if self.grain_patch_processing_worker: self.grain_patch_processing_worker.stop()
              QTimer.singleShot(100, self.grain_patch_processing_thread.quit)


    @Slot(object, object, object)
    def on_processing_finished(self, df_result, annotations_result, segmented_image_vis_result):
        logger.info("Grain processing finished signal received.")
        self.update_progress(0)
        if df_result is not None and annotations_result is not None and segmented_image_vis_result is not None:
            self.grain_df = df_result
            self.grain_annotations = annotations_result
            self.grain_processed_image_vis = segmented_image_vis_result
            # Set polygons_loaded to False to ensure they're hidden by default
            self.polygons_loaded = False
            logger.info(f"Grain processing successful. Received {len(self.grain_df)} results.")
            self.display_image_on_scene(self.grain_processed_image_vis)
            # Don't draw grain highlights immediately - let user click 'Show Polygons' button
            # self.draw_grain_highlights()
            self.results_widget.populate(self.grain_df)
            # Explicitly enable the Show Polygons button
            if hasattr(self, 'reload_results_button'):
                self.reload_results_button.setEnabled(True)
                self.reload_results_button.setText(" Show Grain Visualization")
                logger.info("Explicitly enabling Show Grain Visualization button after processing")
            self.update_status(f"Grain processing complete: {len(self.grain_df)} grains found. Click 'Show Grain Visualization' to display interactive grains.")

            # Mark state as modified
            self.mark_state_as_modified()
        else:
             logger.warning("Grain processing finished but results are None/incomplete.")
             self.update_status("Grain processing finished, but no valid results generated.")
             if self.grain_uploaded_image and self.grain_pixmap_item is None:
                  self.display_image_on_scene(self.grain_uploaded_image)
        QTimer.singleShot(100, lambda: self.update_action_states(processing=False))

    @Slot(str)
    def on_processing_error(self, error_message):
        logger.error(f"Grain processing error signal received: {error_message}")
        self.update_progress(0)
        self._show_error_message("Grain Processing Error", error_message)
        self.update_status(f"Grain processing failed: {error_message}")
        if self.grain_uploaded_image and self.grain_pixmap_item is None:
            self.display_image_on_scene(self.grain_uploaded_image)
        QTimer.singleShot(100, lambda: self.update_action_states(processing=False))

    @Slot()
    def _on_thread_finished(self): # General thread cleanup
         logger.debug("Grain processing QThread finished signal.")
         if self.sender() == self.grain_processing_thread:
              self.grain_processing_thread = None
              self.grain_processing_worker = None
         self.update_action_states(processing=False)

    @Slot()
    def _on_patch_thread_finished(self):
         logger.debug("Grain patch processing QThread finished signal.")
         if self.sender() == self.grain_patch_processing_thread:
              self.grain_patch_processing_thread = None
              self.grain_patch_processing_worker = None
         self.update_action_states(processing=False)

    # --- Visualization and Interaction ---
    def draw_grain_highlights(self):
        # (Copy/Adapt from GrainSightApp, using self.grain_df, self.grain_annotations, self.grain_uploaded_image, etc.)
        logger.info("Starting draw_grain_highlights method")

        if self.grain_df is None:
            logger.error("Cannot draw highlights: grain_df is None")
            return
        if self.grain_uploaded_image is None:
            logger.error("Cannot draw highlights: grain_uploaded_image is None")
            return
        if self.grain_pixmap_item is None:
            logger.error("Cannot draw highlights: grain_pixmap_item is None")
            return

        # If we have a dataframe but no annotations, we can't draw polygons
        # But we'll log this as a warning instead of returning, since we might be able to
        # show some information from the dataframe in the future
        if self.grain_annotations is None:
            logger.warning("No annotations available, but dataframe exists. Cannot draw polygons.")
            self.update_status("Cannot draw polygons: No annotation data available.")
            return

        # Clear existing graphics items before drawing new ones
        self.clear_scene_graphics_items(clear_pixmap=False)

        num_df_rows = len(self.grain_df)
        num_annotations = len(self.grain_annotations) if isinstance(self.grain_annotations, list) else (self.grain_annotations.shape[0] if isinstance(self.grain_annotations, torch.Tensor) else 0)

        logger.info(f"Drawing highlights: DF rows={num_df_rows}, Annotations={num_annotations}, Annotation type={type(self.grain_annotations)}")

        # Check for mismatch between annotations and dataframe
        if num_df_rows != num_annotations:
            logger.error(f"Grain highlight mismatch: DF={num_df_rows}, Anno={num_annotations}. This indicates annotations from a different image.")

            # Clear existing annotations since they don't match the current dataframe
            self.clear_scene_graphics_items(clear_pixmap=False)

            # Try to reload annotations from project for the current image
            if hasattr(self, 'project') and self.project and hasattr(self, 'grain_image_file_path') and self.grain_image_file_path:
                image_id = None
                if hasattr(self.project, 'image_path_to_id_map') and self.grain_image_file_path in self.project.image_path_to_id_map:
                    image_id = self.project.image_path_to_id_map[self.grain_image_file_path]
                else:
                    # Fall back to the slower method
                    for img_id, info in self.project.images.items():
                        full_path = os.path.join(self.project.temp_dir, info.filepath)
                        if full_path == self.grain_image_file_path:
                            image_id = img_id
                            break

                if image_id:
                    logger.info(f"Attempting to reload annotations for image {image_id}")
                    state = self.project.load_grain_analysis_state(image_id, load_annotations=True)
                    if state and 'annotations' in state:
                        self.grain_annotations = state['annotations']
                        num_annotations = len(self.grain_annotations) if isinstance(self.grain_annotations, list) else \
                                        (self.grain_annotations.shape[0] if hasattr(self.grain_annotations, 'shape') else 0)

                        if num_df_rows == num_annotations:
                            logger.info(f"Successfully reloaded matching annotations for image {image_id}")
                        else:
                            logger.error(f"Reloaded annotations still don't match: DF={num_df_rows}, Anno={num_annotations}")
                            return
                    else:
                        logger.error(f"Failed to reload annotations for image {image_id}")
                        return
                else:
                    logger.error("Could not find image ID to reload annotations")
                    return
            else:
                logger.error("Cannot reload annotations: No project or image path available")
                return

        if num_df_rows == 0:
            logger.warning("No rows in dataframe, nothing to draw")
            return

        logger.info(f"Drawing grain highlights for {num_df_rows} grains.")

        try:
            display_width = self.grain_pixmap_item.pixmap().width(); display_height = self.grain_pixmap_item.pixmap().height()
            original_width = self.grain_uploaded_image.width; original_height = self.grain_uploaded_image.height
            if display_width == 0 or original_width == 0: raise ValueError("Zero dims")
            scale_x = display_width / original_width; scale_y = display_height / original_height
        except Exception as e: logger.error(f"Highlight scaling error: {e}"); return

        default_pen = QPen(QColor(0, 255, 255, 200), 1.5); default_pen.setCosmetic(True)
        selected_pen = QPen(QColor(255, 0, 0, 255), 2.5); selected_pen.setCosmetic(True)
        current_selection = self.results_widget.selected_df_indices

        # Convert annotations to a list format for processing
        annotation_list = []
        logger.info(f"Converting annotations of type {type(self.grain_annotations)} to list format")

        if isinstance(self.grain_annotations, list):
            logger.info(f"Annotations already in list format, length: {len(self.grain_annotations)}")
            annotation_list = self.grain_annotations
            # Debug first annotation
            if len(self.grain_annotations) > 0:
                logger.info(f"First annotation type: {type(self.grain_annotations[0])}")
        elif hasattr(self.grain_annotations, 'shape') and hasattr(self.grain_annotations, 'cpu'):
            # PyTorch tensor
            logger.info(f"Converting PyTorch tensor with shape {self.grain_annotations.shape} to list")
            annotation_list = [self.grain_annotations[i] for i in range(num_annotations)]
            logger.info(f"Converted to list with {len(annotation_list)} items")
        elif hasattr(self.grain_annotations, 'shape') and hasattr(self.grain_annotations, 'tolist'):
            # NumPy array
            logger.info(f"Converting NumPy array with shape {self.grain_annotations.shape} to list")
            annotation_list = [self.grain_annotations[i] for i in range(num_annotations)]
            logger.info(f"Converted to list with {len(annotation_list)} items")
        else:
            logger.error(f"Unknown annotation type: {type(self.grain_annotations)}")
            self.clear_scene_graphics_items(clear_pixmap=False)
            return

        # Use the dataframe index as the ID for each polygon
        # This ensures the IDs shown in the visualization match the dataframe
        for df_index, mask_tensor in zip(self.grain_df.index, annotation_list):
            try:
                # Convert mask to numpy array based on its type
                logger.info(f"Processing mask of type {type(mask_tensor)} for df_index {df_index}")

                if hasattr(mask_tensor, 'cpu') and hasattr(mask_tensor, 'numpy'):
                    # PyTorch tensor
                    logger.info(f"Converting PyTorch tensor with shape {mask_tensor.shape} to numpy array")
                    try:
                        # Try to convert directly
                        mask_np = mask_tensor.cpu().numpy().astype(np.uint8)
                        logger.info(f"Converted to numpy array with shape {mask_np.shape}")
                    except RuntimeError as e:
                        # Handle CUDA out-of-memory errors
                        if "CUDA error: out of memory" in str(e):
                            logger.warning(f"CUDA out of memory when converting tensor. Using fallback method.")
                            # Fallback: If tensor is on GPU, try to process it in smaller chunks
                            if mask_tensor.is_cuda:
                                try:
                                    # Create empty numpy array of the same shape
                                    mask_np = np.zeros(mask_tensor.shape, dtype=np.uint8)

                                    # Process in rows to reduce memory usage
                                    chunk_size = 10  # Process 10 rows at a time
                                    height = mask_tensor.shape[0]

                                    for i in range(0, height, chunk_size):
                                        end_idx = min(i + chunk_size, height)
                                        # Process a small chunk at a time
                                        chunk = mask_tensor[i:end_idx].cpu().numpy()
                                        mask_np[i:end_idx] = chunk

                                    logger.info(f"Successfully converted tensor using chunked approach. Shape: {mask_np.shape}")
                                except Exception as chunk_error:
                                    # If chunked approach fails, try with clone and detach
                                    logger.warning(f"Chunked conversion failed: {chunk_error}. Trying with clone and detach.")
                                    try:
                                        # Try to detach from computation graph and move to CPU
                                        detached_tensor = mask_tensor.clone().detach()
                                        torch.cuda.empty_cache()  # Free up GPU memory
                                        cpu_tensor = detached_tensor.cpu()
                                        del detached_tensor  # Free up memory
                                        torch.cuda.empty_cache()  # Free up GPU memory again
                                        mask_np = cpu_tensor.numpy().astype(np.uint8)
                                        del cpu_tensor  # Free up memory
                                        logger.info(f"Successfully converted tensor using detach method. Shape: {mask_np.shape}")
                                    except Exception as detach_error:
                                        # Last resort: create a new tensor on CPU
                                        logger.warning(f"Detach method failed: {detach_error}. Creating new tensor on CPU.")
                                        try:
                                            # Create a new tensor on CPU with same values
                                            mask_np = np.zeros(mask_tensor.shape, dtype=np.uint8)
                                            # Use item() to get individual values (slow but memory efficient)
                                            if len(mask_tensor.shape) == 2:
                                                h, w = mask_tensor.shape
                                                for y in range(h):
                                                    for x in range(w):
                                                        if mask_tensor[y, x].item() > 0:
                                                            mask_np[y, x] = 1
                                            logger.info(f"Successfully created new array on CPU. Shape: {mask_np.shape}")
                                        except Exception as item_error:
                                            # If all else fails, raise the original error
                                            logger.error(f"All conversion methods failed. Last error: {item_error}")
                                            raise e
                        else:
                            # If not a CUDA memory error, re-raise
                            raise
                elif hasattr(mask_tensor, 'astype') and hasattr(mask_tensor, 'shape'):
                    # NumPy array
                    logger.info(f"Converting NumPy array with shape {mask_tensor.shape} to uint8")
                    mask_np = mask_tensor.astype(np.uint8)
                    logger.info(f"Converted to numpy array with shape {mask_np.shape}")
                elif isinstance(mask_tensor, dict) and 'segmentation' in mask_tensor:
                    # Dictionary with segmentation (like from Digital Sreeni annotator)
                    # Create a mask from the segmentation polygon
                    logger.info(f"Converting dictionary with segmentation to mask, keys: {mask_tensor.keys()}")
                    mask_np = np.zeros((original_height, original_width), dtype=np.uint8)
                    points = np.array(mask_tensor['segmentation']).reshape(-1, 2).astype(np.int32)
                    logger.info(f"Segmentation points shape: {points.shape}")
                    cv2.fillPoly(mask_np, [points], 1)
                    logger.info(f"Created mask with shape {mask_np.shape}")
                else:
                    # Try to convert to numpy array
                    try:
                        logger.info(f"Attempting generic conversion to numpy array")
                        mask_np = np.array(mask_tensor).astype(np.uint8)
                        logger.info(f"Converted to numpy array with shape {mask_np.shape}")
                    except Exception as e:
                        logger.exception(f"Could not convert mask to numpy array: {e}")
                        continue
                contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if not contours: continue
                contour = max(contours, key=cv2.contourArea)
                scaled_contour = contour.astype(np.float32)
                scaled_contour[:, :, 0] *= scale_x; scaled_contour[:, :, 1] *= scale_y
                scaled_contour = scaled_contour.astype(np.int32)
                qpolygon = QtGui.QPolygonF()
                for point in scaled_contour.squeeze():
                    if len(point) == 2: qpolygon.append(QPointF(point[0], point[1]))
                if qpolygon.count() < 3: continue

                polygon_item = QGraphicsPolygonItem(qpolygon)
                polygon_item.setAcceptHoverEvents(True)
                polygon_item.setData(Qt.UserRole, df_index)
                is_selected = df_index in current_selection
                polygon_item.setPen(selected_pen if is_selected else default_pen)
                polygon_item.setBrush(Qt.NoBrush)
                polygon_item.setZValue(1 if is_selected else 0)
                self.scene.addItem(polygon_item)

                # Add text item with grain ID
                text_id = f"{df_index}"  # Use df index as ID label
                text_item = QGraphicsTextItem(text_id)
                text_item.setDefaultTextColor(QColor(255, 255, 0) if is_selected else QColor(255, 100, 100, 220))
                # Make selected text bold
                font = text_item.font()
                font.setBold(is_selected)
                text_item.setFont(font)

                # Set font size
                base_font_size = 10  # Default font size
                font = QFont("Arial", base_font_size)
                font.setBold(is_selected)  # Make selected text bold
                text_item.setFont(font)

                # Position text at centroid
                M = cv2.moments(scaled_contour)
                cX, cY = 0, 0
                if M["m00"] != 0:
                    cX, cY = int(M["m10"]/M["m00"]), int(M["m01"]/M["m00"])
                else:
                    cX, cY = int(qpolygon.boundingRect().center().x()), int(qpolygon.boundingRect().center().y())

                text_rect = text_item.boundingRect()
                text_item.setPos(cX - text_rect.width() / 2, cY - text_rect.height() / 2)
                text_item.setFlag(QGraphicsTextItem.ItemIgnoresTransformations, False)  # Scale with view
                text_item.setData(Qt.UserRole, df_index)
                text_item.setZValue(polygon_item.zValue() + 0.1)  # Ensure text is above polygon

                self.scene.addItem(text_item)

                # Store references to both polygon and text items
                self.grain_items[df_index] = {'poly': polygon_item, 'text': text_item}
            except Exception as e: logger.exception(f"Error drawing grain {df_index}: {e}")
        self.scene.update()

    @Slot(float)
    def on_zoom_changed(self, scale_factor: float):
        """
        Handle zoom changes with improved focus management.
        This method has been added to prevent button focus loss when zooming on the image.

        Args:
            scale_factor: The new scale factor after zooming
        """
        # Update action states to ensure buttons remain enabled
        self.update_action_states()

        # Ensure critical buttons remain enabled after zooming
        # This prevents the issue where buttons become unresponsive after zooming
        if hasattr(self, 'reload_results_button') and self.reload_results_button.isEnabled():
            # Force focus back to the widget to ensure buttons remain responsive
            QTimer.singleShot(100, self.setFocus)

        # Log zoom for debugging
        logger.debug(f"Zoom changed: scale factor = {scale_factor:.2f}")

    @Slot(QPointF)
    def on_scene_clicked(self, scene_pos: QPointF):
        """
        Handle clicks on the scene with improved focus management.
        This method has been enhanced to prevent button focus loss when interacting with the image.

        Args:
            scene_pos: Position in the scene where the click occurred
        """
        # Skip if not in selection mode or no grain items exist
        if self.view.mode != CustomGraphicsView.MODE_SELECTION or not self.grain_items:
            return

        # Find items at the click position
        items_at_pos = self.scene.items(scene_pos)
        candidate_grains = []

        # Find all grain polygons at the click position
        for item in items_at_pos:
            if isinstance(item, QGraphicsPolygonItem):
                df_index = item.data(Qt.UserRole)
                if df_index is not None and df_index in self.grain_items and item.contains(scene_pos):
                    # Use bounding box area for quick sorting, smaller is "more inside"
                    area = item.boundingRect().width() * item.boundingRect().height()
                    candidate_grains.append((area, df_index, item))

        # Determine which grain was clicked (smallest area if multiple)
        clicked_grain_index = None
        if candidate_grains:
            # Sort by area ascending and get the smallest one
            clicked_grain_index = sorted(candidate_grains, key=lambda x: x[0])[0][1]
            logger.debug(f"Clicked on grain index: {clicked_grain_index}")
        else:
            logger.debug("Clicked on empty area.")

        # Handle selection logic (Ctrl for multi-select)
        modifiers = QtWidgets.QApplication.keyboardModifiers()
        is_multi_select = modifiers == Qt.ControlModifier
        current_selection = self.results_widget.selected_df_indices.copy()
        new_selection = set()

        if clicked_grain_index is not None:
            if is_multi_select:
                # Toggle selection with Ctrl key
                if clicked_grain_index in current_selection:
                    current_selection.remove(clicked_grain_index)
                else:
                    current_selection.add(clicked_grain_index)
                new_selection = current_selection
            else:
                # Single selection - toggle if already selected
                new_selection = {clicked_grain_index} if not (clicked_grain_index in current_selection and len(current_selection) == 1) else set()
        elif not is_multi_select:
            # Clear selection when clicking empty area without Ctrl
            new_selection = set()
        else:
            # Keep current selection when clicking empty area with Ctrl
            new_selection = current_selection

        # Update selection in results widget
        self.results_widget.set_selected_indices(new_selection)

        # Update visual selection
        self.update_grain_visual_selection(new_selection)

        # Ensure buttons remain enabled after clicking on the image
        self.update_action_states()

        # Ensure critical buttons remain enabled after image interaction
        # This prevents the issue where buttons become unresponsive after clicking on the image
        if hasattr(self, 'reload_results_button') and self.reload_results_button.isEnabled():
            # Force focus back to the widget to ensure buttons remain responsive
            QTimer.singleShot(100, self.setFocus)

        # Log selection for debugging
        logger.debug(f"Selection after click: {len(new_selection)} items selected")

    @Slot(set)
    def on_results_view_selection_changed(self, selected_indices: set):
        """
        Handle selection changes in the results view with improved focus management.
        This method has been enhanced to prevent button focus loss when interacting with the dataframe.

        Args:
            selected_indices: Set of selected indices in the dataframe
        """
        # Update visual selection
        self.update_grain_visual_selection(selected_indices)

        # Update action states to ensure buttons remain enabled
        self.update_action_states()

        # Ensure critical buttons remain enabled after dataframe interaction
        # This prevents the issue where buttons become unresponsive after clicking on the dataframe
        if hasattr(self, 'reload_results_button') and self.reload_results_button.isEnabled():
            # Force focus back to the widget to ensure buttons remain responsive
            self.setFocus()

        # Log selection for debugging
        logger.debug(f"Selection changed: {len(selected_indices)} items selected")

    def update_grain_visual_selection(self, selected_indices: set):
        """
        This function updates the visual selection of grains in the scene.
        The id of the selected grains is stored in the Qt.UserRole of the QGraphicsPolygonItem.

        Args:
            selected_indices: Set of selected indices in the dataframe
        """
        # Skip if no grain items exist
        if not self.grain_items:
            return

        # Define pens and colors for visual feedback
        default_pen = QPen(QColor(0, 255, 255, 200), 1.5); default_pen.setCosmetic(True)
        selected_pen = QPen(QColor(255, 0, 0, 255), 2.5); selected_pen.setCosmetic(True)
        default_text_color = QColor(255, 100, 100, 220)
        selected_text_color = QColor("yellow")

        for df_index, items in self.grain_items.items():
            poly_item = items.get('poly')
            text_item = items.get('text')

            if not poly_item: continue
            is_selected = df_index in selected_indices

            # Update polygon
            poly_item.setPen(selected_pen if is_selected else default_pen)
            poly_item.setZValue(1 if is_selected else 0)

            # Update text if it exists
            if text_item:
                # Update text color
                text_item.setDefaultTextColor(selected_text_color if is_selected else default_text_color)

                # Update font (make selected text bold)
                font = text_item.font()
                font.setBold(is_selected)
                text_item.setFont(font)

                # Update z-value to keep text above polygon
                text_item.setZValue(poly_item.zValue() + 0.1)

        self.scene.update()

    # --- Deletion and Recalculation ---
    @Slot(set)
    def delete_selected_grains(self, indices_to_delete: set):
        # (Copy/Adapt from GrainSightApp, using self.grain_df, self.grain_annotations)
        if not indices_to_delete or self.grain_df is None or self.grain_annotations is None or self.grain_uploaded_image is None: return
        num_to_delete = len(indices_to_delete)
        reply = QMessageBox.question(self, "Confirm Deletion", f"Delete {num_to_delete} selected grain(s)?", QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel)
        if reply == QMessageBox.Cancel: return
        logger.info(f"Deleting {num_to_delete} grains: {indices_to_delete}")
        # QApplication.setOverrideCursor(Qt.WaitCursor) # Parent?
        try:
            valid_indices_to_delete = indices_to_delete.intersection(set(self.grain_df.index))
            if not valid_indices_to_delete: return logger.warning("No selected indices in DF.")

            df_keep_mask = ~self.grain_df.index.isin(valid_indices_to_delete)
            original_df_indices = self.grain_df.index.tolist()

            # Ensure consistent length check
            num_anno = len(self.grain_annotations) if isinstance(self.grain_annotations, list) else (self.grain_annotations.shape[0] if isinstance(self.grain_annotations, torch.Tensor) else -1)
            if len(self.grain_df) != num_anno: raise RuntimeError("CRITICAL: DF/Annotations mismatch before deletion.")

            indices_to_keep_in_annotations = [i for i, df_idx in enumerate(original_df_indices) if df_idx not in valid_indices_to_delete]

            if isinstance(self.grain_annotations, torch.Tensor):
                 self.grain_annotations = self.grain_annotations[indices_to_keep_in_annotations] if indices_to_keep_in_annotations else torch.empty((0,) + self.grain_annotations.shape[1:], dtype=self.grain_annotations.dtype, device=self.grain_annotations.device)
            elif isinstance(self.grain_annotations, list):
                 self.grain_annotations = [self.grain_annotations[i] for i in indices_to_keep_in_annotations]

            # Filter the dataframe and reset its index to ensure it matches the annotation positions
            self.grain_df = self.grain_df[df_keep_mask].reset_index(drop=True)
            logger.info(f"Reset dataframe index after deletion to ensure alignment with annotations. New df shape: {self.grain_df.shape}")

            self.update_status(f"Deleted {len(valid_indices_to_delete)}. Updating...")
            # QApplication.processEvents() # Parent?

            # Automatically recalculate parameters to ensure synchronization
            if self.grain_current_scale_factor is not None and len(self.grain_annotations) > 0:
                logger.info("Automatically recalculating parameters after deletion")
                recalculation_success = self._recalculate_parameters_internal(show_confirmation=False)
                if recalculation_success:
                    # Parameters were recalculated, so we don't need to update visualization separately
                    self.update_status(f"Deleted {len(valid_indices_to_delete)} grains and recalculated parameters.")
                    return
                else:
                    logger.warning("Automatic recalculation failed, falling back to basic visualization update")

            # If we didn't recalculate or it failed, update visualization directly
            current_param_widget = self.model_params_stack.currentWidget()
            contour_thickness = getattr(current_param_widget, 'contour_thickness_slider', None)
            contour_thickness = contour_thickness.value() if contour_thickness else DEFAULT_CONTOUR_THICKNESS

            new_vis = create_segmented_visualization(self.grain_uploaded_image, self.grain_annotations, contour_thickness=contour_thickness)
            if new_vis: self.grain_processed_image_vis = new_vis; self.display_image_on_scene(new_vis)
            else: logger.error("Failed to regen vis after delete."); self.display_image_on_scene(self.grain_uploaded_image)

            self.draw_grain_highlights()
            self.results_widget.populate(self.grain_df)
            self.update_status(f"Deleted {len(valid_indices_to_delete)} grains.")
        except Exception as e: logger.exception("Error deleting grains:"); self._show_error_message("Deletion Error", f"Error: {e}")
        # finally: QApplication.restoreOverrideCursor() # Parent?
        self.update_action_states()

    def _recalculate_parameters_internal(self, show_confirmation=True):
        """Internal method to recalculate parameters without UI confirmation."""
        # Check processing state via thread variable
        is_processing = (self.grain_processing_thread and self.grain_processing_thread.isRunning()) or \
                        (self.grain_patch_processing_thread and self.grain_patch_processing_thread.isRunning())
        if is_processing:
            if show_confirmation:
                return QMessageBox.warning(self, "Busy", "Cannot recalculate.")
            else:
                logger.warning("Cannot recalculate: Processing in progress")
                return False

        if self.grain_annotations is None:
            if show_confirmation:
                return self._show_error_message("No Segmentation", "No results.")
            else:
                logger.warning("Cannot recalculate: No segmentation results")
                return False

        if self.grain_current_scale_factor is None:
            if show_confirmation:
                return self._show_error_message("No Scale", "Set scale.")
            else:
                logger.warning("Cannot recalculate: No scale factor set")
                return False

        num_annotations = len(self.grain_annotations) if isinstance(self.grain_annotations, list) else (self.grain_annotations.shape[0] if isinstance(self.grain_annotations, torch.Tensor) else 0)
        if num_annotations == 0:
            if show_confirmation:
                return self._show_error_message("No Annotations", "No annotations.")
            else:
                logger.warning("Cannot recalculate: No annotations")
                return False

        # Show confirmation dialog if requested
        if show_confirmation:
            reply = QMessageBox.question(self, "Confirm Recalculation",
                                       f"Recalculate for {num_annotations} annotations with scale {self.grain_current_scale_factor:.4f}?",
                                       QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Yes)
            if reply == QMessageBox.Cancel:
                return False

        logger.info(f"Recalculating with scale: {self.grain_current_scale_factor:.4f}")
        self.update_status("Recalculating parameters..."); self.update_progress(1)
        # QApplication.setOverrideCursor(Qt.WaitCursor) # Parent?
        try:
            progress_adapter = lambda val: QMetaObject.invokeMethod(self, "update_progress", Qt.QueuedConnection, Q_ARG(int, val))
            new_df, valid_mask = calculate_parameters(self.grain_annotations, self.grain_current_scale_factor, progress_callback=progress_adapter)

            # Check if we need to filter annotations based on valid_mask
            num_annotations = len(self.grain_annotations) if isinstance(self.grain_annotations, list) else (self.grain_annotations.shape[0] if isinstance(self.grain_annotations, torch.Tensor) else 0)
            if np.sum(valid_mask) != num_annotations:
                logger.warning(f"Valid annotations changed ({np.sum(valid_mask)}/{num_annotations}). Filtering.")
                indices_to_keep = np.where(valid_mask)[0]
                if isinstance(self.grain_annotations, torch.Tensor):
                    self.grain_annotations = self.grain_annotations[indices_to_keep] if indices_to_keep.size > 0 else torch.empty((0,) + self.grain_annotations.shape[1:], dtype=self.grain_annotations.dtype, device=self.grain_annotations.device)
                elif isinstance(self.grain_annotations, list):
                    self.grain_annotations = [self.grain_annotations[i] for i in indices_to_keep]

            self.grain_df = new_df
            self.results_widget.populate(self.grain_df)
            self.draw_grain_highlights()
            self.update_status(f"Parameters recalculated: {len(self.grain_df)} objects.")
            return True
        except Exception as e:
            logger.exception("Recalculation error:")
            if show_confirmation:
                self._show_error_message("Recalculation Error", f"Error: {e}")
            self.update_status("Recalculation failed.")
            return False
        finally:
            # QApplication.restoreOverrideCursor() # Parent?
            self.update_progress(0)
            self.update_action_states()

    @Slot()
    def recalculate_parameters(self):
        """Recalculate grain parameters from existing annotations."""
        logger.info("Recalculate parameters requested")

        # First check if we have annotations in memory
        if self.grain_annotations is None:
            # If we don't have annotations in memory but have a dataframe, try to load annotations from the project
            if hasattr(self, 'grain_df') and self.grain_df is not None and not self.grain_df.empty:
                logger.info("No annotations in memory but dataframe exists. Attempting to load annotations from project.")

                # Try to load annotations from the project
                if hasattr(self, 'project') and self.project and hasattr(self, 'grain_image_file_path') and self.grain_image_file_path:
                    image_id = self._get_image_id_for_path(self.grain_image_file_path)

                    if image_id:
                        logger.info(f"Loading annotations for image {image_id} from project")
                        state = self.project.load_grain_analysis_state(image_id, load_annotations=True)

                        if state and 'annotations' in state:
                            self.grain_annotations = state['annotations']
                            logger.info(f"Successfully loaded annotations from project for image {image_id}")
                        else:
                            logger.warning(f"Failed to load annotations from project for image {image_id}")
                            self._show_error_message("No Annotations", "Could not load annotations from project. Please segment the image first.")
                            return
                    else:
                        logger.warning("Could not find image ID to load annotations")
                        self._show_error_message("No Annotations", "Could not find image in project. Please segment the image first.")
                        return
                else:
                    logger.warning("No project or image path available to load annotations")
                    self._show_error_message("No Annotations", "No grain annotations available. Please segment the image first.")
                    return
            else:
                # No annotations and no dataframe
                self._show_error_message("No Annotations", "No grain annotations available. Please segment the image first.")
                return

        if self.grain_current_scale_factor is None:
            self._show_error_message("Scale Not Set", "Please set the image scale before recalculating parameters.")
            return

        # Confirm with user
        reply = QMessageBox.question(
            self, "Confirm Recalculation",
            "Recalculate grain parameters? This will update measurements but keep the same segmentation.",
            QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel
        )
        if reply == QMessageBox.Cancel:
            return

        self._recalculate_parameters_internal(show_confirmation=False)

    @Slot()
    def recalculate_parameters_with_new_scale(self):
        """Public method to recalculate parameters with user confirmation."""
        logger.info("Grain Recalculate requested.")
        self.recalculate_parameters()

    # --- Saving Methods ---
    @Slot()
    def save_results(self):
        # (Copy/Adapt from GrainSightApp, using self.grain_df, self.grain_processed_image_vis)
        if self.grain_df is None or self.grain_df.empty: return self._show_error_message("No Results", "No results.")
        save_image = self.grain_processed_image_vis is not None
        if not save_image: logger.warning("Grain DF exists, but visualization image missing.")

        base_filename = os.path.splitext(self.grain_image_filename or "grain_analysis")[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        suggested_csv = f"{base_filename}_results_{timestamp}.csv"
        suggested_img = f"{base_filename}_segmented_{timestamp}.png"
        target_dir = self.grain_default_save_dir or self.grain_last_save_dir
        save_dir = QFileDialog.getExistingDirectory(self, "Select Save Directory", target_dir)
        if not save_dir: return
        self.grain_last_save_dir = save_dir

        try:
            df_file_path = os.path.join(save_dir, suggested_csv)
            decimal_sep = locale.localeconv().get('decimal_point', '.')
            self.grain_df.to_csv(df_file_path, index=False, decimal=decimal_sep)
            logger.info(f"Grain results saved: {df_file_path}")
            if save_image:
                img_file_path = os.path.join(save_dir, suggested_img)
                self.grain_processed_image_vis.save(img_file_path, "PNG")
                logger.info(f"Grain segmented image saved: {img_file_path}")
            QMessageBox.information(self, "Save Successful", f"Grain results saved in:\n{save_dir}")
        except Exception as e: logger.exception("Save grain results error:"); self._show_error_message("Save Error", f"Failed to save: {e}")

    @Slot()
    def save_coco_annotations(self):
        # (Copy/Adapt from GrainSightApp, using self.grain_annotations, self.grain_uploaded_image)
        if self.grain_annotations is None: return self._show_error_message("No Annotations", "No annotations.")
        if self.grain_uploaded_image is None: return self._show_error_message("No Image", "Original image needed.")
        num_annotations = len(self.grain_annotations) if isinstance(self.grain_annotations, list) else (self.grain_annotations.shape[0] if isinstance(self.grain_annotations, torch.Tensor) else 0)
        if num_annotations == 0: return self._show_error_message("No Annotations", "List empty.")

        base_filename = os.path.splitext(self.grain_image_filename or "grain_coco")[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        suggested_filename = f"{base_filename}_coco_{timestamp}.json"
        target_dir = self.grain_default_save_dir or self.grain_last_save_dir
        file_path, _ = QFileDialog.getSaveFileName(self, "Save Grain COCO Annotations", os.path.join(target_dir, suggested_filename), "JSON files (*.json)")
        if not file_path: return
        self.grain_last_save_dir = os.path.dirname(file_path)

        try:
            coco_dict = generate_coco_dict(self.grain_annotations, self.grain_uploaded_image.height, self.grain_uploaded_image.width, self.grain_image_filename)
            if coco_dict is None: raise ValueError("Failed to generate COCO dict.")
            with open(file_path, 'w') as f: json.dump(coco_dict, f, indent=2)
            QMessageBox.information(self, "Export Successful", f"Grain COCO saved:\n{file_path}")
        except Exception as e: logger.exception("Save grain COCO error:"); self._show_error_message("Export Error", f"Failed: {e}")

    # --- Plotting ---
    @Slot()
    def show_plotting_dialog(self):
        # (Copy/Adapt from GrainSightApp, using self.grain_df)
        if self.grain_df is None or self.grain_df.empty: return self._show_error_message("No Data", "No results.")
        plot_dialog = show_plot_selection_dialog(self.grain_df, self)
        if plot_dialog:
             self.grain_plot_dialogs.append(plot_dialog)
             plot_dialog.finished.connect(lambda _: self._remove_plot_dialog(plot_dialog))
             plot_dialog.show()

    def _remove_plot_dialog(self, dialog):
         try: self.grain_plot_dialogs.remove(dialog)
         except ValueError: pass

    # --- Image Navigation Methods ---
    def update_grain_image_counter(self):
        """Updates the image counter for the grain analysis page.
        This method is kept for compatibility but is no longer needed with the gallery.
        The gallery handles its own image counting and display.
        """
        # This method is no longer needed with the gallery
        pass

    def previous_grain_image(self):
        """Navigates to the previous image in the grain analysis page.
        This method is kept for compatibility but is no longer needed with the gallery.
        The gallery handles its own image navigation.
        """
        # This method is no longer needed with the gallery
        if hasattr(self, 'grain_gallery') and self.grain_gallery.selected_index > 0:
            # Save current analysis state if we have annotations
            if hasattr(self, 'grain_annotations') and self.grain_annotations is not None:
                self.save_grain_analysis_state()

            # Select previous image in gallery
            self.grain_gallery.select_image(self.grain_gallery.selected_index - 1)
            self.on_gallery_image_clicked(self.grain_gallery.selected_index)

    def next_grain_image(self):
        """Navigates to the next image in the grain analysis page.
        This method is kept for compatibility but is no longer needed with the gallery.
        The gallery handles its own image navigation.
        """
        # This method is no longer needed with the gallery
        if hasattr(self, 'grain_gallery') and self.grain_gallery.selected_index >= 0 and \
           self.grain_gallery.selected_index < len(self.grain_gallery.images) - 1:
            # Save current analysis state if we have annotations
            if hasattr(self, 'grain_annotations') and self.grain_annotations is not None:
                self.save_grain_analysis_state()

            # Select next image in gallery
            self.grain_gallery.select_image(self.grain_gallery.selected_index + 1)
            self.on_gallery_image_clicked(self.grain_gallery.selected_index)

    def select_grain_image(self, index):
        """Selects and loads the image at the specified index.
        This method is kept for compatibility but is now a wrapper around the gallery selection.
        """
        if hasattr(self, 'grain_gallery') and 0 <= index < len(self.grain_gallery.images):
            # Select the image in the gallery
            self.grain_gallery.select_image(index)
            # Trigger the gallery image clicked handler
            self.on_gallery_image_clicked(index)
        elif hasattr(self, 'grain_images') and 0 <= index < len(self.grain_images):
            # Fallback to old method if gallery is not available
            # Save the current state if we have annotations
            if hasattr(self, 'grain_image_file_path') and self.grain_image_file_path and \
               hasattr(self, 'grain_annotations') and self.grain_annotations is not None:
                self.save_grain_analysis_state()

            # Load the image
            self.grain_uploaded_image = self.grain_images[index]
            self.grain_image_file_path = self.grain_image_file_paths[index]
            self.grain_image_filename = self.grain_image_filenames[index]

            # Try to load state from project first if available
            state = None
            if hasattr(self, 'project') and self.project and hasattr(self.project, 'load_grain_analysis_state'):
                # Find the image_id for this file path
                image_id = None
                for img_id, info in self.project.images.items():
                    if os.path.join(self.project.temp_dir, info.filepath) == self.grain_image_file_path:
                        image_id = img_id
                        break

                if image_id:
                    # Load state but don't load annotations yet - they'll be loaded on demand when user clicks Show Polygons
                    state = self.project.load_grain_analysis_state(image_id, load_annotations=False)

            # If no state from project, try from memory
            if not state and self.grain_image_file_path in self.grain_image_states:
                state = self.grain_image_states[self.grain_image_file_path]

            # Load state if available
            if state:
                self.load_grain_analysis_state(state)
            else:
                # Reset the analysis state but keep the image
                self.reset_grain_analysis_state(clear_image=False)

            # Display the image
            self.display_image_on_scene(self.grain_uploaded_image)

            # Update action states to enable/disable buttons
            self.update_action_states()

            # Set current_grain_image_path for navigation
            self.current_grain_image_path = self.grain_image_file_path

    def mark_state_as_modified(self):
        """Mark the current state as modified, indicating it needs to be saved."""
        self.state_modified = True
        logger.debug(f"Marked state as modified for image: {self.grain_image_file_path}")

    def save_all_pending_changes(self):
        """Save all images that have pending changes."""
        if not hasattr(self, 'images_needing_save') or not self.images_needing_save:
            logger.info("No pending changes to save")
            return

        logger.info(f"Saving {len(self.images_needing_save)} images with pending changes")

        # Remember current image path
        current_path = self.grain_image_file_path if hasattr(self, 'grain_image_file_path') else None

        # Save current image first if it's modified
        if current_path and self.state_modified:
            self.save_grain_analysis_state()
            self.state_modified = False
            if current_path in self.images_needing_save:
                self.images_needing_save.remove(current_path)

        # Save all other pending images
        for image_path in list(self.images_needing_save):
            self._save_state_for_path(image_path)
            self.images_needing_save.remove(image_path)

        logger.info("All pending changes saved")

    def _save_state_for_path(self, file_path):
        """Save the state for a specific image path."""
        if not hasattr(self, 'project') or not self.project:
            logger.warning(f"Cannot save state for {file_path}: No project available")
            return False

        # Find the image_id for this file path
        image_id = None
        if hasattr(self.project, 'image_path_to_id_map') and file_path in self.project.image_path_to_id_map:
            image_id = self.project.image_path_to_id_map[file_path]
        else:
            # Fall back to the slower method
            for img_id, info in self.project.images.items():
                full_path = os.path.join(self.project.temp_dir, info.filepath)
                if full_path == file_path:
                    image_id = img_id
                    # Cache this for future lookups
                    if not hasattr(self.project, 'image_path_to_id_map'):
                        self.project.image_path_to_id_map = {}
                    self.project.image_path_to_id_map[file_path] = img_id
                    break

        if not image_id:
            logger.warning(f"Cannot save state: Could not find image ID for {file_path}")
            return False

        # Get the state from memory
        if file_path in self.grain_image_states:
            state = self.grain_image_states[file_path]
            self.project.save_grain_analysis_state(image_id, state)
            logger.info(f"Saved state for image {image_id} from memory")
            return True

        logger.warning(f"Cannot save state: No state in memory for {file_path}")
        return False

    def load_grain_analysis_state(self):
        """Loads the analysis state for the current image.

        This method loads the saved state from the project for the current image,
        including annotations, dataframe, and visualization image.
        """
        if not hasattr(self, 'grain_image_file_path') or not self.grain_image_file_path:
            logger.debug("Cannot load state: No image file path")
            return False

        if not hasattr(self, 'project') or not self.project:
            logger.debug("Cannot load state: No project available")
            return False

        # Get the image ID for the current file path
        image_id = self._get_image_id_for_path(self.grain_image_file_path)
        if not image_id:
            logger.debug(f"Cannot load state: No image ID found for {self.grain_image_file_path}")
            return False

        # Try to load state from the project
        try:
            state = self.project.load_grain_analysis_state(image_id)
            if not state:
                logger.debug(f"No saved state found for image {image_id}")
                return False

            logger.info(f"Loaded state for image {image_id}")

            # Load annotations if available
            if 'annotations' in state:
                self.grain_annotations = state['annotations']
                logger.debug(f"Loaded annotations for image {image_id}")

            # Load dataframe if available
            if 'df' in state and isinstance(state['df'], pd.DataFrame):
                self.grain_df = state['df']
                logger.debug(f"Loaded dataframe with {len(self.grain_df)} rows for image {image_id}")

                # Update the results widget with the loaded dataframe
                if hasattr(self, 'results_widget'):
                    self.results_widget.set_dataframe(self.grain_df)
                    logger.debug("Updated results widget with loaded dataframe")

            # Load processed image visualization if available
            if 'processed_image_vis' in state:
                self.grain_processed_image_vis = state['processed_image_vis']
                logger.debug(f"Loaded processed image visualization for image {image_id}")

                # Display the processed image
                if self.grain_processed_image_vis:
                    self.display_image_on_scene(self.grain_processed_image_vis)
                    logger.debug("Displayed processed image visualization")

            # Load scale information
            if 'scale_value' in state:
                self.grain_current_scale_factor = state['scale_value']
                logger.debug(f"Loaded scale factor: {self.grain_current_scale_factor}")

            if 'scale_unit' in state:
                self.grain_current_scale_unit = state['scale_unit']
                logger.debug(f"Loaded scale unit: {self.grain_current_scale_unit}")

            if 'scale_line' in state:
                self.grain_scale_line = state['scale_line']
                logger.debug(f"Loaded scale line")

            # Update UI with loaded scale information
            self.update_scale_display()

            # Store the state in memory
            self.grain_image_states[self.grain_image_file_path] = state

            # Update action states
            self.update_action_states()

            # Reset the modified flag since we just loaded the state
            self.state_modified = False

            return True
        except Exception as e:
            logger.exception(f"Error loading state for image {image_id}: {e}")
            return False

    def save_grain_analysis_state(self):
        """Saves the current analysis state for the current image.

        This method implements an efficient state saving approach that:
        1. Only saves what's needed (separates UI state from data state)
        2. Uses a cached image ID lookup for better performance
        3. Avoids redundant saves
        """
        if not hasattr(self, 'grain_image_file_path') or not self.grain_image_file_path:
            logger.debug("Cannot save state: No image file path")
            return False

        # Skip if nothing has changed
        if not self.state_modified:
            logger.debug("Skipping state save: No changes detected")
            return True

        # Get image ID efficiently using cached lookup
        image_id = self._get_image_id_for_path(self.grain_image_file_path)
        if not image_id and hasattr(self, 'project') and self.project:
            logger.warning(f"Cannot find image ID for {self.grain_image_file_path}")
            return False

        # Create a state dictionary with all the necessary information
        # Split into UI state and data state for more efficient handling
        ui_state = {
            'scale_line': self.view.scale_line if hasattr(self, 'view') and hasattr(self.view, 'scale_line') else None,
            'scale_value': self.grain_current_scale_factor if hasattr(self, 'grain_current_scale_factor') else None,
            'scale_unit': self.scale_unit_combo.currentText() if hasattr(self, 'scale_unit_combo') else 'μm',
            'polygons_loaded': self.polygons_loaded if hasattr(self, 'polygons_loaded') else False,
            'ui_timestamp': datetime.now().isoformat(),
        }

        # Only include data state if we have results
        data_state = {}
        if hasattr(self, 'grain_annotations') and self.grain_annotations is not None:
            data_state['annotations'] = self.grain_annotations

        if hasattr(self, 'grain_df') and self.grain_df is not None and not self.grain_df.empty:
            data_state['df'] = self.grain_df.copy()

        if hasattr(self, 'grain_processed_image_vis') and self.grain_processed_image_vis is not None:
            data_state['processed_image_vis'] = self.grain_processed_image_vis

        # Combine states for storage
        combined_state = {**ui_state, **data_state}

        # Save the state in memory (only if we have data)
        if data_state:
            self.grain_image_states[self.grain_image_file_path] = combined_state
            logger.debug(f"Saved state in memory for {self.grain_image_file_path}")

        # Reset the modified flag
        self.state_modified = False

        # Note: Automatic disk persistence disabled for better performance
        # State is only kept in memory during the session
        # Use "Save Current Results" button for manual saves to disk
        logger.debug(f"State saved in memory only (disk persistence disabled for performance)")
        return True

    def load_grain_analysis_state(self, state):
        """Loads a saved analysis state.

        This method implements an improved state loading approach that:
        1. Separates UI state from data state for more efficient handling
        2. Properly handles visualization toggle state
        3. Ensures consistent button states
        4. Provides better user feedback
        """
        if not state:
            logger.debug("Cannot load state: No state provided")
            return False

        # Start with a clean slate for UI elements
        self.polygons_loaded = False

        # Track what was loaded for better user feedback
        loaded_components = []

        # --- Load UI State ---
        # Restore scale line
        if 'scale_line' in state and state['scale_line'] and hasattr(self, 'view'):
            self.view.scale_line = state['scale_line']
            self.view.update()
            loaded_components.append("scale line")

        # Restore scale value and unit
        if 'scale_value' in state and state['scale_value'] is not None:
            self.grain_current_scale_factor = state['scale_value']
            self.scale_factor_label.setText(f"<b>Scale: {self.grain_current_scale_factor:.4f} µm/pixel</b>")
            # Also update the manual scale edit
            self.manual_scale_edit.setText(f"{self.grain_current_scale_factor:.6f}")
            loaded_components.append("scale factor")
            logger.info(f"Loaded scale factor: {self.grain_current_scale_factor:.4f} µm/pixel")

        # Restore scale unit if the combo box exists
        if 'scale_unit' in state and state['scale_unit'] and hasattr(self, 'scale_unit_combo'):
            index = self.scale_unit_combo.findText(state['scale_unit'])
            if index >= 0:
                self.scale_unit_combo.setCurrentIndex(index)
                loaded_components.append("scale unit")

        # --- Load Data State ---
        # Store annotations in memory but don't render them yet
        if 'annotations' in state and state['annotations'] is not None:
            self.grain_annotations = state['annotations']
            anno_count = len(self.grain_annotations) if isinstance(self.grain_annotations, list) else \
                        (self.grain_annotations.shape[0] if hasattr(self.grain_annotations, 'shape') else 0)
            logger.info(f"Loaded {anno_count} annotations of type {type(self.grain_annotations).__name__}")
            loaded_components.append(f"{anno_count} annotations")

        # Load dataframe results
        if 'df' in state and state['df'] is not None and not state['df'].empty:
            self.grain_df = state['df']
            logger.info(f"Loaded dataframe with {len(self.grain_df)} rows")
            self.results_widget.populate(self.grain_df)
            loaded_components.append(f"results table with {len(self.grain_df)} grains")

        # Restore processed image visualization
        if 'processed_image_vis' in state and state['processed_image_vis'] is not None:
            self.grain_processed_image_vis = state['processed_image_vis']
            # Display the processed image if available
            self.display_image_on_scene(self.grain_processed_image_vis)
            loaded_components.append("segmentation visualization")
        else:
            # Display the original image if no processed image is available
            self.display_image_on_scene(self.grain_uploaded_image)

        # --- Restore UI State ---
        # Restore polygons_loaded state if it was saved
        if 'polygons_loaded' in state:
            # Don't actually load the polygons yet, just remember the state
            # The actual loading happens when the user clicks the button
            self.polygons_loaded = False

            # Update the button text based on the saved state
            if hasattr(self, 'reload_results_button'):
                self.reload_results_button.setText(" Save & Show Visualization")

        # --- Update UI Based on Loaded State ---
        # Update scale reminder first
        if self.grain_current_scale_factor is not None:
            self.update_scale_reminder(is_set=True)
        else:
            self.update_scale_reminder()

        # Force update of action states to reflect the loaded state
        # This is critical to ensure buttons are properly enabled/disabled
        logger.info("Updating action states after loading state")
        self.update_action_states()

        # Log the state of key variables for debugging
        logger.debug(f"After loading state: scale_factor={self.grain_current_scale_factor}, " +
                    f"has_df={self.grain_df is not None and not self.grain_df.empty}, " +
                    f"has_annotations={self.grain_annotations is not None}")

        # Always ensure critical buttons are enabled if we have results
        # This is a failsafe to ensure buttons are never incorrectly disabled
        has_results = (hasattr(self, 'grain_df') and self.grain_df is not None and not self.grain_df.empty) or \
                     (hasattr(self, 'grain_annotations') and self.grain_annotations is not None)

        if has_results:
            # Ensure visualization button is enabled
            if hasattr(self, 'reload_results_button'):
                logger.info("Ensuring reload_results_button is enabled because results exist")
                self.reload_results_button.setEnabled(True)

            # Ensure statistics button is enabled if scale is set
            if hasattr(self, 'stats_button') and self.grain_current_scale_factor is not None:
                logger.info("Ensuring stats_button is enabled because results exist and scale is set")
                self.stats_button.setEnabled(True)

            # Ensure recalculate button is enabled if scale is set
            if hasattr(self, 'recalculate_button') and self.grain_current_scale_factor is not None:
                logger.info("Ensuring recalculate_button is enabled because results exist and scale is set")
                self.recalculate_button.setEnabled(True)

            # Ensure save results button is enabled
            if hasattr(self, 'save_results_action_button'):
                logger.info("Ensuring save_results_action_button is enabled because results exist")
                self.save_results_action_button.setEnabled(True)

        # Update status to inform user about available results
        if loaded_components:
            components_str = ", ".join(loaded_components)
            self.update_status(f"Loaded {components_str}. Click 'Show Grain Visualization' to display interactive grains.")
        else:
            self.update_status("No previous results found for this image.")

        # Call update_action_states one more time to ensure consistency
        # This helps address edge cases where button states might be inconsistent
        QTimer.singleShot(100, self.update_action_states)

        return True

    # --- State Management & UI Updates ---
    @Slot()
    def reset_grain_analysis(self):
        # (Copy/Adapt from GrainSightApp)
        if self.grain_df is not None or self.grain_uploaded_image is not None:
            reply = QMessageBox.question(self, "Reset Confirmation", "Reset grain analysis data?", QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel)
            if reply == QMessageBox.Cancel: return
        logger.info("Resetting grain analysis.")
        self.reset_grain_analysis_state(clear_image=True)
        self.update_status("Grain analysis reset. Load image.")

    def reset_grain_analysis_state(self, clear_image=True):
        """Resets the grain analysis state.

        This method implements an improved state reset approach that:
        1. Properly handles UI state during reset
        2. Ensures consistent button states
        3. Provides better user feedback

        Args:
            clear_image: Whether to clear the current image (True) or keep it (False)
        """
        # Stop any running processing
        self.stop_processing_thread()

        # Clear image-related state if requested
        if clear_image:
            self.grain_uploaded_image = None
            self.grain_image_file_path = None
            self.grain_image_filename = None
            self.scene.clear()
            self.grain_pixmap_item = None
            self.view.reset_view()
            logger.debug("Cleared image and related state")

        # Clear analysis results
        self.grain_processed_image_vis = None
        self.grain_annotations = None
        self.grain_df = None
        self.grain_items = {}

        # Reset UI state
        self.polygons_loaded = False
        self.grain_current_scale_factor = None
        self.grain_original_pixel_length = None
        self.scale_factor_label.setText("<b>Current Scale: Not set</b>")
        self.view.set_scale_line_points(None, None)

        # Clear results widget
        self.results_widget.clear()

        # Update UI state
        self.update_scale_reminder()
        self.update_action_states()

        # Reset the reload button text
        if hasattr(self, 'reload_results_button'):
            self.reload_results_button.setText(" Show Grain Visualization")

        # Reset the state modified flag
        self.state_modified = False

        logger.debug("Reset grain analysis state complete")
        # Note: We don't reset parameters here unless explicitly requested

    @Slot()
    def toggle_grain_visualization(self):
        """Toggles the display of grain visualization (polygons) without saving state.

        This method is a more efficient version that:
        1. Properly handles the show/hide state
        2. Provides better user feedback
        3. Ensures consistent button states
        4. Does NOT save state when just toggling visualization
        """
        # If polygons are already loaded, we'll toggle them off
        if self.polygons_loaded:
            logger.info("Hiding grain visualization")
            # Clear existing polygons
            self.clear_scene_graphics_items(clear_pixmap=False)
            self.grain_items = {}
            self.polygons_loaded = False

            # Update button text and status
            self.reload_results_button.setText(" Show Grain Visualization")
            self.update_status("Visualization hidden. Click 'Show Grain Visualization' to display interactive grains.")

            # Make sure the button stays enabled
            self.reload_results_button.setEnabled(True)

            # Update action states
            self.update_action_states()
            return

        # Show loading indicator immediately
        self.status_progress.show()
        self.status_progress.setValue(10)
        self.update_status("Loading grain visualization...")
        QApplication.processEvents()

        # Start a timer to measure performance
        start_time = time.time()

        # Check if we have annotations and dataframe
        has_annotations = self.grain_annotations is not None
        has_df = self.grain_df is not None and not self.grain_df.empty

        # Log the state for debugging
        logger.debug(f"toggle_grain_visualization called: has_annotations={has_annotations}, has_df={has_df}, df_type={type(self.grain_df) if self.grain_df is not None else None}")

        # If we have a dataframe but no annotations, try to load annotations from project
        if not has_annotations and has_df and hasattr(self, 'project') and self.project:
            logger.info("We have a dataframe but no annotations. Attempting to load annotations from project.")
            self.update_status("Loading annotations from project...")
            self.status_progress.setValue(20)
            QApplication.processEvents()

            # Find the image_id for this file path
            image_id = self._get_image_id_for_path(self.grain_image_file_path)

            if image_id:
                # Update progress indicator
                self.status_progress.setValue(30)
                self.update_status(f"Loading annotations for image {os.path.basename(self.grain_image_file_path)}...")
                QApplication.processEvents()

                # Load annotations on demand
                state = self.project.load_grain_analysis_state(image_id, load_annotations=True)
                if state and 'annotations' in state:
                    self.grain_annotations = state['annotations']
                    has_annotations = True
                    logger.info(f"Successfully loaded annotations from project for image {image_id}")
                    self.status_progress.setValue(40)
                    self.update_status("Preparing to draw polygons...")
                    QApplication.processEvents()
                else:
                    logger.warning(f"Failed to load annotations from project for image {image_id}")
                    self.update_status("Failed to load annotations from project.")

        # If we still don't have annotations but have a dataframe, we'll try to proceed with just the dataframe
        if not has_annotations and has_df:
            logger.info("We have a dataframe but no annotations. Attempting to proceed with just the dataframe.")
            self.update_status("No polygon data available, but will display dataframe results.")
            self.status_progress.setValue(50)
            QApplication.processEvents()
            # We'll continue with just the dataframe
            has_annotations = True

        if not has_annotations and not has_df:
            self.update_status("No results available to display.")
            self.status_progress.hide()
            return

        # Show a progress dialog for large datasets
        if self.grain_annotations is None:
            # If we have a dataframe but no annotations, we can still show the button as enabled
            # but we'll inform the user that we can't draw polygons
            self.update_status("Cannot draw polygons: No annotation data available.")
            # Set the flag to indicate polygons are loaded (even though we can't actually draw them)
            # This allows the button to toggle between Show/Hide states
            self.polygons_loaded = True
            self.reload_results_button.setText(" Hide Grain Visualization")
            self.status_progress.hide()
            return

        # Check for mismatch between annotations and dataframe
        num_annotations = len(self.grain_annotations) if isinstance(self.grain_annotations, list) else \
                         (self.grain_annotations.shape[0] if hasattr(self.grain_annotations, 'shape') else 0)
        num_df_rows = len(self.grain_df) if self.grain_df is not None else 0

        if num_annotations != num_df_rows:
            logger.error(f"Grain highlight mismatch: DF={num_df_rows}, Anno={num_annotations}. This indicates annotations from a different image.")

            # Clear existing annotations since they don't match the current dataframe
            self.grain_annotations = None

            # Try to reload annotations from project for the current image
            if hasattr(self, 'project') and self.project and hasattr(self, 'grain_image_file_path') and self.grain_image_file_path:
                image_id = self._get_image_id_for_path(self.grain_image_file_path)

                if image_id:
                    logger.info(f"Attempting to reload annotations for image {image_id}")
                    state = self.project.load_grain_analysis_state(image_id, load_annotations=True)
                    if state and 'annotations' in state:
                        self.grain_annotations = state['annotations']
                        num_annotations = len(self.grain_annotations) if isinstance(self.grain_annotations, list) else \
                                        (self.grain_annotations.shape[0] if hasattr(self.grain_annotations, 'shape') else 0)

                        if num_df_rows == num_annotations:
                            logger.info(f"Successfully reloaded matching annotations for image {image_id}")
                        else:
                            logger.error(f"Reloaded annotations still don't match: DF={num_df_rows}, Anno={num_annotations}")
                            self.update_status(f"Cannot show visualization: Annotations don't match dataframe. Try reprocessing the image.")
                            self.polygons_loaded = True  # Set this to true so the button toggles correctly
                            self.reload_results_button.setText(" Hide Grain Visualization")
                            self.status_progress.hide()
                            return
                    else:
                        logger.error(f"Failed to reload annotations for image {image_id}")
                        self.update_status("Cannot show visualization: Failed to load annotations. Try reprocessing the image.")
                        self.polygons_loaded = True  # Set this to true so the button toggles correctly
                        self.reload_results_button.setText(" Hide Grain Visualization")
                        self.status_progress.hide()
                        return
                else:
                    logger.error("Could not find image ID to reload annotations")
                    self.update_status("Cannot show visualization: Could not find image in project. Try reprocessing the image.")
                    self.polygons_loaded = True  # Set this to true so the button toggles correctly
                    self.reload_results_button.setText(" Hide Grain Visualization")
                    self.status_progress.hide()
                    return
            else:
                logger.error("Cannot reload annotations: No project or image path available")
                self.update_status("Cannot show visualization: No project or image path available. Try reprocessing the image.")
                self.polygons_loaded = True  # Set this to true so the button toggles correctly
                self.reload_results_button.setText(" Hide Grain Visualization")
                self.status_progress.hide()
                return

        # Update progress for large datasets
        self.update_status(f"Loading {num_annotations} polygons...")
        self.status_progress.setValue(60)
        QApplication.processEvents()

        try:
            # For very large datasets, show more detailed progress
            if num_annotations > 100:
                self.update_status(f"Processing {num_annotations} polygons (this may take a moment)...")
                self.status_progress.setValue(70)
                QApplication.processEvents()

            # Draw the grain highlights
            self.draw_grain_highlights()

            # Update progress
            self.status_progress.setValue(90)
            QApplication.processEvents()

            # Set the flag to indicate polygons are loaded
            self.polygons_loaded = True
            self.reload_results_button.setText(" Hide Grain Visualization")

            # Update the status with timing information
            elapsed_time = time.time() - start_time
            self.update_status(f"Loaded {num_annotations} polygons in {elapsed_time:.2f} seconds.")

        except Exception as e:
            self.update_status(f"Error loading polygons: {str(e)}")
            logger.error(f"Error in toggle_grain_visualization: {str(e)}")
        finally:
            # Hide progress bar
            self.status_progress.hide()

            # Update action states
            self.update_action_states()

    @Slot()
    def reload_previous_results(self):
        """Legacy method kept for compatibility. Now redirects to toggle_grain_visualization."""
        logger.info("reload_previous_results called - redirecting to toggle_grain_visualization")
        self.toggle_grain_visualization()



    @Slot(int)
    def update_progress(self, value: int):
        if 0 < value < 100: self.status_progress.show(); self.status_progress.setValue(value)
        else: self.status_progress.hide(); self.status_progress.setValue(0)

    @Slot(str)
    def _get_image_id_for_path(self, file_path: str) -> Optional[str]:
        """Gets the image ID for a file path using an efficient cached lookup.

        Args:
            file_path: The full path to the image file

        Returns:
            The image ID if found, None otherwise
        """
        if not hasattr(self, 'project') or not self.project:
            return None

        # Use cached lookup if available
        if not hasattr(self.project, 'image_path_to_id_map'):
            self.project.image_path_to_id_map = {}

        # Check cache first
        if file_path in self.project.image_path_to_id_map:
            return self.project.image_path_to_id_map[file_path]

        # Not in cache, do the lookup
        for img_id, info in self.project.images.items():
            full_path = os.path.join(self.project.temp_dir, info.filepath)
            if full_path == file_path:
                # Cache for future lookups
                self.project.image_path_to_id_map[file_path] = img_id
                return img_id

        return None

    def update_status(self, message: str, log_level: str = 'info'):
        """
        Update the status label and log the message at the specified level.

        Args:
            message: The status message to display
            log_level: The logging level to use ('debug', 'info', 'warning', 'error')
        """
        # Use green color for informative messages, red for errors/processing
        if log_level == 'error' or 'processing' in message.lower() or 'failed' in message.lower() or 'error' in message.lower():
            # Keep red color for error messages or processing messages
            self.status_label.setText(message)
        else:
            # Use green color for all other informative messages
            self.status_label.setText(f"<span style='color: #90EE90;'>{message}</span>")

        # Log the message at the specified level
        if log_level == 'debug':
            logger.debug(f"Grain Status: {message}")
        elif log_level == 'warning':
            logger.warning(f"Grain Status: {message}")
        elif log_level == 'error':
            logger.error(f"Grain Status: {message}")
        else:  # Default to info
            logger.info(f"Grain Status: {message}")

    @Slot()
    def update_slider_labels(self):
        """Updates labels next to sliders."""
        # Update FastSAM/MobileSAM parameter labels (handled by their respective widgets)
        # Check if the parameter widgets have the appropriate update method
        if hasattr(self, 'fastsam_params_widget'):
            if hasattr(self.fastsam_params_widget, 'update_slider_labels'):
                self.fastsam_params_widget.update_slider_labels()
            elif hasattr(self.fastsam_params_widget, '_update_labels'):
                self.fastsam_params_widget._update_labels()

        if hasattr(self, 'mobilesam_params_widget'):
            if hasattr(self.mobilesam_params_widget, 'update_slider_labels'):
                self.mobilesam_params_widget.update_slider_labels()
            elif hasattr(self.mobilesam_params_widget, '_update_labels'):
                self.mobilesam_params_widget._update_labels()

        # Update NMS parameter labels
        if hasattr(self, 'containment_threshold') and hasattr(self, 'containment_threshold_label'):
            containment_val = self.containment_threshold.value() / 100.0
            self.containment_threshold_label.setText(f"{containment_val:.2f}")

        if hasattr(self, 'size_ratio_threshold') and hasattr(self, 'size_ratio_threshold_label'):
            # Convert from 20-100 range to 2.0-10.0
            size_ratio_val = self.size_ratio_threshold.value() / 10.0
            self.size_ratio_threshold_label.setText(f"{size_ratio_val:.1f}")

        if hasattr(self, 'border_penalty') and hasattr(self, 'border_penalty_label'):
            border_penalty_val = self.border_penalty.value() / 100.0
            self.border_penalty_label.setText(f"{border_penalty_val:.2f}")

    # Note: Advanced performance settings methods removed for better performance
    # Using hardcoded optimized defaults instead of UI controls

    @Slot(int)
    def apply_artifact_preset(self, preset_index):
        """Applies a preset configuration for intelligent artifact handling.

        Args:
            preset_index: Index of the preset (0=Conservative, 1=Balanced, 2=Aggressive)
        """
        if not hasattr(self, 'preset_combo') or not hasattr(self, 'artifact_sensitivity_slider'):
            logger.warning("Cannot apply preset: UI elements not found")
            return

        preset_name = self.preset_combo.currentText()
        logger.info(f"Applying artifact preset: {preset_name}")

        # Set sensitivities based on preset
        if preset_index == 0:  # Conservative
            # Lower sensitivities to keep more grains
            self.artifact_sensitivity_slider.setValue(30)  # 0.30
            self.duplicate_sensitivity_slider.setValue(50)  # 0.50
        elif preset_index == 1:  # Balanced (default)
            # Medium sensitivities for balanced results
            self.artifact_sensitivity_slider.setValue(50)  # 0.50
            self.duplicate_sensitivity_slider.setValue(70)  # 0.70
        else:  # Aggressive
            # Higher sensitivities to remove more artifacts and duplicates
            self.artifact_sensitivity_slider.setValue(70)  # 0.70
            self.duplicate_sensitivity_slider.setValue(90)  # 0.90

        # Update the sensitivity labels
        artifact_sensitivity = self.artifact_sensitivity_slider.value() / 100.0
        duplicate_sensitivity = self.duplicate_sensitivity_slider.value() / 100.0

        self.artifact_sensitivity_label.setText(f"{artifact_sensitivity:.2f}")
        self.duplicate_sensitivity_label.setText(f"{duplicate_sensitivity:.2f}")

        logger.info(f"Set artifact sensitivity to {artifact_sensitivity:.2f} and duplicate sensitivity to {duplicate_sensitivity:.2f}")

    @Slot()
    def show_grain_statistics(self):
        """Show the grain statistics dialog with detailed statistical reports."""
        if self.grain_df is None or self.grain_df.empty:
            self._show_error_message("No Data", "No grain analysis results available. Please segment the image first.")
            return

        if self.grain_current_scale_factor is None:
            self._show_error_message("Scale Not Set", "Please set the image scale before generating statistics.")
            return

        # Log DataFrame structure for debugging
        logger.info(f"DataFrame columns: {self.grain_df.columns.tolist()}")
        logger.info(f"DataFrame sample: {self.grain_df.head(1).to_dict('records')}")

        # Prepare sample information
        sample_info = {
            "identity": self.grain_image_filename if self.grain_image_filename else "Unknown",
            "analyst_date": datetime.now().strftime("%Y-%m-%d"),
            "type": "Grain Sample"
        }

        # Show the statistics dialog
        logger.info(f"Showing grain statistics dialog for {len(self.grain_df)} grains")
        self.update_status(f"Generating statistical report for {len(self.grain_df)} grains...")

        # Get the selected measurement type
        measurement_type = "length" if hasattr(self, 'length_radio') and self.length_radio.isChecked() else "ecd"
        logger.info(f"Using measurement type: {measurement_type}")

        try:
            # Show the dialog with the selected measurement type
            dialog = show_grain_statistics_dialog(
                self,
                self.grain_df,
                self.grain_current_scale_factor,
                sample_info,
                measurement_type=measurement_type
            )

            # Save the statistics report to the project state directory
            self.save_grain_statistics_report(dialog)

            self.update_status(f"Statistical report generated successfully using {measurement_type} measurement.")
        except Exception as e:
            logger.exception(f"Error generating grain statistics: {e}")
            self._show_error_message("Statistics Error", f"Error generating statistics: {str(e)}")
            self.update_status("Error generating statistical report.")

    def _clean_directory(self, directory, file_pattern="*.png"):
        """Clean up files matching the pattern in the specified directory.

        Args:
            directory: Directory to clean
            file_pattern: File pattern to match (default: "*.png")
        """
        try:
            import glob
            # Find all files matching the pattern
            files_to_remove = glob.glob(os.path.join(directory, file_pattern))
            if files_to_remove:
                logger.info(f"Cleaning up {len(files_to_remove)} old files in {directory}")
                for file_path in files_to_remove:
                    try:
                        os.remove(file_path)
                        logger.debug(f"Removed old file: {file_path}")
                    except Exception as e:
                        logger.warning(f"Failed to remove file {file_path}: {e}")
        except Exception as e:
            logger.warning(f"Error cleaning directory {directory}: {e}")

    def save_grain_statistics_report(self, dialog):
        """Save the grain statistics report to the project state directory."""
        if not hasattr(self, 'project') or not self.project or not hasattr(self, 'grain_image_file_path') or not self.grain_image_file_path:
            logger.warning("Cannot save statistics report: No project or image file path")
            return

        try:
            # Find the image_id for this file path
            image_id = None
            for img_id, info in self.project.images.items():
                if os.path.join(self.project.temp_dir, info.filepath) == self.grain_image_file_path:
                    image_id = img_id
                    break

            if not image_id:
                logger.warning(f"Cannot save statistics report: No image ID found for {self.grain_image_file_path}")
                return

            # Create the statistics directory in the state folder
            stats_dir = os.path.join(self.project.temp_dir, "state", "grain_analysis", image_id, "statistics")
            os.makedirs(stats_dir, exist_ok=True)

            # Save the CSV report
            try:
                # Clean up old CSV files before saving new ones
                logger.info(f"Cleaning up old CSV files in {stats_dir}")
                self._clean_directory(stats_dir, "*.csv")

                csv_path = os.path.join(stats_dir, "grain_statistics.csv")
                logger.info(f"Saving statistics report to {csv_path}")
                # Pass show_message=False to suppress the message box
                csv_file = dialog.export_to_csv(csv_path, show_message=False)

                if csv_file:
                    logger.info(f"Statistics report saved to {csv_file}")
                else:
                    logger.warning("Failed to save statistics report")
            except Exception as e:
                logger.exception(f"Error saving CSV report: {e}")

            # Save the plots
            try:
                plots_dir = os.path.join(stats_dir, "plots")
                os.makedirs(plots_dir, exist_ok=True)

                # Clean up old plot files before saving new ones
                logger.info(f"Cleaning up old plot files in {plots_dir}")
                self._clean_directory(plots_dir, "*.png")

                logger.info(f"Saving plots to {plots_dir}")
                # Pass show_message=False to suppress the message box
                plot_files = dialog.save_plots(plots_dir, show_message=False)

                if plot_files:
                    logger.info(f"Plots saved: {', '.join(plot_files)}")
                else:
                    logger.warning("Failed to save plots")
            except Exception as e:
                logger.exception(f"Error saving plots: {e}")

            logger.info(f"Statistics report and plots saved to {stats_dir}")

            # Mark the state as modified so it will be saved
            self.state_modified = True

        except Exception as e:
            logger.exception(f"Error saving statistics report: {e}")

    def update_action_states(self, processing: Optional[bool] = None):
        """
        Update the enabled/disabled state of all action buttons based on the current application state.
        This method has been enhanced to handle edge cases and ensure consistent button states.

        Args:
            processing: Override the processing state detection. If None, detect automatically.
        """
        # Check if image is loaded
        image_loaded = self.grain_uploaded_image is not None

        # Determine number of annotations with robust type checking
        num_annotations = 0
        if self.grain_annotations is not None:
            if isinstance(self.grain_annotations, list):
                num_annotations = len(self.grain_annotations)
                logger.debug(f"Found {num_annotations} annotations in list")
            elif isinstance(self.grain_annotations, torch.Tensor):
                num_annotations = self.grain_annotations.shape[0]
                logger.debug(f"Found {num_annotations} annotations in tensor")
            elif isinstance(self.grain_annotations, np.ndarray):
                num_annotations = len(self.grain_annotations)
                logger.debug(f"Found {num_annotations} annotations in numpy array")
            else:
                logger.debug(f"Unknown annotation type: {type(self.grain_annotations)}")

        # Check if we have a dataframe with results
        has_dataframe = hasattr(self, 'grain_df') and self.grain_df is not None and not self.grain_df.empty

        # If we have a dataframe but no annotations, we should still consider annotations to exist
        # This handles the case where annotations are available in the project but not loaded into memory
        if has_dataframe and num_annotations == 0:
            num_annotations = len(self.grain_df)
            logger.debug(f"Using dataframe length as annotation count: {num_annotations}")

        # Determine if annotations exist
        annotations_exist = num_annotations > 0
        logger.debug(f"annotations_exist = {annotations_exist}, num_annotations = {num_annotations}")

        # Check if results exist in the dataframe
        results_exist = has_dataframe

        # Check if scale is set
        scale_is_set = self.grain_current_scale_factor is not None

        # Check if model is loaded
        model_is_loaded = self.grain_model is not None

        # Determine if processing is happening
        if processing is None:
            processing = (self.grain_processing_thread and self.grain_processing_thread.isRunning()) or \
                         (self.grain_patch_processing_thread and self.grain_patch_processing_thread.isRunning())
        elif processing:
            model_is_loaded = False

        # For previously segmented images, we should enable certain buttons even if annotations aren't loaded yet
        # This is because the annotations might be available in the project but not loaded into memory
        has_results_in_df = results_exist

        # Enable processing if we have an image, scale, and model
        can_process = image_loaded and scale_is_set and model_is_loaded and not processing

        # Enable recalculate if we have annotations or results in the dataframe
        can_recalculate = (annotations_exist or has_results_in_df) and scale_is_set and not processing

        # Other action states with improved logic
        can_save_results = results_exist and not processing
        can_export_coco = (annotations_exist or has_results_in_df) and not processing
        can_plot = results_exist and not processing
        can_crop = image_loaded and not processing
        can_save_scale = scale_is_set and not processing
        can_load_scale = image_loaded and not processing
        can_reset_scale = scale_is_set and not processing
        can_reset_app = not processing
        can_interact = not processing

        # Always enable reload button if we have annotations or dataframe results
        # This is critical for fixing the issue where the button is disabled when reloading segmented images
        can_reload_results = (annotations_exist or results_exist) and not processing

        # Force enable reload button if we have a dataframe, even if annotations aren't loaded yet
        if has_dataframe and not processing:
            can_reload_results = True

        # Enable statistics button if we have results and scale is set
        can_show_statistics = results_exist and scale_is_set and not processing

        # Log detailed button states for debugging
        logger.debug(f"Button states: can_process={can_process}, can_recalculate={can_recalculate}, " +
                    f"can_show_statistics={can_show_statistics}, has_results_in_df={has_results_in_df}, " +
                    f"annotations_exist={annotations_exist}, results_exist={results_exist}, scale_is_set={scale_is_set}")
        logger.debug(f"Save & Show Visualization button enabled: {can_reload_results} (annotations_exist={annotations_exist}, results_exist={results_exist})")

        # Update buttons specific to this widget with improved error handling
        try:
            # Image manipulation buttons
            if hasattr(self, 'crop_action_button'):
                self.crop_action_button.setEnabled(can_crop)

            # Results management buttons
            if hasattr(self, 'save_results_action_button'):
                self.save_results_action_button.setEnabled(can_save_results)
            if hasattr(self, 'export_coco_action_button'):
                self.export_coco_action_button.setEnabled(can_export_coco)
            if hasattr(self, 'plot_action_button'):
                self.plot_action_button.setEnabled(can_plot)

            # Scale management buttons
            if hasattr(self, 'save_scale_action_button'):
                self.save_scale_action_button.setEnabled(can_save_scale)
            if hasattr(self, 'load_scale_action_button'):
                self.load_scale_action_button.setEnabled(can_load_scale)
            if hasattr(self, 'reset_scale_action_button'):
                self.reset_scale_action_button.setEnabled(can_reset_scale)

            # App control buttons
            if hasattr(self, 'reset_action_button'):
                self.reset_action_button.setEnabled(can_reset_app)

            # Critical functionality buttons - ensure these are always properly enabled
            if hasattr(self, 'reload_results_button'):
                self.reload_results_button.setEnabled(can_reload_results)
                # Force enable if we have results to ensure it's never incorrectly disabled
                if results_exist and not processing:
                    self.reload_results_button.setEnabled(True)

            if hasattr(self, 'stats_button'):
                self.stats_button.setEnabled(can_show_statistics)
                # Force enable if we have results and scale to ensure it's never incorrectly disabled
                if results_exist and scale_is_set and not processing:
                    self.stats_button.setEnabled(True)

            if hasattr(self, 'process_button'):
                self.process_button.setEnabled(can_process)
            if hasattr(self, 'recalculate_button'):
                self.recalculate_button.setEnabled(can_recalculate)
                # Force enable if we have results and scale to ensure it's never incorrectly disabled
                if (annotations_exist or has_results_in_df) and scale_is_set and not processing:
                    self.recalculate_button.setEnabled(True)

            # Parameter widgets
            if hasattr(self, 'fastsam_params_widget'):
                self.fastsam_params_widget.setEnabled(can_interact)
            if hasattr(self, 'mobilesam_params_widget'):
                self.mobilesam_params_widget.setEnabled(can_interact and MOBILE_SAM_AVAILABLE)
            if hasattr(self, 'fastsam_radio'):
                self.fastsam_radio.setEnabled(can_interact)
            if hasattr(self, 'mobilesam_radio'):
                self.mobilesam_radio.setEnabled(can_interact and MOBILE_SAM_AVAILABLE)

            # Scale input widgets
            if hasattr(self, 'real_world_length_edit'):
                self.real_world_length_edit.setEnabled(can_interact)
            if hasattr(self, 'manual_scale_edit'):
                self.manual_scale_edit.setEnabled(can_interact)
            set_scale_button = self.findChild(QPushButton, "setManualScaleButton")
            if set_scale_button:
                set_scale_button.setEnabled(can_interact)

            # View mode buttons
            if hasattr(self, 'select_mode_button'):
                self.select_mode_button.setEnabled(can_interact)
            if hasattr(self, 'pan_mode_button'):
                self.pan_mode_button.setEnabled(can_interact)
            if hasattr(self, 'scale_mode_button'):
                self.scale_mode_button.setEnabled(can_interact)

            # Update delete button state in results widget
            if hasattr(self, 'results_widget'):
                self.results_widget.update_delete_button_state()

        except Exception as e:
            # Log any errors but don't crash
            logger.error(f"Error updating button states: {e}")
            logger.debug("Continuing despite button state update error")


    @Slot(str, str)
    def _show_error_message(self, title: str, message: str):
        if QThread.currentThread() != self.thread():
             QMetaObject.invokeMethod(self, "_show_error_message", Qt.QueuedConnection, Q_ARG(str, title), Q_ARG(str, message))
        else: QMessageBox.critical(self, title, message)

    # --- Cleanup ---
    def set_project(self, project):
        """Sets the project for the grain analysis widget.

        Args:
            project: The project to set
        """
        logger.info(f"Setting project for grain analysis widget: {project.name if hasattr(project, 'name') else 'Unknown'}")
        self.project = project

        # Load state for the current image if available
        if hasattr(self, 'grain_image_file_path') and self.grain_image_file_path:
            logger.info(f"Loading state for current image: {self.grain_image_file_path}")
            self.load_grain_analysis_state()

    def cleanup_before_exit(self):
        # (Adapt from GrainSightApp)
        logger.info("Cleaning up GrainAnalysisWidget...")

        # Save all pending changes before exiting
        self.save_all_pending_changes()

        # If current image has unsaved changes, save it too
        if hasattr(self, 'state_modified') and self.state_modified and \
           hasattr(self, 'grain_image_file_path') and self.grain_image_file_path:
            logger.info("Saving current image state before exit")
            self.save_grain_analysis_state()

        self.stop_processing_thread()
        # Close plot dialogs
        for dialog in self.grain_plot_dialogs[:]:
             if dialog: dialog.close()
        self.grain_plot_dialogs.clear()
        try: import matplotlib.pyplot as plt; plt.close('all')
        except Exception: pass
        # Release model
        if hasattr(self, 'grain_model'): del self.grain_model; self.grain_model = None
        if self.grain_device.type == 'cuda':
             try: torch.cuda.empty_cache()
             except: pass

    # --- Manual Save/Load Methods for Better Performance ---
    @Slot()
    def save_current_results(self):
        """Save current analysis results to a user-selected file.

        This replaces automatic disk persistence for better performance.
        Only saves when user explicitly requests it.
        """
        if not hasattr(self, 'grain_image_file_path') or not self.grain_image_file_path:
            QMessageBox.warning(self, "No Image", "No image is currently loaded.")
            return

        # Check if we have results to save
        has_results = False
        if hasattr(self, 'grain_df') and self.grain_df is not None and not self.grain_df.empty:
            has_results = True
        elif hasattr(self, 'grain_annotations') and self.grain_annotations is not None:
            has_results = True

        if not has_results:
            QMessageBox.warning(self, "No Results", "No analysis results to save. Please run grain analysis first.")
            return

        # Get save file path
        image_name = os.path.splitext(os.path.basename(self.grain_image_file_path))[0]
        default_filename = f"{image_name}_grain_analysis.json"

        save_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Grain Analysis Results",
            os.path.join(self.grain_last_save_dir or os.path.dirname(self.grain_image_file_path), default_filename),
            "JSON Files (*.json);;All Files (*)"
        )

        if not save_path:
            return

        try:
            # Create state dictionary with all current results
            state = {
                'image_path': self.grain_image_file_path,
                'image_name': os.path.basename(self.grain_image_file_path),
                'timestamp': datetime.now().isoformat(),
                'scale_factor': self.grain_current_scale_factor,
                'scale_unit': self.scale_unit_combo.currentText() if hasattr(self, 'scale_unit_combo') else 'μm',
            }

            # Add scale line if available
            if hasattr(self, 'view') and hasattr(self.view, 'scale_line') and self.view.scale_line:
                state['scale_line'] = {
                    'start': [self.view.scale_line.line().p1().x(), self.view.scale_line.line().p1().y()],
                    'end': [self.view.scale_line.line().p2().x(), self.view.scale_line.line().p2().y()]
                }

            # Add dataframe if available
            if hasattr(self, 'grain_df') and self.grain_df is not None and not self.grain_df.empty:
                # Convert dataframe to JSON-serializable format
                state['dataframe'] = self.grain_df.to_dict('records')
                state['num_grains'] = len(self.grain_df)

            # Add annotations if available (but not the full data to keep file size reasonable)
            if hasattr(self, 'grain_annotations') and self.grain_annotations is not None:
                if isinstance(self.grain_annotations, list):
                    state['num_annotations'] = len(self.grain_annotations)
                elif hasattr(self.grain_annotations, 'shape'):
                    state['num_annotations'] = self.grain_annotations.shape[0]
                else:
                    state['num_annotations'] = 1

                # Note: We don't save the full annotations to keep file size manageable
                # The user would need to re-run analysis to get the full annotation data
                state['has_annotations'] = True

            # Save to file
            with open(save_path, 'w') as f:
                json.dump(state, f, indent=2, default=str)

            self.update_status(f"Results saved to {os.path.basename(save_path)}")
            QMessageBox.information(self, "Save Successful", f"Analysis results saved to:\n{save_path}")

            # Update last save directory
            self.grain_last_save_dir = os.path.dirname(save_path)

        except Exception as e:
            logger.error(f"Error saving results: {e}")
            QMessageBox.critical(self, "Save Error", f"Failed to save results:\n{str(e)}")

    @Slot()
    def load_saved_results(self):
        """Load previously saved analysis results from a user-selected file.

        This replaces automatic disk loading for better performance.
        Only loads when user explicitly requests it.
        """
        # Get load file path
        load_path, _ = QFileDialog.getOpenFileName(
            self,
            "Load Grain Analysis Results",
            self.grain_last_save_dir or os.getcwd(),
            "JSON Files (*.json);;All Files (*)"
        )

        if not load_path:
            return

        try:
            # Load state from file
            with open(load_path, 'r') as f:
                state = json.load(f)

            # Validate the loaded state
            if 'image_name' not in state:
                QMessageBox.warning(self, "Invalid File", "The selected file does not appear to be a valid grain analysis results file.")
                return

            # Check if we have an image loaded
            if not hasattr(self, 'grain_image_file_path') or not self.grain_image_file_path:
                QMessageBox.information(self, "Load Results",
                    f"Results are for image: {state.get('image_name', 'Unknown')}\n"
                    "Please load the corresponding image first, then load these results again.")
                return

            # Check if the results match the current image
            current_image_name = os.path.basename(self.grain_image_file_path)
            saved_image_name = state.get('image_name', '')

            if current_image_name != saved_image_name:
                reply = QMessageBox.question(self, "Image Mismatch",
                    f"The saved results are for image '{saved_image_name}' but you have '{current_image_name}' loaded.\n\n"
                    "Do you want to load the results anyway? This may cause incorrect visualization.",
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.No:
                    return

            # Load scale information
            if 'scale_factor' in state and state['scale_factor'] is not None:
                self.grain_current_scale_factor = float(state['scale_factor'])
                self.scale_factor_label.setText(f"<b>Scale: {self.grain_current_scale_factor:.4f} µm/pixel</b>")
                self.manual_scale_edit.setText(f"{self.grain_current_scale_factor:.6f}")

            # Load scale unit
            if 'scale_unit' in state and hasattr(self, 'scale_unit_combo'):
                unit = state['scale_unit']
                index = self.scale_unit_combo.findText(unit)
                if index >= 0:
                    self.scale_unit_combo.setCurrentIndex(index)

            # Load scale line
            if 'scale_line' in state and hasattr(self, 'view'):
                scale_line_data = state['scale_line']
                start_point = QPointF(scale_line_data['start'][0], scale_line_data['start'][1])
                end_point = QPointF(scale_line_data['end'][0], scale_line_data['end'][1])
                self.view.set_scale_line_points(start_point, end_point)

            # Load dataframe
            if 'dataframe' in state:
                import pandas as pd
                self.grain_df = pd.DataFrame(state['dataframe'])
                self.results_widget.populate(self.grain_df)
                logger.info(f"Loaded dataframe with {len(self.grain_df)} rows")

            # Update UI state
            self.update_scale_reminder(is_set=self.grain_current_scale_factor is not None)
            self.update_action_states()

            # Show success message
            num_grains = state.get('num_grains', 0)
            timestamp = state.get('timestamp', 'Unknown')

            self.update_status(f"Loaded results: {num_grains} grains from {os.path.basename(load_path)}")

            QMessageBox.information(self, "Load Successful",
                f"Successfully loaded analysis results:\n\n"
                f"Image: {saved_image_name}\n"
                f"Grains: {num_grains}\n"
                f"Saved: {timestamp}\n\n"
                f"Note: Polygon visualization is not available from saved results. "
                f"Re-run analysis to get interactive grain visualization.")

            # Update last save directory
            self.grain_last_save_dir = os.path.dirname(load_path)

        except Exception as e:
            logger.error(f"Error loading results: {e}")
            QMessageBox.critical(self, "Load Error", f"Failed to load results:\n{str(e)}")
