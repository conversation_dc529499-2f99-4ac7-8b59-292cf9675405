# src/gui/ui/base_ui.py

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
    QHBoxLayout, QLabel, QPushButton, QFileDialog, QMessageBox, QSlider, QFrame,
    QSizePolicy, QMenuBar, QCheckBox, QProgressBar, QGridLayout, QLineEdit,
    QComboBox, QTabWidget, QColorDialog, QSpinBox, QDoubleSpinBox, QDialog,
    QFormLayout, QGroupBox, QScrollArea, QStyleFactory)

from PySide6.QtGui import QPixmap, QImage, QAction, QPalette, QColor
from PySide6.QtCore import Qt, QRect

class BaseUI(QMainWindow):
    """Base class for VisionLab Ai UI, containing common UI setup methods."""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("VisionLab Ai")
        self.setGeometry(100, 100, 1600, 900)
        self.setMinimumSize(1200, 800)

        # Set window flags to remove the title bar
        self.setWindowFlags(self.windowFlags() | Qt.FramelessWindowHint)

        # Hide the menu bar
        self.menuBar().setVisible(False)
        self.menuBar().setMaximumHeight(0)

        # UI elements that will be needed in the main class
        self.stacked_widget = None
        self.process_btn = None
        self.analysis_btn = None
        self.settings_btn = None
        self.trainable_btn = None
        self.ai_assistant_btn = None
        self.point_counting_btn = None

        # Menu actions
        self.open_action = None
        self.save_action = None
        self.exit_action = None
        self.rand_colors_action = None
        self.pick_colors_action = None
        self.calc_percent_action = None
        self.start_seg_action = None
        self.merge_seg_action = None
        self.preprocess_action = None
        self.about_action = None

    def setup_ui(self):
        """Sets up the main UI components."""
        # Skip creating the old menu bar
        # self.create_menu()
        central_widget = QWidget(self)
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Set the window to have no margins
        self.setContentsMargins(0, 0, 0, 0)

        # Create custom title bar
        self.create_title_bar(main_layout)

        # Ensure the window has no margin at the top
        self.setStyleSheet("""
            QMainWindow {
                margin: 0;
                padding: 0;
                border: none;
            }
        """)

        # Stacked Widget for Pages
        self.stacked_widget = QTabWidget()
        main_layout.addWidget(self.stacked_widget)

        # Make the tab bar visible and apply styling
        self.stacked_widget.setTabBarAutoHide(False)
        self.stacked_widget.tabBar().setVisible(True)
        self.stacked_widget.setDocumentMode(True)  # Make tabs look more modern

        # Set tab position to top
        self.stacked_widget.setTabPosition(QTabWidget.North)

        # Apply initial tab styling
        self.update_tab_styling()

    def update_tab_styling(self):
        """Updates the tab styling based on the current theme."""
        # Get the current theme from settings
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
            theme = settings.value("app/theme", "Dark Theme").lower()
            is_dark = "dark" in theme
        except Exception:
            # Default to dark theme if there's an error
            is_dark = True

        # Define colors based on theme
        if is_dark:
            # Dark theme colors (using palette for more dynamic theming)
            # bg_color = "#1e1e1e" # Old hardcoded
            bg_color = QColor(self.palette().color(QPalette.Window)).name()
            border_color = QColor(self.palette().color(QPalette.Mid)).name()
            text_color = QColor(self.palette().color(QPalette.WindowText)).name()
            hover_bg_color = QColor(self.palette().color(QPalette.AlternateBase)).name() # Example, adjust as needed
            hover_text_color = QColor(self.palette().color(QPalette.HighlightedText)).name()
            selected_bg_color = QColor(self.palette().color(QPalette.Highlight)).name()
            selected_text_color = QColor(self.palette().color(QPalette.HighlightedText)).name()
            selected_border_color = QColor(self.palette().color(QPalette.Highlight)).name()
        else:
            # Light theme colors
            bg_color = "#f0f0f0"
            border_color = "#d0d0d0"
            text_color = "#333333"
            hover_bg_color = "#e0e0e0"
            hover_text_color = "#000000"
            selected_bg_color = "#007acc"  # Updated to the requested blue color
            selected_text_color = "white"
            selected_border_color = "#007acc"  # Updated to the requested blue color

        # Apply theme-aware styling to the tab bar
        self.stacked_widget.setStyleSheet(f"""
            QTabWidget {{
                padding: 0px;
                margin: 0px;
                margin-top: 0px;
            }}

            QTabWidget::pane {{
                border: none;
                top: 0px;
                padding: 0px;
                margin: 0px;
                margin-top: 0px;
            }}

            QTabBar {{
                background-color: {bg_color};
                border-bottom: 1px solid {border_color};
                padding: 0px;
                margin: 0px;
                margin-top: 0px;
            }}

            QTabBar::tab {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                padding: 6px 12px;  /* Balanced padding */
                font-size: 14px;
                font-weight: 500;
                margin: 0px 2px;
                margin-top: 0px;
                border-radius: 0px;
                min-width: 80px;  /* Balanced min-width */
                min-height: 14px;  /* Balanced min-height */
            }}

            QTabBar::tab:hover {{
                background-color: {hover_bg_color};
                color: {hover_text_color};
            }}

            QTabBar::tab:selected {{
                background-color: {selected_bg_color};
                color: {selected_text_color};
                font-weight: bold;
                border-bottom: 3px solid {selected_border_color};
            }}

            QTabBar::tab:!selected {{
                background-color: {bg_color};
                border-right: 1px solid {border_color}; /* Add right border for separation */
                margin-right: 1px; /* Add a small margin to make the border distinct */
            }}

            QTabBar::tab:selected {{
                background-color: {selected_bg_color};
                color: {selected_text_color};
                font-weight: bold;
                border-bottom: 3px solid {selected_border_color};
                border-right: 1px solid {selected_border_color}; /* Ensure selected tab also has a right border if others do */
                 margin-right: 1px;
             }}
        """)

    def setup_navigation_bar(self, main_layout):
        """Creates the navigation bar."""
        self.nav_bar = QFrame()
        self.nav_bar.setObjectName("MainNavigationBar")
        # Styling will be handled by the theme or a central stylesheet
        # For now, ensure it's distinct or uses palette colors
        self.nav_bar.setStyleSheet("""
            QFrame#MainNavigationBar {
                background-color: palette(window); 
                border-bottom: 1px solid palette(mid);
            }
        """)
        nav_layout = QHBoxLayout(self.nav_bar)
        nav_layout.setSpacing(4)
        nav_layout.setContentsMargins(5, 0, 5, 0)
        main_layout.addWidget(self.nav_bar)

        # Navigation buttons (using QPushButton for navigation tabs)
        self.project_hub_btn = QPushButton("Project Hub")
        self.process_btn = QPushButton("Unsupervised Segmentation")
        self.trainable_btn = QPushButton("Trainable Segmentation")
        self.point_counting_btn = QPushButton("Point Counting")
        self.grain_analysis_btn = QPushButton("Grain Analysis")
        self.advanced_segmentation_btn = QPushButton("Advanced Segmentation")
        self.analysis_btn = QPushButton("Image Lab")
        self.settings_btn = QPushButton("Settings")
        self.ai_assistant_btn = QPushButton("AI Assistant")

        for btn in [self.project_hub_btn, self.process_btn, self.trainable_btn, self.point_counting_btn, self.grain_analysis_btn, self.advanced_segmentation_btn, self.analysis_btn, self.settings_btn, self.ai_assistant_btn]:
            btn.setMinimumWidth(120)
            # Theme-aware styling for navigation buttons
            # Colors will be derived from the theme settings in update_tab_styling
            # For now, this is a placeholder to remove direct hardcoding here
            # Actual styling should ideally be centralized or use QPalette
            # Get theme colors from update_tab_styling logic
            try:
                from PySide6.QtCore import QSettings
                settings = QSettings("VisionLab Ai", "VisionLab_Ai_V4")
                theme = settings.value("app/theme", "Dark Theme").lower()
                is_dark = "dark" in theme
            except Exception:
                is_dark = True # Default

            if is_dark:
                nav_btn_bg = QColor(self.palette().color(QPalette.Window)).name()
                nav_btn_text = QColor(self.palette().color(QPalette.WindowText)).name()
                nav_btn_hover_bg = QColor(self.palette().color(QPalette.AlternateBase)).name()
                nav_btn_hover_text = QColor(self.palette().color(QPalette.BrightText)).name()
                nav_btn_pressed_bg = QColor(self.palette().color(QPalette.Button)).darker(120).name()
                nav_btn_checked_bg = QColor(self.palette().color(QPalette.Highlight)).name()
                nav_btn_checked_text = QColor(self.palette().color(QPalette.HighlightedText)).name()
                nav_btn_checked_border = QColor(self.palette().color(QPalette.Highlight)).name()
            else:
                nav_btn_bg = QColor(self.palette().color(QPalette.Window)).name()
                nav_btn_text = QColor(self.palette().color(QPalette.WindowText)).name()
                nav_btn_hover_bg = QColor(self.palette().color(QPalette.AlternateBase)).name()
                nav_btn_hover_text = QColor(self.palette().color(QPalette.Text)).name()
                nav_btn_pressed_bg = QColor(self.palette().color(QPalette.Button)).darker(110).name()
                nav_btn_checked_bg = QColor(self.palette().color(QPalette.Highlight)).name()
                nav_btn_checked_text = QColor(self.palette().color(QPalette.HighlightedText)).name()
                nav_btn_checked_border = QColor(self.palette().color(QPalette.Highlight)).name()

            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {nav_btn_bg};
                    color: {nav_btn_text};
                    border: none;
                    padding: 10px 18px;
                    font-size: 14px;
                    font-weight: 500;
                    margin: 0px 2px;
                    border-radius: 0px;
                }}
                QPushButton:hover {{
                    background-color: {nav_btn_hover_bg};
                    color: {nav_btn_hover_text};
                }}
                QPushButton:pressed {{
                    background-color: {nav_btn_pressed_bg};
                }}
                QPushButton:checked {{
                    background-color: {nav_btn_checked_bg};
                    color: {nav_btn_checked_text};
                    font-weight: bold;
                    border-bottom: 3px solid {nav_btn_checked_border};
                }}
            """)
            btn.setCheckable(True)
            nav_layout.addWidget(btn)

        nav_layout.addStretch()
        self.project_hub_btn.setChecked(True)

    def create_title_bar(self, main_layout):
        """Creates a custom title bar for the frameless window."""
        # Create title bar frame
        title_bar = QFrame()
        title_bar.setFixedHeight(30)
        # Apply theme-aware styling to the title bar
        # This will be updated by the theme change handler if one exists
        # For now, let's set a default that can be overridden
        title_bar.setObjectName("CustomTitleBar")
        title_bar.setStyleSheet("""
            QFrame#CustomTitleBar {
                background-color: palette(window);
                border: none;
            }
        """)
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(5, 0, 5, 0)
        title_layout.setSpacing(0)

        # Add window title
        title_label = QLabel("VisionLab Ai V4")
        title_label.setStyleSheet("color: #cccccc; font-weight: bold;")
        title_layout.addWidget(title_label)

        # Add spacer
        title_layout.addStretch()

        # Add window control buttons
        min_button = QPushButton("🗕")
        min_button.setFixedSize(30, 30)
        min_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #cccccc;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #333333;
            }
            QPushButton:pressed {
                background-color: #444444;
            }
        """)
        min_button.clicked.connect(self.showMinimized)

        max_button = QPushButton("🗖")
        max_button.setFixedSize(30, 30)
        max_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #cccccc;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #333333;
            }
            QPushButton:pressed {
                background-color: #444444;
            }
        """)
        max_button.clicked.connect(self.toggle_maximize)

        close_button = QPushButton("✕")
        close_button.setFixedSize(30, 30)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #cccccc;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e81123;
                color: white;
            }
            QPushButton:pressed {
                background-color: #f1707a;
            }
        """)
        close_button.clicked.connect(self.close)

        title_layout.addWidget(min_button)
        title_layout.addWidget(max_button)
        title_layout.addWidget(close_button)

        # Add title bar to main layout
        main_layout.addWidget(title_bar)

        # Store reference to title bar for mouse events
        self.title_bar = title_bar

        # Install event filter for mouse events
        title_bar.mousePressEvent = self.title_bar_mouse_press
        title_bar.mouseMoveEvent = self.title_bar_mouse_move
        title_bar.mouseDoubleClickEvent = self.title_bar_double_click

    def title_bar_mouse_press(self, event):
        """Handle mouse press events on the title bar."""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()

    def title_bar_mouse_move(self, event):
        """Handle mouse move events on the title bar."""
        if hasattr(self, 'drag_position') and event.buttons() == Qt.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()

    def title_bar_double_click(self, event):
        """Handle double click events on the title bar."""
        self.toggle_maximize()
        event.accept()

    def toggle_maximize(self):
        """Toggle between maximized and normal window state."""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()

    def toggle_navigation_bar(self):
        """Toggles the visibility of the navigation bar."""
        if hasattr(self, 'nav_bar'):
            self.nav_bar.setVisible(not self.nav_bar.isVisible())