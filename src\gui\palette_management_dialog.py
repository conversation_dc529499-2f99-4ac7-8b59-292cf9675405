from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QListWidget, QListWidgetItem, QFileDialog, QMessageBox, QWidget, QScrollArea)
from PySide6.QtGui import QColor
from PySide6.QtCore import Qt, Signal
import json
import os
import logging

logger = logging.getLogger(__name__)

class PalettePreviewWidget(QWidget):
    """Widget to display a preview of a palette."""
    
    def __init__(self, palette_data, parent=None):
        super().__init__(parent)
        self.palette_data = palette_data
        self.setMinimumHeight(30)
        self.setMaximumHeight(30)
    
    def paintEvent(self, event):
        """Paint the palette preview."""
        import PySide6.QtGui as QtGui
        painter = QtGui.QPainter(self)
        
        # Get the number of colors in the palette
        num_colors = len(self.palette_data)
        if num_colors == 0:
            return
        
        # Calculate the width of each color block
        width = self.width() / num_colors
        
        # Draw each color block
        x = 0
        for name, color_str in self.palette_data.items():
            # Parse the color
            r, g, b = map(int, color_str.split(','))
            color = QColor(r, g, b)
            
            # Draw the color block
            painter.fillRect(x, 0, width, self.height(), color)
            
            # Move to the next position
            x += width

class PaletteManagementDialog(QDialog):
    """Dialog for managing custom palettes."""
    
    palette_deleted = Signal(str)  # Signal emitted when a palette is deleted
    palettes_imported = Signal()   # Signal emitted when palettes are imported
    
    def __init__(self, custom_palettes, parent=None):
        """Initialize the palette management dialog.
        
        Args:
            custom_palettes (dict): Dictionary of custom palettes
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("Manage Custom Palettes")
        self.setModal(True)
        self.resize(600, 500)
        
        self.custom_palettes = custom_palettes.copy()
        
        self.setup_ui()
        self.populate_palette_list()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        main_layout = QVBoxLayout(self)
        
        # Instructions
        instructions = QLabel(
            "Manage your custom palettes. Select a palette to preview it, "
            "or use the buttons below to export, import, or delete palettes."
        )
        instructions.setWordWrap(True)
        main_layout.addWidget(instructions)
        
        # Palette list
        self.palette_list = QListWidget()
        self.palette_list.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        self.palette_list.currentItemChanged.connect(self.on_palette_selected)
        main_layout.addWidget(self.palette_list)
        
        # Preview area
        preview_label = QLabel("Preview:")
        main_layout.addWidget(preview_label)
        
        self.preview_scroll = QScrollArea()
        self.preview_scroll.setWidgetResizable(True)
        self.preview_scroll.setMinimumHeight(100)
        self.preview_scroll.setMaximumHeight(100)
        
        self.preview_widget = QWidget()
        self.preview_layout = QVBoxLayout(self.preview_widget)
        
        self.palette_preview = QLabel("Select a palette to preview")
        self.preview_layout.addWidget(self.palette_preview)
        
        self.preview_scroll.setWidget(self.preview_widget)
        main_layout.addWidget(self.preview_scroll)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.export_button = QPushButton("Export Selected")
        self.export_button.clicked.connect(self.export_palette)
        button_layout.addWidget(self.export_button)
        
        self.export_all_button = QPushButton("Export All")
        self.export_all_button.clicked.connect(self.export_all_palettes)
        button_layout.addWidget(self.export_all_button)
        
        self.import_button = QPushButton("Import")
        self.import_button.clicked.connect(self.import_palettes)
        button_layout.addWidget(self.import_button)
        
        self.delete_button = QPushButton("Delete Selected")
        self.delete_button.clicked.connect(self.delete_palette)
        button_layout.addWidget(self.delete_button)
        
        main_layout.addLayout(button_layout)
        
        # Close button
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        
        self.close_button = QPushButton("Close")
        self.close_button.clicked.connect(self.accept)
        close_layout.addWidget(self.close_button)
        
        main_layout.addLayout(close_layout)
    
    def populate_palette_list(self):
        """Populate the palette list with custom palettes."""
        self.palette_list.clear()
        
        for name in sorted(self.custom_palettes.keys()):
            item = QListWidgetItem(name)
            self.palette_list.addItem(item)
    
    def on_palette_selected(self, current, previous):
        """Handle palette selection."""
        if current is None:
            self.palette_preview.setText("Select a palette to preview")
            self.export_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            return
        
        palette_name = current.text()
        if palette_name in self.custom_palettes:
            # Clear the preview layout
            while self.preview_layout.count():
                item = self.preview_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
            
            # Create a preview widget
            preview = PalettePreviewWidget(self.custom_palettes[palette_name])
            self.preview_layout.addWidget(preview)
            
            # Add a label with the palette details
            details = QLabel(f"Palette: {palette_name} ({len(self.custom_palettes[palette_name])} colors)")
            self.preview_layout.addWidget(details)
            
            # Enable buttons
            self.export_button.setEnabled(True)
            self.delete_button.setEnabled(True)
        else:
            self.palette_preview.setText("Palette not found")
            self.export_button.setEnabled(False)
            self.delete_button.setEnabled(False)
    
    def export_palette(self):
        """Export the selected palette to a JSON file."""
        current_item = self.palette_list.currentItem()
        if current_item is None:
            QMessageBox.warning(self, "Warning", "Please select a palette to export.")
            return
        
        palette_name = current_item.text()
        if palette_name not in self.custom_palettes:
            QMessageBox.warning(self, "Warning", f"Palette '{palette_name}' not found.")
            return
        
        # Get save location
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Palette",
            f"{palette_name}.palette.json",
            "JSON Files (*.json)"
        )
        
        if not file_path:
            return
        
        try:
            # Create a dictionary with just this palette
            palette_data = {palette_name: self.custom_palettes[palette_name]}
            
            # Save to file
            with open(file_path, 'w') as f:
                json.dump(palette_data, f, indent=4)
            
            QMessageBox.information(
                self,
                "Success",
                f"Palette '{palette_name}' exported to {file_path}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to export palette: {e}"
            )
    
    def export_all_palettes(self):
        """Export all palettes to a JSON file."""
        if not self.custom_palettes:
            QMessageBox.warning(self, "Warning", "No custom palettes to export.")
            return
        
        # Get save location
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export All Palettes",
            "all_palettes.json",
            "JSON Files (*.json)"
        )
        
        if not file_path:
            return
        
        try:
            # Save to file
            with open(file_path, 'w') as f:
                json.dump(self.custom_palettes, f, indent=4)
            
            QMessageBox.information(
                self,
                "Success",
                f"All palettes ({len(self.custom_palettes)}) exported to {file_path}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to export palettes: {e}"
            )
    
    def import_palettes(self):
        """Import palettes from a JSON file."""
        # Get file location
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Import Palettes",
            "",
            "JSON Files (*.json)"
        )
        
        if not file_path:
            return
        
        try:
            # Load from file
            with open(file_path, 'r') as f:
                imported_palettes = json.load(f)
            
            if not isinstance(imported_palettes, dict):
                QMessageBox.warning(
                    self,
                    "Warning",
                    "Invalid palette file format. Expected a dictionary."
                )
                return
            
            # Count new and updated palettes
            new_count = 0
            updated_count = 0
            
            # Check each imported palette
            for name, palette in imported_palettes.items():
                if not isinstance(palette, dict):
                    logger.warning(f"Skipping invalid palette '{name}': not a dictionary")
                    continue
                
                # Check if this palette already exists
                if name in self.custom_palettes:
                    updated_count += 1
                else:
                    new_count += 1
                
                # Add or update the palette
                self.custom_palettes[name] = palette
            
            # Refresh the list
            self.populate_palette_list()
            
            # Emit signal that palettes were imported
            self.palettes_imported.emit()
            
            QMessageBox.information(
                self,
                "Success",
                f"Imported {new_count} new and updated {updated_count} existing palettes."
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to import palettes: {e}"
            )
    
    def delete_palette(self):
        """Delete the selected palette."""
        current_item = self.palette_list.currentItem()
        if current_item is None:
            QMessageBox.warning(self, "Warning", "Please select a palette to delete.")
            return
        
        palette_name = current_item.text()
        if palette_name not in self.custom_palettes:
            QMessageBox.warning(self, "Warning", f"Palette '{palette_name}' not found.")
            return
        
        # Confirm deletion
        confirm = QMessageBox.question(
            self,
            "Confirm Deletion",
            f"Are you sure you want to delete the palette '{palette_name}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if confirm != QMessageBox.Yes:
            return
        
        # Delete the palette
        del self.custom_palettes[palette_name]
        
        # Refresh the list
        self.populate_palette_list()
        
        # Emit signal that a palette was deleted
        self.palette_deleted.emit(palette_name)
        
        QMessageBox.information(
            self,
            "Success",
            f"Palette '{palette_name}' deleted."
        )
    
    def get_custom_palettes(self):
        """Get the updated custom palettes."""
        return self.custom_palettes.copy()
