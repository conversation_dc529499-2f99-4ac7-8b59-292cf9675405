"""
Runtime hook to fix the 'obj' not defined error in scipy.stats._distn_infrastructure
"""
import sys
import types

# Define a custom loader for scipy.stats._distn_infrastructure
class CustomLoader:
    @staticmethod
    def create_module(spec):
        return None
    
    @staticmethod
    def exec_module(module):
        # Import the original module
        import importlib.machinery
        import importlib.util
        
        # Get the original module spec
        original_spec = importlib.machinery.PathFinder.find_spec('scipy.stats._distn_infrastructure', None)
        if original_spec is None:
            raise ImportError("Could not find scipy.stats._distn_infrastructure")
        
        # Load the original module
        original_module = importlib.util.module_from_spec(original_spec)
        original_spec.loader.exec_module(original_module)
        
        # Copy all attributes from the original module to our module
        for attr_name in dir(original_module):
            if not attr_name.startswith('__'):
                setattr(module, attr_name, getattr(original_module, attr_name))
        
        # Define 'obj' to prevent NameError
        module.obj = None

# Create a new module
module_name = 'scipy.stats._distn_infrastructure'
if module_name in sys.modules:
    # Module already loaded, patch it
    sys.modules[module_name].obj = None
else:
    # Create a new module spec with our custom loader
    spec = types.ModuleSpec(module_name, CustomLoader())
    module = types.ModuleType(module_name)
    sys.modules[module_name] = module
    CustomLoader.exec_module(module)
