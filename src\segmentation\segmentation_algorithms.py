# src/segmentation/segmentation_algorithms.py
# Contains the segmentation algorithm implemented with PyTorch (replacing TensorFlow/Keras)

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from skimage import segmentation
import os


def perform_custom_segmentation(image, params, stop_event, progress_callback=None):
    """Performs custom image segmentation."""
    class Args(object):
        def __init__(self, params):
            self.train_epoch = params.get('train_epoch', 8)
            self.mod_dim1 = params.get('mod_dim1', 64)
            self.mod_dim2 = params.get('mod_dim2', 32)
            self.gpu_id = params.get('gpu_id', 0)
            self.min_label_num = params.get('min_label_num', 6)
            self.max_label_num = params.get('max_label_num', 256)
            self.segmentation_method = params.get('segmentation_method', 'felzenszwalb')
            self.seed = params.get('seed', 1943)  # Add seed parameter, default to 1943

    args = Args(params)

    # Define the PyTorch model
    # Helper Residual Block for MyNet
    class DepthwiseSeparableConv(nn.Module):
        def __init__(self, in_channels, out_channels, kernel_size, stride, padding, bias=False):
            super(DepthwiseSeparableConv, self).__init__()
            self.depthwise = nn.Conv2d(in_channels, in_channels, kernel_size=kernel_size, 
                                       stride=stride, padding=padding, groups=in_channels, bias=bias)
            self.pointwise = nn.Conv2d(in_channels, out_channels, kernel_size=1, bias=bias)

        def forward(self, x):
            out = self.depthwise(x)
            out = self.pointwise(out)
            return out

    class ResidualBlock(nn.Module):
        def __init__(self, channels):
            super(ResidualBlock, self).__init__()
            self.conv1 = DepthwiseSeparableConv(channels, channels, kernel_size=3, stride=1, padding=1, bias=False)
            self.bn1 = nn.BatchNorm2d(channels)
            self.selu = nn.SELU(inplace=True)
            self.conv2 = DepthwiseSeparableConv(channels, channels, kernel_size=3, stride=1, padding=1, bias=False)
            self.bn2 = nn.BatchNorm2d(channels)

        def forward(self, x):
            identity = x
            
            out = self.conv1(x)
            out = self.bn1(out)
            out = self.selu(out)

            out = self.conv2(out)
            out = self.bn2(out)

            out += identity  # Skip connection
            out = self.selu(out)  # SELU after addition
            return out

    class MyNet(nn.Module):
        def __init__(self, inp_dim, mod_dim1, mod_dim2):
            super(MyNet, self).__init__()

            self.conv_initial = nn.Sequential(
                DepthwiseSeparableConv(inp_dim, mod_dim1, kernel_size=3, stride=1, padding=1, bias=False),
                nn.BatchNorm2d(mod_dim1),
                nn.SELU(inplace=True)
            )

            # Residual Block 1
            self.res_block1 = ResidualBlock(mod_dim1)

            # Residual Block 2
            self.res_block2 = ResidualBlock(mod_dim1)
            
            # Final convolution to change to mod_dim2
            self.conv_final = nn.Sequential(
                DepthwiseSeparableConv(mod_dim1, mod_dim2, kernel_size=1, stride=1, padding=0, bias=False),
                nn.BatchNorm2d(mod_dim2)
                # No SELU here, to match original network's final layer output behavior if it's intended to be linear
            )

        def forward(self, x):
            x = self.conv_initial(x)
            x = self.res_block1(x)
            x = self.res_block2(x)
            x = self.conv_final(x)
            return x

    # Set random seeds for reproducibility using the provided or default seed
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    # Set CUDA device if available
    if torch.cuda.is_available():
        torch.cuda.set_device(args.gpu_id)
        torch.cuda.manual_seed(args.seed)

    '''segmentation ML'''
    # Initialize seg_lab as an empty list by default
    seg_lab = []

    # Convert segmentation method to lowercase for case-insensitive comparison
    segmentation_method = args.segmentation_method.lower()

    if segmentation_method == 'felzenszwalb':
        # Perform Felzenszwalb segmentation
        # Adjusted parameters for finer segmentation:
        # - Lower scale for smaller segments
        # - Lower sigma for more edge sensitivity
        # - Smaller min_size to allow tiny segments
        seg_map = segmentation.felzenszwalb(image, scale=8, sigma=0.02, min_size=3)
        seg_map = seg_map.flatten()
        seg_lab = [np.where(seg_map == u_label)[0] for u_label in np.unique(seg_map)]

    elif segmentation_method == 'kmeans':
        # Perform KMeans clustering
        image_flatten = image.reshape((-1, 3))
        kmeans = KMeans(n_clusters=args.max_label_num, random_state=0).fit(image_flatten)
        seg_map = kmeans.labels_
        seg_lab = [np.where(seg_map == u_label)[0] for u_label in np.unique(seg_map)]

    elif segmentation_method == 'pca':
        # Perform PCA-based segmentation
        image_flatten = image.reshape((-1, 3))

        # Apply PCA to reduce dimensionality to 1 component for segmentation
        n_components = min(3, image_flatten.shape[1])  # Use at most 3 components
        pca = PCA(n_components=n_components)
        reduced_data = pca.fit_transform(image_flatten)

        # Apply KMeans on the PCA-reduced data
        kmeans = KMeans(n_clusters=args.max_label_num, random_state=0).fit(reduced_data)
        seg_map = kmeans.labels_
        seg_lab = [np.where(seg_map == u_label)[0] for u_label in np.unique(seg_map)]

    else:
        # Default to simple grid segmentation if method is not recognized
        print(f"Warning: Unrecognized segmentation method '{segmentation_method}'. Using simple grid segmentation.")
        h, w = image.shape[:2]
        grid_size = max(h, w) // 16  # Simple grid division
        seg_map = np.zeros((h, w), dtype=np.int32)

        # Create a simple grid segmentation
        for i in range(0, h, grid_size):
            for j in range(0, w, grid_size):
                seg_map[i:min(i+grid_size, h), j:min(j+grid_size, w)] = (i//grid_size) * (w//grid_size) + (j//grid_size)

        seg_map = seg_map.flatten()
        seg_lab = [np.where(seg_map == u_label)[0] for u_label in np.unique(seg_map)]

    # Prepare input tensor for PyTorch
    tensor = image.astype(np.float32) / 255.0
    tensor = np.transpose(tensor, (2, 0, 1))  # Convert from HWC to CHW format
    tensor = np.expand_dims(tensor, axis=0)  # Add batch dimension
    tensor = torch.from_numpy(tensor)  # Convert to PyTorch tensor

    # Set device to GPU if available, otherwise CPU
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    tensor = tensor.to(device)

    segmented_images = []

    # Initialize the model
    model = MyNet(inp_dim=3, mod_dim1=args.mod_dim1, mod_dim2=args.mod_dim2).to(device)
    optimizer = optim.SGD(model.parameters(), lr=5e-2, momentum=0.9)

    # Flatten image for color assignment
    image_flatten = image.reshape(-1, 3)
    color_avg = np.random.randint(255, size=(args.max_label_num, 3))
    show = image.copy()

    for batch_idx in range(args.train_epoch):
        if stop_event.is_set():
            print("Training stopped by user.")
            break  # Exit the training loop

        # Forward pass
        model.train()
        optimizer.zero_grad()
        output = model(tensor)

        # Reshape output and compute argmax
        output_reshaped = output[0].permute(1, 2, 0).reshape(-1, args.mod_dim2)
        target = torch.argmax(output_reshaped, dim=1)
        im_target = target.cpu().numpy()

        # Update target labels based on segmentation
        for inds in seg_lab:
            u_labels, hist = np.unique(im_target[inds], return_counts=True)
            if len(u_labels) > 0:
                im_target[inds] = u_labels[np.argmax(hist)]

        # Convert back to tensor for loss calculation
        target = torch.from_numpy(im_target).long().to(device)

        # Compute loss (CrossEntropyLoss expects raw logits)
        loss = F.cross_entropy(output_reshaped, target)

        # Backward pass and optimization
        loss.backward()
        optimizer.step()

        # Update segmented image
        un_label, lab_inverse = np.unique(im_target, return_inverse=True)
        if un_label.shape[0] < args.max_label_num:
            img_flatten = image_flatten.copy()
            if len(color_avg) != un_label.shape[0]:
                # Recompute color_avg based on current labels
                color_avg = [np.mean(img_flatten[im_target == label], axis=0, dtype=int) for label in un_label]
                color_avg = np.array(color_avg)
            for lab_id, color in enumerate(color_avg):
                img_flatten[lab_inverse == lab_id] = color
            show = img_flatten.reshape(image.shape)
            show = show.astype(np.uint8)  # Ensure proper image data type

        segmented_images.append(show.copy())

        # Update progress bar and display image
        if progress_callback:
            progress_callback(batch_idx + 1, show)

    return segmented_images