# src/gui/ui/image_lab_page_ui.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QScrollArea, QFrame, QSlider, QSpinBox, QDoubleSpinBox,
    QFormLayout, QComboBox, QTabWidget, QLineEdit)
from PySide6.QtCore import Qt, Signal

from src.widgets.scrollable_frame import ScrollableFrame
from src.widgets.pixmap_view import QPixmapView
from src.widgets.collapsible_section import CollapsibleSection
from src.widgets.page_image_gallery import PageImageGallery
from src.gui.widgets.interactive_histogram import InteractiveHistogram

class ImageLabGallery(PageImageGallery):
    """Image gallery specifically for the Image Lab Page."""

    def __init__(self, parent=None):
        super().__init__(parent)

    def clear_images(self):
        """Removes all images and thumbnails from the gallery."""
        self.clear()
        print("ImageLabGallery: All images cleared.")

class ImageLabPageUI(QWidget):
    """UI class for the Image Lab page."""

    # Define signals
    image_processed = Signal(object)  # Signal emitted when an image is processed

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """Sets up the Image Lab page UI."""
        main_layout = QHBoxLayout(self)

        # Left panel for controls
        self.control_panel = ScrollableFrame()
        self.control_panel.setFixedWidth(400)
        control_layout = QVBoxLayout(self.control_panel.get_content_frame())
        main_layout.addWidget(self.control_panel)

        # Image Gallery Group
        gallery_group = QGroupBox("Image Gallery")
        gallery_layout = QVBoxLayout(gallery_group)
        gallery_layout.setContentsMargins(5, 5, 5, 5)
        gallery_layout.setSpacing(2)

        # Set fixed height for the gallery group
        gallery_group.setFixedHeight(200)

        # Create the image lab gallery
        self.image_lab_gallery = ImageLabGallery()
        gallery_layout.addWidget(self.image_lab_gallery)

        control_layout.addWidget(gallery_group)

        # Basic Enhancement Section (Collapsible, expanded by default)
        self.enhance_section = CollapsibleSection("Basic Enhancement")
        self.enhance_section.set_expanded(True)
        enhance_widget = QWidget()
        enhance_layout = QVBoxLayout(enhance_widget)

        # Enhancement buttons
        enhance_buttons_layout = QVBoxLayout()
        self.hist_equal_btn = QPushButton("Histogram Equalization")
        enhance_buttons_layout.addWidget(self.hist_equal_btn)

        self.auto_contrast_btn = QPushButton("Auto Contrast")
        enhance_buttons_layout.addWidget(self.auto_contrast_btn)

        # Undo button for this section
        self.enhance_undo_btn = QPushButton("Undo Last")
        self.enhance_undo_btn.setToolTip("Undo the last enhancement operation")
        enhance_buttons_layout.addWidget(self.enhance_undo_btn)

        enhance_layout.addLayout(enhance_buttons_layout)

        # Brightness and Contrast controls
        brightness_contrast_form = QFormLayout()

        self.brightness_slider = QSlider(Qt.Horizontal)
        self.brightness_slider.setRange(-100, 100)
        self.brightness_slider.setValue(0)
        self.brightness_value = QLabel("0")
        brightness_layout = QHBoxLayout()
        brightness_layout.addWidget(self.brightness_slider)
        brightness_layout.addWidget(self.brightness_value)
        brightness_contrast_form.addRow("Brightness:", brightness_layout)

        self.contrast_slider = QSlider(Qt.Horizontal)
        self.contrast_slider.setRange(-100, 100)
        self.contrast_slider.setValue(0)
        self.contrast_value = QLabel("0")
        contrast_layout = QHBoxLayout()
        contrast_layout.addWidget(self.contrast_slider)
        contrast_layout.addWidget(self.contrast_value)
        brightness_contrast_form.addRow("Contrast:", contrast_layout)

        enhance_layout.addLayout(brightness_contrast_form)

        # Gamma correction
        gamma_layout = QHBoxLayout()
        self.gamma_spinbox = QDoubleSpinBox()
        self.gamma_spinbox.setRange(0.1, 10.0)
        self.gamma_spinbox.setSingleStep(0.1)
        self.gamma_spinbox.setValue(1.0)
        gamma_layout.addWidget(self.gamma_spinbox)
        self.apply_gamma_btn = QPushButton("Apply Gamma")
        gamma_layout.addWidget(self.apply_gamma_btn)
        enhance_layout.addLayout(gamma_layout)

        # Reset button
        self.reset_image_btn = QPushButton("Reset Image")
        enhance_layout.addWidget(self.reset_image_btn)

        # Add the widget to the section
        self.enhance_section.add_widget(enhance_widget)
        control_layout.addWidget(self.enhance_section)

        # Morphological Operations Section (Collapsible, collapsed by default)
        self.morph_section = CollapsibleSection("Morphological Operations")
        self.morph_section.set_expanded(False)
        morph_widget = QWidget()
        morph_layout = QVBoxLayout(morph_widget)

        # Morphological operation type
        morph_form = QFormLayout()
        self.morph_type = QComboBox()
        self.morph_type.addItems(["Erosion", "Dilation", "Opening", "Closing", "Gradient", "Top Hat", "Black Hat"])
        morph_form.addRow("Operation:", self.morph_type)

        # Kernel size
        self.kernel_size = QSpinBox()
        self.kernel_size.setRange(1, 21)
        self.kernel_size.setSingleStep(2)
        self.kernel_size.setValue(3)
        morph_form.addRow("Kernel Size:", self.kernel_size)

        # Iterations
        self.iterations = QSpinBox()
        self.iterations.setRange(1, 10)
        self.iterations.setValue(1)
        morph_form.addRow("Iterations:", self.iterations)

        morph_layout.addLayout(morph_form)

        # Buttons layout
        morph_buttons_layout = QVBoxLayout()

        # Apply button
        self.apply_morph_btn = QPushButton("Apply Morphological Operation")
        morph_buttons_layout.addWidget(self.apply_morph_btn)

        # Undo button for this section
        self.morph_undo_btn = QPushButton("Undo Last")
        self.morph_undo_btn.setToolTip("Undo the last morphological operation")
        morph_buttons_layout.addWidget(self.morph_undo_btn)

        morph_layout.addLayout(morph_buttons_layout)

        # Add the widget to the section
        self.morph_section.add_widget(morph_widget)
        control_layout.addWidget(self.morph_section)

        # Filters Section (Collapsible, collapsed by default)
        self.filter_section = CollapsibleSection("Filters")
        self.filter_section.set_expanded(False)
        filter_widget = QWidget()
        filter_layout = QVBoxLayout(filter_widget)

        # Filter type
        filter_form = QFormLayout()
        self.filter_type = QComboBox()
        self.filter_type.addItems(["Gaussian Blur", "Median Blur", "Bilateral Filter", "Box Filter", "Sharpen"])
        filter_form.addRow("Filter:", self.filter_type)

        # Filter size
        self.filter_size = QSpinBox()
        self.filter_size.setRange(1, 31)
        self.filter_size.setSingleStep(2)
        self.filter_size.setValue(5)
        filter_form.addRow("Size:", self.filter_size)

        filter_layout.addLayout(filter_form)

        # Buttons layout
        filter_buttons_layout = QVBoxLayout()

        # Apply button
        self.apply_filter_btn = QPushButton("Apply Filter")
        filter_buttons_layout.addWidget(self.apply_filter_btn)

        # Undo button for this section
        self.filter_undo_btn = QPushButton("Undo Last")
        self.filter_undo_btn.setToolTip("Undo the last filter operation")
        filter_buttons_layout.addWidget(self.filter_undo_btn)

        filter_layout.addLayout(filter_buttons_layout)

        # Add the widget to the section
        self.filter_section.add_widget(filter_widget)
        control_layout.addWidget(self.filter_section)

        # Thresholding Section (Collapsible, collapsed by default)
        self.threshold_section = CollapsibleSection("Thresholding")
        self.threshold_section.set_expanded(False)
        threshold_widget = QWidget()
        threshold_layout = QVBoxLayout(threshold_widget)

        # Threshold type
        threshold_form = QFormLayout()
        self.threshold_type = QComboBox()
        self.threshold_type.addItems([
            "Binary", "Binary Inverted", "Truncate", "To Zero", "To Zero Inverted",
            "Otsu", "Adaptive Mean", "Adaptive Gaussian"
        ])
        threshold_form.addRow("Method:", self.threshold_type)

        # Threshold value
        self.threshold_value = QSlider(Qt.Horizontal)
        self.threshold_value.setRange(0, 255)
        self.threshold_value.setValue(127)
        self.threshold_value_label = QLabel("127")
        threshold_value_layout = QHBoxLayout()
        threshold_value_layout.addWidget(self.threshold_value)
        threshold_value_layout.addWidget(self.threshold_value_label)
        threshold_form.addRow("Threshold:", threshold_value_layout)

        # Block size for adaptive methods
        self.block_size = QSpinBox()
        self.block_size.setRange(3, 51)
        self.block_size.setSingleStep(2)
        self.block_size.setValue(11)
        threshold_form.addRow("Block Size:", self.block_size)

        # C value for adaptive methods
        self.c_value = QSpinBox()
        self.c_value.setRange(-10, 10)
        self.c_value.setValue(2)
        threshold_form.addRow("C Value:", self.c_value)

        threshold_layout.addLayout(threshold_form)

        # Buttons layout
        threshold_buttons_layout = QVBoxLayout()

        # Apply button
        self.apply_threshold_btn = QPushButton("Apply Threshold")
        threshold_buttons_layout.addWidget(self.apply_threshold_btn)

        # Undo button for this section
        self.threshold_undo_btn = QPushButton("Undo Last")
        self.threshold_undo_btn.setToolTip("Undo the last threshold operation")
        threshold_buttons_layout.addWidget(self.threshold_undo_btn)

        threshold_layout.addLayout(threshold_buttons_layout)

        # Add the widget to the section
        self.threshold_section.add_widget(threshold_widget)
        control_layout.addWidget(self.threshold_section)

        # Color Manipulation Section (Collapsible, collapsed by default)
        self.color_section = CollapsibleSection("Color Manipulation")
        self.color_section.set_expanded(False)
        color_widget = QWidget()
        color_layout = QVBoxLayout(color_widget)

        # Color space conversion
        color_form = QFormLayout()
        self.color_space = QComboBox()
        self.color_space.addItems([
            "RGB to Grayscale", "RGB to HSV", "RGB to LAB",
            "HSV to RGB", "LAB to RGB"
        ])
        color_form.addRow("Convert:", self.color_space)

        # Channel operations
        self.channel_operation = QComboBox()
        self.channel_operation.addItems([
            "Extract Red Channel", "Extract Green Channel", "Extract Blue Channel",
            "Extract Hue Channel", "Extract Saturation Channel", "Extract Value Channel",
            "Swap Red-Blue", "Invert Colors"
        ])
        color_form.addRow("Channel Op:", self.channel_operation)

        color_layout.addLayout(color_form)

        # Buttons layout
        color_buttons_layout = QVBoxLayout()

        # Apply color space conversion button
        self.apply_color_space_btn = QPushButton("Apply Color Space Conversion")
        color_buttons_layout.addWidget(self.apply_color_space_btn)

        # Apply channel operation button
        self.apply_channel_op_btn = QPushButton("Apply Channel Operation")
        color_buttons_layout.addWidget(self.apply_channel_op_btn)

        # Undo button for this section
        self.color_undo_btn = QPushButton("Undo Last")
        self.color_undo_btn.setToolTip("Undo the last color operation")
        color_buttons_layout.addWidget(self.color_undo_btn)

        color_layout.addLayout(color_buttons_layout)

        # Add the widget to the section
        self.color_section.add_widget(color_widget)
        control_layout.addWidget(self.color_section)

        # Edge Detection Section (Collapsible, collapsed by default)
        self.edge_section = CollapsibleSection("Edge Detection")
        self.edge_section.set_expanded(False)
        edge_widget = QWidget()
        edge_layout = QVBoxLayout(edge_widget)

        # Edge detection method
        edge_form = QFormLayout()
        self.edge_method = QComboBox()
        self.edge_method.addItems([
            "Sobel", "Scharr", "Laplacian", "Canny"
        ])
        edge_form.addRow("Method:", self.edge_method)

        # Parameters for Canny
        self.canny_threshold1 = QSlider(Qt.Horizontal)
        self.canny_threshold1.setRange(0, 255)
        self.canny_threshold1.setValue(100)
        self.canny_threshold1_label = QLabel("100")
        canny_threshold1_layout = QHBoxLayout()
        canny_threshold1_layout.addWidget(self.canny_threshold1)
        canny_threshold1_layout.addWidget(self.canny_threshold1_label)
        edge_form.addRow("Threshold 1:", canny_threshold1_layout)

        self.canny_threshold2 = QSlider(Qt.Horizontal)
        self.canny_threshold2.setRange(0, 255)
        self.canny_threshold2.setValue(200)
        self.canny_threshold2_label = QLabel("200")
        canny_threshold2_layout = QHBoxLayout()
        canny_threshold2_layout.addWidget(self.canny_threshold2)
        canny_threshold2_layout.addWidget(self.canny_threshold2_label)
        edge_form.addRow("Threshold 2:", canny_threshold2_layout)

        edge_layout.addLayout(edge_form)

        # Buttons layout
        edge_buttons_layout = QVBoxLayout()

        # Apply button
        self.apply_edge_btn = QPushButton("Apply Edge Detection")
        edge_buttons_layout.addWidget(self.apply_edge_btn)

        # Undo button for this section
        self.edge_undo_btn = QPushButton("Undo Last")
        self.edge_undo_btn.setToolTip("Undo the last edge detection operation")
        edge_buttons_layout.addWidget(self.edge_undo_btn)

        edge_layout.addLayout(edge_buttons_layout)

        # Add the widget to the section
        self.edge_section.add_widget(edge_widget)
        control_layout.addWidget(self.edge_section)

        # Crop Section (Collapsible, collapsed by default)
        self.crop_section = CollapsibleSection("Crop & Save")
        self.crop_section.set_expanded(False)
        crop_widget = QWidget()
        crop_layout = QVBoxLayout(crop_widget)

        # Crop button
        self.crop_button = QPushButton("Crop Image")
        crop_layout.addWidget(self.crop_button)

        # Apply edits button
        self.apply_edits_button = QPushButton("Save Edited Image to Project")
        crop_layout.addWidget(self.apply_edits_button)

        # Undo button
        self.undo_button = QPushButton("Undo Last Operation")
        crop_layout.addWidget(self.undo_button)

        # Add the widget to the section
        self.crop_section.add_widget(crop_widget)
        control_layout.addWidget(self.crop_section)

        # Batch Processing Section (Collapsible, collapsed by default)
        self.batch_section = CollapsibleSection("Batch Processing")
        self.batch_section.set_expanded(False)
        batch_widget = QWidget()
        batch_layout = QVBoxLayout(batch_widget)

        # Template name input
        template_name_layout = QHBoxLayout()
        template_name_layout.addWidget(QLabel("Template Name:"))
        self.template_name_input = QLineEdit()
        self.template_name_input.setPlaceholderText("Enter template name")
        template_name_layout.addWidget(self.template_name_input)
        batch_layout.addLayout(template_name_layout)

        # Template buttons
        template_buttons_layout = QHBoxLayout()
        self.save_template_btn = QPushButton("Save Operations as Template")
        self.load_template_btn = QPushButton("Load Template")
        self.manage_templates_btn = QPushButton("Manage Templates")
        template_buttons_layout.addWidget(self.save_template_btn)
        template_buttons_layout.addWidget(self.load_template_btn)
        template_buttons_layout.addWidget(self.manage_templates_btn)
        batch_layout.addLayout(template_buttons_layout)

        # Template selection
        self.template_combo = QComboBox()
        self.template_combo.setPlaceholderText("Select saved template")
        batch_layout.addWidget(self.template_combo)

        # Apply buttons
        apply_buttons_layout = QHBoxLayout()
        self.apply_selected_btn = QPushButton("Apply to Selected")
        self.apply_all_btn = QPushButton("Apply to All Images")
        apply_buttons_layout.addWidget(self.apply_selected_btn)
        apply_buttons_layout.addWidget(self.apply_all_btn)
        batch_layout.addLayout(apply_buttons_layout)

        # Export button
        self.export_all_btn = QPushButton("Export All Processed Images")
        batch_layout.addWidget(self.export_all_btn)

        # Add the widget to the section
        self.batch_section.add_widget(batch_widget)
        control_layout.addWidget(self.batch_section)

        # Add stretch to push all controls to the top
        control_layout.addStretch()

        # Center panel for image display
        image_display_container = QWidget()
        image_display_layout = QVBoxLayout(image_display_container)
        image_display_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(image_display_container, stretch=1)

        # Use QTabWidget to switch between views
        self.image_tab_widget = QTabWidget()
        image_display_layout.addWidget(self.image_tab_widget)

        # Side-by-side view tab
        side_by_side_widget = QWidget()
        side_by_side_layout = QHBoxLayout(side_by_side_widget)
        side_by_side_layout.setContentsMargins(0, 0, 0, 0)

        # Original image view
        self.original_view = QPixmapView()
        side_by_side_layout.addWidget(self.original_view)

        # Processed image view
        self.processed_view = QPixmapView()
        side_by_side_layout.addWidget(self.processed_view)

        side_by_side_widget.setLayout(side_by_side_layout)
        self.image_tab_widget.addTab(side_by_side_widget, "Side by Side")

        # Full processed view tab
        self.full_processed_view = QPixmapView()
        self.image_tab_widget.addTab(self.full_processed_view, "Full Processed View")

        # Right panel for information
        info_panel = QWidget()
        info_panel.setFixedWidth(300)
        info_layout = QVBoxLayout(info_panel)
        main_layout.addWidget(info_panel)

        # Image information group
        image_info_group = QGroupBox("Image Information")
        image_info_layout = QVBoxLayout(image_info_group)

        self.image_info_label = QLabel("No image loaded")
        image_info_layout.addWidget(self.image_info_label)

        # Interactive histogram
        histogram_label = QLabel("Histogram:")
        image_info_layout.addWidget(histogram_label)

        self.histogram_widget = InteractiveHistogram()
        self.histogram_widget.setMinimumHeight(200)
        image_info_layout.addWidget(self.histogram_widget)

        info_layout.addWidget(image_info_group)
        info_layout.addStretch()
