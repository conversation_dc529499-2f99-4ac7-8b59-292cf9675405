"""
Markdown Text Edit Widget for VisionLab Ai.
This module provides a QTextEdit subclass that can render markdown text.
"""

import re
from PySide6.QtWidgets import QTextEdit
from PySide6.QtGui import QTextCursor, QTextCharFormat, QFont, QColor, QTextBlockFormat
from PySide6.QtCore import Qt

class MarkdownTextEdit(QTextEdit):
    """A QTextEdit subclass that can render markdown text."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setReadOnly(True)
        self.setStyleSheet("font-family: monospace;")
        
        # Define formats for different markdown elements
        self._header_formats = {
            1: self._create_header_format(24, True),
            2: self._create_header_format(20, True),
            3: self._create_header_format(16, True),
            4: self._create_header_format(14, True),
            5: self._create_header_format(12, True),
            6: self._create_header_format(12, False),
        }
        
        self._bold_format = self._create_format(weight=QFont.Weight.Bold)
        self._italic_format = self._create_format(italic=True)
        self._code_format = self._create_format(family="Courier New", background=QColor("#f0f0f0"))
        self._link_format = self._create_format(underline=True, color=QColor("blue"))
        
        # Block formats
        self._block_format = QTextBlockFormat()
        self._block_format.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        self._code_block_format = QTextBlockFormat()
        self._code_block_format.setBackground(QColor("#f0f0f0"))
        self._code_block_format.setLeftMargin(20)
        self._code_block_format.setRightMargin(20)
        
        self._quote_block_format = QTextBlockFormat()
        self._quote_block_format.setLeftMargin(20)
        self._quote_block_format.setBackground(QColor("#f5f5f5"))
        self._quote_block_format.setProperty(QTextBlockFormat.BlockIndent, 1)

    def _create_header_format(self, size, bold=False):
        """Create a format for headers."""
        format = QTextCharFormat()
        font = QFont()
        font.setPointSize(size)
        if bold:
            font.setWeight(QFont.Weight.Bold)
        format.setFont(font)
        return format
    
    def _create_format(self, family=None, size=None, weight=None, italic=False, 
                      underline=False, color=None, background=None):
        """Create a text format with the specified properties."""
        format = QTextCharFormat()
        font = format.font()
        
        if family:
            font.setFamily(family)
        if size:
            font.setPointSize(size)
        if weight:
            font.setWeight(weight)
        font.setItalic(italic)
        font.setUnderline(underline)
        
        format.setFont(font)
        
        if color:
            format.setForeground(color)
        if background:
            format.setBackground(background)
            
        return format

    def setMarkdown(self, text):
        """Set the markdown text and render it."""
        self.clear()
        if not text:
            return
            
        cursor = self.textCursor()
        cursor.beginEditBlock()
        
        # Process the text line by line
        lines = text.split('\n')
        i = 0
        in_code_block = False
        code_block_content = []
        
        while i < len(lines):
            line = lines[i]
            
            # Check for code blocks
            if line.strip().startswith('```'):
                in_code_block = not in_code_block
                
                # If we're ending a code block, render it
                if not in_code_block and code_block_content:
                    self._insert_code_block(cursor, code_block_content)
                    code_block_content = []
                
                i += 1
                continue
                
            # If we're in a code block, collect the content
            if in_code_block:
                code_block_content.append(line)
                i += 1
                continue
                
            # Headers
            header_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if header_match:
                level = len(header_match.group(1))
                text = header_match.group(2)
                self._insert_header(cursor, text, level)
                i += 1
                continue
                
            # Blockquotes
            if line.strip().startswith('>'):
                text = line.strip()[1:].strip()
                self._insert_blockquote(cursor, text)
                i += 1
                continue
                
            # Lists (simple implementation)
            list_match = re.match(r'^(\s*)([-*+]|\d+\.)\s+(.+)$', line)
            if list_match:
                indent = len(list_match.group(1))
                marker = list_match.group(2)
                text = list_match.group(3)
                self._insert_list_item(cursor, text, indent, marker)
                i += 1
                continue
                
            # Regular paragraph with inline formatting
            self._insert_paragraph(cursor, line)
            i += 1
            
        cursor.endEditBlock()
        self.setTextCursor(cursor)

    def _insert_header(self, cursor, text, level):
        """Insert a header with the appropriate format."""
        cursor.insertBlock()
        cursor.setBlockFormat(self._block_format)
        cursor.setCharFormat(self._header_formats.get(level, self._header_formats[1]))
        cursor.insertText(text)
        cursor.insertBlock()  # Add an empty line after header

    def _insert_paragraph(self, cursor, text):
        """Insert a paragraph with inline formatting."""
        cursor.insertBlock()
        cursor.setBlockFormat(self._block_format)
        
        # Process inline formatting
        i = 0
        while i < len(text):
            # Bold
            if i + 1 < len(text) and text[i:i+2] == '**' and '**' in text[i+2:]:
                end = text.find('**', i+2)
                cursor.setCharFormat(self._bold_format)
                cursor.insertText(text[i+2:end])
                cursor.setCharFormat(QTextCharFormat())
                i = end + 2
                continue
                
            # Italic
            if text[i] == '*' and '*' in text[i+1:]:
                end = text.find('*', i+1)
                cursor.setCharFormat(self._italic_format)
                cursor.insertText(text[i+1:end])
                cursor.setCharFormat(QTextCharFormat())
                i = end + 1
                continue
                
            # Inline code
            if text[i] == '`' and '`' in text[i+1:]:
                end = text.find('`', i+1)
                cursor.setCharFormat(self._code_format)
                cursor.insertText(text[i+1:end])
                cursor.setCharFormat(QTextCharFormat())
                i = end + 1
                continue
                
            # Links
            if text[i] == '[' and '](' in text[i:] and ')' in text[i:]:
                text_end = text.find('](', i)
                link_end = text.find(')', text_end)
                if text_end != -1 and link_end != -1:
                    link_text = text[i+1:text_end]
                    link_url = text[text_end+2:link_end]
                    cursor.setCharFormat(self._link_format)
                    cursor.insertText(link_text)
                    cursor.setCharFormat(QTextCharFormat())
                    i = link_end + 1
                    continue
            
            # Regular text
            cursor.insertText(text[i])
            i += 1

    def _insert_code_block(self, cursor, lines):
        """Insert a code block."""
        cursor.insertBlock()
        cursor.setBlockFormat(self._code_block_format)
        cursor.setCharFormat(self._code_format)
        
        for line in lines:
            cursor.insertText(line)
            cursor.insertBlock()
            cursor.setBlockFormat(self._code_block_format)
            
        cursor.setCharFormat(QTextCharFormat())
        cursor.setBlockFormat(self._block_format)

    def _insert_blockquote(self, cursor, text):
        """Insert a blockquote."""
        cursor.insertBlock()
        cursor.setBlockFormat(self._quote_block_format)
        self._insert_paragraph(cursor, text)
        cursor.setBlockFormat(self._block_format)

    def _insert_list_item(self, cursor, text, indent, marker):
        """Insert a list item."""
        cursor.insertBlock()
        
        block_format = QTextBlockFormat()
        block_format.setIndent(indent // 4 + 1)  # Simple indentation
        block_format.setLeftMargin(20 * (indent // 4 + 1))
        cursor.setBlockFormat(block_format)
        
        # Insert the marker
        cursor.insertText(f"{marker} ")
        
        # Insert the text with inline formatting
        self._insert_paragraph(cursor, text)
